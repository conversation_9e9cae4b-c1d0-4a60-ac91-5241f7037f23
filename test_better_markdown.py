#!/usr/bin/env python3
"""
Test better markdown conversion that preserves profile content
"""

import json
import re
from mistralai import Mistra<PERSON>
from nyc_boroughs_scraper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>craper
from bs4 import BeautifulSoup

def extract_better_markdown(html_content: str, url: str) -> str:
    """Extract markdown with better profile content preservation"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Remove only the most problematic elements (keep more content)
        for element in soup(['script', 'style', 'noscript']):
            element.decompose()
        
        # Try to find the main profile content area
        profile_content = None
        
        # Look for common profile content containers
        for selector in [
            'div.profile-content',
            'div.post-content', 
            'div.ad-content',
            'div.listing-content',
            'main',
            'article',
            '.content',
            '.profile',
            '.post'
        ]:
            profile_content = soup.select_one(selector)
            if profile_content:
                break
        
        # If no specific container found, use body but remove nav/footer
        if not profile_content:
            profile_content = soup.find('body')
            if profile_content:
                # Remove navigation and footer elements
                for element in profile_content(['nav', 'footer', 'header']):
                    element.decompose()
        
        # If still no content, use the whole soup
        if not profile_content:
            profile_content = soup
        
        # Extract text content
        text_content = profile_content.get_text()
        
        # Clean up text
        lines = []
        for line in text_content.split('\n'):
            line = line.strip()
            if line and len(line) > 2:  # Keep more content
                lines.append(line)
        
        # Join lines and limit length
        cleaned_content = '\n'.join(lines)
        
        # Don't truncate too aggressively - keep more content
        if len(cleaned_content) > 3000:
            cleaned_content = cleaned_content[:3000] + "\n[Content truncated...]"
        
        # Format as structured markdown
        structured_markdown = f"""# Profile Page
**URL:** {url}

## Content:
{cleaned_content}

---
"""
        
        return structured_markdown
        
    except Exception as e:
        print(f"Error in markdown conversion: {e}")
        return f"""# Profile Page
**URL:** {url}

## Content:
[Failed to extract content]

---
"""

def test_better_markdown():
    """Test the improved markdown conversion"""
    
    # Use confirmed working URL
    test_url = "https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/manhattanny/191403342"
    
    print("Testing Better Markdown Conversion")
    print("=" * 50)
    print(f"Test URL: {test_url}")
    
    # Get HTML
    scraper = NYCBoroughsScraper("TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G")
    html = scraper.execute_curl_request(test_url, scraper.dedicated_curl_template)
    
    if not html:
        print("❌ Failed to get HTML")
        return
    
    print(f"✓ Downloaded {len(html)} characters of HTML")
    
    # Convert with better method
    markdown = extract_better_markdown(html, test_url)
    
    print(f"✓ Converted to {len(markdown)} characters of markdown")
    print("\nMarkdown Preview (first 1000 chars):")
    print("-" * 50)
    print(markdown[:1000])
    print("-" * 50)
    
    # Test with Mistral
    print("\nTesting with Mistral...")
    
    client = Mistral(api_key="TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G")
    
    prompt = f"""
    You are extracting data from 1 escort profile page. Extract the following information:

    1. title: Profile title or headline
    2. name: Person's name
    3. age: Age (must be ≤30, skip if >30)
    4. phone: Phone number
    5. description: Profile description
    6. social_media: Social media handles/links (Instagram, Twitter, Snapchat, OnlyFans, etc.)
    7. email: Email address
    8. website: Website links
    9. posted_date: When the post was created
    10. post_id: Unique post identifier

    IMPORTANT: Only include profiles where age ≤30 AND gender is woman. Skip any profiles with age >30 or not women.

    Page content:

    {markdown}

    
    Return a JSON array with one object per valid page (only for women with age ≤30). Each object should have:
    {{
        "page_index": 0,
        "url": "{test_url}",
        "title": "<title>",
        "name": "<name>", 
        "age": "<age>",
        "phone": "<phone>",
        "description": "<description>",
        "social_media": "<social_media>",
        "email": "<email>",
        "website": "<website>",
        "posted_date": "<posted_date>",
        "post_id": "<post_id>"
    }}
    
    If any field is not found, use null. Only return valid JSON array.
    """
    
    try:
        response = client.chat.complete(
            model="mistral-large-latest",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=2000
        )
        
        result_text = response.choices[0].message.content.strip()
        
        print("=" * 60)
        print("MISTRAL RESPONSE:")
        print("=" * 60)
        print(result_text)
        print("=" * 60)
        
        # Try to parse
        try:
            # Try direct parsing first
            parsed_json = json.loads(result_text)
            print("✓ DIRECT JSON PARSING SUCCESSFUL!")
            print(f"Found {len(parsed_json)} profiles")
            
        except json.JSONDecodeError:
            # Try fallback parsing
            json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
            if json_match:
                try:
                    parsed_json = json.loads(json_match.group())
                    print("✓ FALLBACK JSON PARSING SUCCESSFUL!")
                    print(f"Found {len(parsed_json)} profiles")
                    
                except json.JSONDecodeError as e2:
                    print("✗ BOTH PARSING METHODS FAILED!")
                    print(f"Error: {e2}")
                    return
            else:
                print("✗ NO JSON FOUND IN RESPONSE!")
                return
        
        # Show results
        if isinstance(parsed_json, list) and len(parsed_json) > 0:
            print("\n🎯 SUCCESS! Found profile data:")
            for profile in parsed_json:
                print(f"  - Name: {profile.get('name', 'N/A')}")
                print(f"  - Age: {profile.get('age', 'N/A')}")
                print(f"  - Phone: {profile.get('phone', 'N/A')}")
                print(f"  - Social Media: {profile.get('social_media', 'N/A')}")
        else:
            print("❌ Still getting empty results")
        
    except Exception as e:
        print(f"❌ API call failed: {e}")

if __name__ == "__main__":
    test_better_markdown()
