#!/usr/bin/env python3
"""
Test script for the comprehensive data extractor
"""

import json
import os
from comprehensive_data_extractor import ProfileDataExtractor, load_urls_from_json

def test_single_url():
    """Test extraction from a single URL"""
    print("Testing single URL extraction...")

    # Create test extractor
    extractor = ProfileDataExtractor(delay=0.5, max_workers=1)

    # Test URL (using one from your structure.html)
    test_url = "https://escortbabylon.net/post/escorts/usa/california/columbia/188853860"

    result = extractor.extract_profile_data(test_url, city="Columbia", state="California")

    print(f"Result for {test_url}:")
    print(f"  Status: {result['status']}")
    print(f"  Phone: {result['phone_number']}")
    print(f"  Name: {result['name']}")
    print(f"  Age: {result['age']}")
    print(f"  Location: {result['location']}")
    print(f"  Social Media: {result['social_media']}")
    print(f"  Raw Text (first 100 chars): {result['raw_text'][:100] if result['raw_text'] else 'None'}...")
    print(f"  Error: {result['error']}")

    return result

def test_json_loading():
    """Test loading URLs from JSON file"""
    print("\nTesting JSON loading...")

    # Find JSON files in the directory
    json_files = [f for f in os.listdir('.') if f.endswith('.json') and 'urls' in f.lower()]

    if not json_files:
        print("No JSON files found with 'urls' in the name")
        return

    # Test with the first JSON file found
    test_file = json_files[0]
    print(f"Testing with: {test_file}")

    try:
        urls_data = load_urls_from_json(test_file)
        print(f"Loaded {len(urls_data)} URLs")

        if urls_data:
            print("Sample URL data:")
            sample = urls_data[0]
            for key, value in sample.items():
                print(f"  {key}: {value}")

        return urls_data[:3]  # Return first 3 for testing

    except Exception as e:
        print(f"Error loading JSON: {e}")
        return []

def test_batch_processing():
    """Test processing multiple URLs"""
    print("\nTesting batch processing...")

    urls_data = test_json_loading()
    if not urls_data:
        print("No URLs to test with")
        return

    # Create extractor
    extractor = ProfileDataExtractor(delay=0.5, max_workers=2)

    # Process the URLs
    results = extractor.process_urls(urls_data)

    print(f"\nBatch processing results:")
    print(f"Total processed: {len(results)}")

    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] == 'failed']

    print(f"Successful: {len(successful)}")
    print(f"Failed: {len(failed)}")

    if successful:
        print("\nSuccessful extractions:")
        for i, result in enumerate(successful, 1):
            print(f"  {i}. Phone: {result.get('phone_number', 'N/A')}, "
                  f"Name: {result.get('name', 'N/A')}, "
                  f"Age: {result.get('age', 'N/A')}")

    if failed:
        print("\nFailed extractions:")
        for i, result in enumerate(failed[:3], 1):  # Show first 3 failures
            print(f"  {i}. URL: {result['url']}")
            print(f"     Error: {result['error']}")

    # Save test results
    if results:
        extractor.save_to_excel(results, 'test_extraction_results.xlsx')
        print(f"\nTest results saved to: test_extraction_results.xlsx")

    return results

def create_sample_json():
    """Create a sample JSON file for testing if none exist"""
    print("Creating sample JSON file for testing...")

    sample_data = [
        {
            "url": "https://escortbabylon.net/post/escorts/usa/california/columbia/188853860",
            "city": "Columbia",
            "state": "California",
            "estimated_age": 28,
            "source": "escortbabylon"
        },
        {
            "url": "https://aaok.com.listcrawler.eu/post/escorts/usa/maryland/baltimore/191781472",
            "city": "Baltimore",
            "state": "Maryland",
            "estimated_age": 27,
            "source": "aaok"
        }
    ]

    with open('test_urls.json', 'w') as f:
        json.dump(sample_data, f, indent=2)

    print("Created test_urls.json with sample data")
    return sample_data

def main():
    """Run all tests"""
    print("=== Comprehensive Data Extractor Test ===\n")

    # Test 1: Single URL
    try:
        test_single_url()
    except Exception as e:
        print(f"Single URL test failed: {e}")

    # Test 2: JSON Loading
    try:
        urls_data = test_json_loading()
        if not urls_data:
            print("No JSON data found, creating sample...")
            create_sample_json()
            urls_data = load_urls_from_json('test_urls.json')
    except Exception as e:
        print(f"JSON loading test failed: {e}")
        print("Creating sample JSON...")
        create_sample_json()

    # Test 3: Batch Processing
    try:
        test_batch_processing()
    except Exception as e:
        print(f"Batch processing test failed: {e}")

    print("\n=== Test Complete ===")

if __name__ == '__main__':
    main()
