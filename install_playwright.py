#!/usr/bin/env python3
"""
Installation script for Playwright Enhanced Scraper
This script installs all required dependencies and sets up Playwright browsers
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔧 {description}")
    print(f"Running: {command}")

    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        print("✅ Success!")
        if result.stdout.strip():
            print(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed with return code {e.returncode}")
        if e.stdout:
            print(f"Stdout: {e.stdout}")
        if e.stderr:
            print(f"Stderr: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def check_python_version():
    """Check if Python version is suitable"""
    print("🐍 Checking Python version...")

    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")

    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required for Playwright")
        return False

    print("✅ Python version is compatible")
    return True

def install_pip_packages():
    """Install required Python packages"""
    print("\n📦 Installing Python packages...")

    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False

    # Install packages from requirements.txt if it exists
    requirements_file = Path("requirements.txt")
    if requirements_file.exists():
        if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing packages from requirements.txt"):
            return False
    else:
        # Install packages individually
        packages = [
            "beautifulsoup4==4.12.2",
            "pandas==2.1.4",
            "openpyxl==3.1.2",
            "requests==2.31.0",
            "lxml==4.9.3",
            "mistralai==0.4.2",
            "playwright==1.40.0",
            "playwright-stealth==1.0.6"
        ]

        for package in packages:
            if not run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}"):
                return False

    return True

def install_playwright_browsers():
    """Install Playwright browser binaries"""
    print("\n🌐 Installing Playwright browsers...")

    # Install all browsers (chromium, firefox, webkit)
    if not run_command(f"{sys.executable} -m playwright install", "Installing all Playwright browsers"):
        return False

    # Install system dependencies (Linux/macOS)
    if sys.platform.startswith('linux'):
        print("🐧 Installing system dependencies for Linux...")
        if not run_command(f"{sys.executable} -m playwright install-deps", "Installing system dependencies"):
            print("⚠️  System dependencies installation failed. You may need to run with sudo or install manually.")

    return True

def verify_installation():
    """Verify that Playwright is installed correctly"""
    print("\n🔍 Verifying Playwright installation...")

    try:
        # Try importing playwright
        import playwright
        print(f"✅ Playwright imported successfully (version: {playwright.__version__})")

        # Try importing playwright-stealth
        import playwright_stealth
        print("✅ Playwright-stealth imported successfully")

        # Test basic playwright functionality
        from playwright.sync_api import sync_playwright

        print("🧪 Testing Playwright browser launch...")
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            page = browser.new_page()
            page.goto("https://httpbin.org/get")
            content = page.content()
            browser.close()

            if len(content) > 100:
                print("✅ Playwright browser test successful")
                return True
            else:
                print("❌ Playwright browser test failed - got empty content")
                return False

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def create_test_script():
    """Create a simple test script to verify everything works"""
    print("\n📝 Creating test script...")

    test_script_content = '''#!/usr/bin/env python3
"""
Quick test script for Playwright setup
"""

import asyncio
from playwright.async_api import async_playwright

async def test_playwright():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()

        print("🌐 Testing basic page navigation...")
        await page.goto("https://httpbin.org/get")
        title = await page.title()

        print(f"✅ Successfully loaded page with title: {title}")

        await browser.close()
        print("🎉 Playwright is working correctly!")

if __name__ == "__main__":
    asyncio.run(test_playwright())
'''

    try:
        with open("test_playwright_setup.py", "w") as f:
            f.write(test_script_content)
        print("✅ Created test_playwright_setup.py")
        return True
    except Exception as e:
        print(f"❌ Failed to create test script: {e}")
        return False

def main():
    """Main installation process"""
    print("🚀 Playwright Enhanced Scraper Installation")
    print("=" * 60)
    print("This script will install all required dependencies for the Playwright Enhanced Scraper")
    print("Including Python packages and Playwright browser binaries")
    print("=" * 60)

    # Check if we're in the right directory
    if not os.path.exists("nyc_boroughs_scraper.py"):
        print("⚠️  Warning: nyc_boroughs_scraper.py not found in current directory")
        print("Make sure you're running this script from the project root directory")

    steps = [
        ("Checking Python version", check_python_version),
        ("Installing Python packages", install_pip_packages),
        ("Installing Playwright browsers", install_playwright_browsers),
        ("Verifying installation", verify_installation),
        ("Creating test script", create_test_script)
    ]

    failed_steps = []

    for step_name, step_function in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")

        if not step_function():
            failed_steps.append(step_name)
            print(f"❌ {step_name} failed!")
        else:
            print(f"✅ {step_name} completed successfully!")

    # Summary
    print("\n" + "=" * 60)
    print("📋 INSTALLATION SUMMARY")
    print("=" * 60)

    if not failed_steps:
        print("🎉 Installation completed successfully!")
        print("\nNext steps:")
        print("1. Test the setup: python test_playwright_setup.py")
        print("2. Run the Playwright scraper: python playwright_enhanced_scraper.py --max-urls 10")
        print("3. Compare with curl-based approach: python test_playwright_scraper.py")

        return True
    else:
        print("❌ Installation completed with errors!")
        print("\nFailed steps:")
        for step in failed_steps:
            print(f"  - {step}")

        print("\nTroubleshooting:")
        print("1. Make sure you have Python 3.8+ installed")
        print("2. Try running with administrator/sudo privileges")
        print("3. Check your internet connection")
        print("4. Install missing system dependencies manually")

        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
