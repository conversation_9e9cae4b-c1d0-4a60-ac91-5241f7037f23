#!/usr/bin/env python3
"""
Comprehensive Test Script for OptimizedScraperAPI v2
Tests all the outstanding issues mentioned in conversation summary:
- ScraperAPI integration with Cloudflare bypass
- Fixed Mistral AI import and functionality
- Phone number deduplication
- Generic state/city support
- API endpoint validation
"""

import sys
import time
import json
from datetime import datetime
from optimized_scraper_api_v2 import OptimizedScraperAPIv2

def test_api_keys():
    """Test 1: Verify API keys are working"""
    print("🔧 TEST 1: API Keys Validation")
    print("-" * 50)

    scraper = OptimizedScraperAPIv2()

    # Test ScraperAPI key
    test_url = "https://aaok.com.listcrawler.eu/brief/escorts/usa/alabama/auburn/1"
    html = scraper.fetch_with_scraperapi(test_url)

    if html and len(html) > 1000:
        print("✅ ScraperAPI key working - bypassing Cloudflare protection")
        print(f"   Response length: {len(html)} characters")
    else:
        print("❌ ScraperAPI key failed")
        return False

    # Test Mistral AI
    if scraper.mistral_client:
        print("✅ Mistral AI client initialized successfully")
        try:
            # Quick test
            from mistralai.models.chat_completion import ChatMessage
            messages = [ChatMessage(role="user", content="Hello")]
            response = scraper.mistral_client.chat(
                model="mistral-large-latest",
                messages=messages,
                max_tokens=10
            )
            if response:
                print("✅ Mistral AI API working")
            else:
                print("⚠️ Mistral AI API issue")
        except Exception as e:
            print(f"⚠️ Mistral AI test error: {e}")
    else:
        print("❌ Mistral AI not available")

    return True

def test_phone_deduplication():
    """Test 2: Phone number deduplication functionality"""
    print("\n📱 TEST 2: Phone Number Deduplication")
    print("-" * 50)

    scraper = OptimizedScraperAPIv2()

    # Test phone cleaning
    test_phones = [
        "************",
        "(*************",
        "15109574071",
        "5109574071",
        "************"
    ]

    cleaned_phones = [scraper.clean_phone_number(phone) for phone in test_phones]
    unique_cleaned = set(filter(None, cleaned_phones))

    if len(unique_cleaned) == 1 and "5109574071" in unique_cleaned:
        print("✅ Phone number cleaning working correctly")
        print(f"   All variations normalized to: {list(unique_cleaned)[0]}")
    else:
        print("❌ Phone number cleaning failed")
        print(f"   Results: {unique_cleaned}")
        return False

    # Test deduplication
    test_phone = "5109574071"

    # First add should work
    is_duplicate_1 = scraper.is_phone_duplicate(test_phone)
    scraper.add_phone_to_database(test_phone, "http://test1.com", {"test": "data1"})

    # Second add should be detected as duplicate
    is_duplicate_2 = scraper.is_phone_duplicate(test_phone)
    scraper.add_phone_to_database(test_phone, "http://test2.com", {"test": "data2"})

    if not is_duplicate_1 and is_duplicate_2:
        print("✅ Phone deduplication working correctly")
        print(f"   Duplicate count: {scraper.duplicate_count}")
        print(f"   Known phones: {len(scraper.known_phones)}")
    else:
        print("❌ Phone deduplication failed")
        return False

    return True

def test_url_extraction():
    """Test 3: URL extraction from search pages (age ≤30 filter)"""
    print("\n🔗 TEST 3: URL Extraction with Age Filtering")
    print("-" * 50)

    scraper = OptimizedScraperAPIv2()

    # Test both sources
    test_urls = [
        ("aaok", "https://aaok.com.listcrawler.eu/brief/escorts/usa/alabama/auburn/1"),
        ("aypapi", "https://aypapi.com.listcrawler.eu/brief/escorts/usa/alabama/auburn/1")
    ]

    for source, url in test_urls:
        print(f"\nTesting {source.upper()} source...")

        # Fetch search page
        html = scraper.fetch_with_scraperapi(url)
        if not html:
            print(f"❌ Failed to fetch {source} search page")
            continue

        # Extract URLs
        urls = scraper.extract_dedicated_urls_from_search(html, source)

        if urls:
            print(f"✅ {source}: Found {len(urls)} URLs")
            print(f"   Sample URL: {urls[0]}")
        else:
            print(f"❌ {source}: No URLs extracted")
            return False

    return True

def test_profile_extraction():
    """Test 4: Profile data extraction with Mistral AI"""
    print("\n👤 TEST 4: Profile Data Extraction")
    print("-" * 50)

    scraper = OptimizedScraperAPIv2()

    # Get a test URL from search results
    search_url = "https://aaok.com.listcrawler.eu/brief/escorts/usa/alabama/auburn/1"
    search_html = scraper.fetch_with_scraperapi(search_url)

    if not search_html:
        print("❌ Failed to get search page for testing")
        return False

    # Extract URLs
    urls = scraper.extract_dedicated_urls_from_search(search_html, "aaok")

    if not urls:
        print("❌ No URLs found for profile testing")
        return False

    # Test first profile
    test_url = urls[0]
    print(f"Testing profile: {test_url}")

    # Create URL data structure
    url_data = {
        'url': test_url,
        'city': 'Auburn',
        'state': 'Alabama',
        'source': 'aaok'
    }

    # Process URL
    profile_data = scraper.process_single_url(url_data)

    if profile_data:
        print("✅ Profile extraction successful")
        print(f"   Name: {profile_data.get('name', 'N/A')}")
        print(f"   Age: {profile_data.get('age', 'N/A')}")
        print(f"   Phone: {profile_data.get('phone', 'N/A')}")
        print(f"   City: {profile_data.get('city', 'N/A')}")
        print(f"   Source: {profile_data.get('source', 'N/A')}")

        # Test age filter
        if profile_data.get('age') and profile_data['age'] > 30:
            print("❌ Age filter not working (age > 30)")
            return False
        else:
            print("✅ Age filter working (age ≤ 30)")

    else:
        print("⚠️ No profile data extracted (could be male/over 30/duplicate)")

    return True

def test_cloudflare_bypass():
    """Test 5: Cloudflare bypass capability"""
    print("\n🛡️ TEST 5: Cloudflare Bypass Test")
    print("-" * 50)

    scraper = OptimizedScraperAPIv2()

    # Test direct request (should fail with Cloudflare)
    import requests
    try:
        direct_response = requests.get("https://aaok.com.listcrawler.eu/brief/escorts/usa/alabama/auburn/1", timeout=10)
        direct_success = direct_response.status_code == 200 and len(direct_response.text) > 10000
    except:
        direct_success = False

    # Test ScraperAPI request (should succeed)
    scraperapi_html = scraper.fetch_with_scraperapi("https://aaok.com.listcrawler.eu/brief/escorts/usa/alabama/auburn/1")
    scraperapi_success = scraperapi_html and len(scraperapi_html) > 10000

    print(f"Direct request: {'✅ Success' if direct_success else '❌ Failed (Expected with Cloudflare)'}")
    print(f"ScraperAPI request: {'✅ Success' if scraperapi_success else '❌ Failed'}")

    if scraperapi_success:
        print("✅ Cloudflare bypass working via ScraperAPI")
        return True
    else:
        print("❌ Cloudflare bypass failed")
        return False

def test_integration():
    """Test 6: Full integration test"""
    print("\n🚀 TEST 6: Full Integration Test")
    print("-" * 50)

    scraper = OptimizedScraperAPIv2(max_workers=2)

    print("Testing city URL scraping...")
    url_list = scraper.scrape_city_urls("Alabama", "Auburn")

    if url_list:
        print(f"✅ Found {len(url_list)} URLs for Auburn, Alabama")

        # Process first few URLs
        test_urls = url_list[:3] if len(url_list) >= 3 else url_list
        results = []

        for url_data in test_urls:
            result = scraper.process_single_url(url_data)
            if result:
                results.append(result)

        if results:
            print(f"✅ Successfully processed {len(results)} profiles")

            # Save test results
            filename = f"test_integration_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            scraper.save_results(results, filename)
            print(f"✅ Results saved to {filename}")

        else:
            print("⚠️ No valid profiles extracted (could be duplicates/males/over 30)")

        return True
    else:
        print("❌ No URLs found")
        return False

def run_all_tests():
    """Run all tests and report results"""
    print("🧪 OPTIMIZED SCRAPER API V2 - COMPREHENSIVE TESTING")
    print("=" * 60)
    print("Testing all outstanding issues from conversation summary:")
    print("- ScraperAPI integration with Cloudflare bypass")
    print("- Fixed Mistral AI import and integration")
    print("- Enhanced phone number deduplication")
    print("- Generic state/city support")
    print("- API endpoint validation")
    print("=" * 60)

    tests = [
        ("API Keys Validation", test_api_keys),
        ("Phone Deduplication", test_phone_deduplication),
        ("URL Extraction", test_url_extraction),
        ("Profile Extraction", test_profile_extraction),
        ("Cloudflare Bypass", test_cloudflare_bypass),
        ("Full Integration", test_integration)
    ]

    results = []

    for test_name, test_func in tests:
        try:
            start_time = time.time()
            success = test_func()
            end_time = time.time()

            results.append({
                'test': test_name,
                'status': 'PASS' if success else 'FAIL',
                'time': f"{end_time - start_time:.2f}s"
            })
        except Exception as e:
            print(f"\n❌ {test_name} failed with exception: {e}")
            results.append({
                'test': test_name,
                'status': 'ERROR',
                'time': 'N/A'
            })

    # Print summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)

    passed = 0
    total = len(results)

    for result in results:
        status_icon = "✅" if result['status'] == 'PASS' else "❌"
        print(f"{status_icon} {result['test']:<25} {result['status']:<6} ({result['time']})")
        if result['status'] == 'PASS':
            passed += 1

    print("-" * 60)
    print(f"OVERALL: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL TESTS PASSED! All outstanding issues addressed.")
        print("\nReady for production use:")
        print("- python optimized_scraper_api_v2.py Alabama")
        print("- python optimized_scraper_api_v2.py Alabama --city Auburn")
        print("- python optimized_scraper_api_v2.py --list-states")
    else:
        print("⚠️ Some tests failed. Please review and fix issues.")

    print("=" * 60)
    return passed == total

def main():
    """Main test execution"""
    try:
        success = run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n🛑 Testing interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Testing failed with error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
