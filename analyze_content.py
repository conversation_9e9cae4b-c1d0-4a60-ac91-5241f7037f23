#!/usr/bin/env python3
"""
Content Analysis Script
Analyzes the HTML content fetched by both curl and Playwright methods
to understand why data extraction is failing
"""

import json
import os
import asyncio
import time
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async
from bs4 import BeautifulSoup
from nyc_boroughs_scraper import NYCBoroughsScraper

async def analyze_content():
    """Analyze content from both methods to understand extraction issues"""

    print("🔍 Content Analysis - Understanding Extraction Issues")
    print("=" * 60)

    # Load test URL
    urls_file = "parallel_nyc_all_urls_deduplicated.json"
    test_url = None

    if os.path.exists(urls_file):
        with open(urls_file, 'r') as f:
            url_data = json.load(f)

        # Extract first URL
        for borough_source, data in url_data.items():
            if 'urls' in data and data['urls']:
                test_url = data['urls'][0]
                print(f"Analyzing URL from {borough_source}:")
                print(f"{test_url}")
                break

    if not test_url:
        print("❌ No test URL available")
        return False

    # Get content using CURL method
    print(f"\n📡 Fetching content with CURL...")
    try:
        scraper = NYCBoroughsScraper("dummy_key")
        curl_html = scraper.execute_curl_request(test_url, scraper.dedicated_curl_template)
        print(f"✅ CURL fetched {len(curl_html)} characters")
    except Exception as e:
        print(f"❌ CURL failed: {e}")
        curl_html = None

    # Get content using Playwright method
    print(f"\n📡 Fetching content with PLAYWRIGHT...")
    playwright_html = None

    try:
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context(
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = await context.new_page()
        await stealth_async(page)

        response = await page.goto(test_url, wait_until='domcontentloaded', timeout=30000)
        if response and response.status == 200:
            await page.wait_for_timeout(2000)
            playwright_html = await page.content()
            print(f"✅ PLAYWRIGHT fetched {len(playwright_html)} characters")

        await browser.close()
        await playwright.stop()

    except Exception as e:
        print(f"❌ PLAYWRIGHT failed: {e}")
        playwright_html = None

    # Analyze both contents
    if curl_html:
        print(f"\n🔧 ANALYZING CURL CONTENT")
        print("-" * 40)
        analyze_html_content(curl_html, "CURL")

    if playwright_html:
        print(f"\n🎭 ANALYZING PLAYWRIGHT CONTENT")
        print("-" * 40)
        analyze_html_content(playwright_html, "PLAYWRIGHT")

    # Compare contents
    if curl_html and playwright_html:
        print(f"\n📊 CONTENT COMPARISON")
        print("-" * 40)

        # Check for anti-bot protection
        curl_protection = check_antibot_protection(curl_html)
        playwright_protection = check_antibot_protection(playwright_html)

        print(f"CURL anti-bot detected:       {curl_protection}")
        print(f"PLAYWRIGHT anti-bot detected: {playwright_protection}")

        # Find differences
        curl_unique = len(set(curl_html.split()) - set(playwright_html.split()))
        playwright_unique = len(set(playwright_html.split()) - set(curl_html.split()))

        print(f"Words unique to CURL:       {curl_unique}")
        print(f"Words unique to PLAYWRIGHT: {playwright_unique}")

    return True

def analyze_html_content(html_content, method_name):
    """Analyze HTML content to understand structure and extraction issues"""

    if not html_content:
        print(f"❌ No content to analyze for {method_name}")
        return

    soup = BeautifulSoup(html_content, 'html.parser')

    print(f"Content length: {len(html_content)} characters")
    print(f"Title: {soup.title.get_text() if soup.title else 'No title'}")

    # Check for common anti-bot indicators
    antibot_indicators = [
        "Just a moment",
        "Enable JavaScript and cookies",
        "Checking your browser",
        "Please wait while we verify",
        "Ray ID:",
        "Cloudflare"
    ]

    found_indicators = [ind for ind in antibot_indicators if ind in html_content]
    if found_indicators:
        print(f"🚨 Anti-bot indicators found: {found_indicators}")
    else:
        print("✅ No obvious anti-bot protection detected")

    # Look for profile-specific elements
    print(f"\n🔍 PROFILE ELEMENTS ANALYSIS:")

    # Age elements
    age_elements = soup.find_all('div', class_='titleAge')
    print(f"Age elements (.titleAge): {len(age_elements)}")
    if age_elements:
        for i, elem in enumerate(age_elements[:3]):
            print(f"  Age {i+1}: '{elem.get_text(strip=True)}'")

    # Alternative age patterns
    age_patterns = soup.find_all(text=lambda text: text and any(word in text.lower() for word in ['age', 'years old', 'yo']))
    print(f"Text mentioning age: {len(age_patterns)}")
    if age_patterns:
        for i, pattern in enumerate(age_patterns[:3]):
            clean_text = ' '.join(pattern.split())
            print(f"  Age text {i+1}: '{clean_text[:50]}...'")

    # Title/name elements
    h1_elements = soup.find_all('h1')
    print(f"H1 elements: {len(h1_elements)}")
    if h1_elements:
        for i, elem in enumerate(h1_elements[:3]):
            print(f"  H1 {i+1}: '{elem.get_text(strip=True)[:50]}...'")

    # Phone elements
    phone_elements = soup.find_all(text=lambda text: text and any(char.isdigit() for char in text) and len([c for c in text if c.isdigit()]) >= 10)
    print(f"Potential phone numbers: {len(phone_elements)}")
    if phone_elements:
        for i, elem in enumerate(phone_elements[:3]):
            clean_text = ' '.join(elem.split())
            print(f"  Phone {i+1}: '{clean_text[:30]}...'")

    # Check for common class names that might contain data
    common_classes = ['profile', 'title', 'name', 'age', 'phone', 'description', 'info', 'details']
    print(f"\n🏷️  ELEMENT CLASSES:")
    for class_name in common_classes:
        elements = soup.find_all(class_=lambda x: x and class_name in x.lower())
        if elements:
            print(f"  .{class_name}*: {len(elements)} elements")
            for i, elem in enumerate(elements[:2]):
                classes = ' '.join(elem.get('class', []))
                text = elem.get_text(strip=True)[:40]
                print(f"    {i+1}. class='{classes}' text='{text}...'")

    # Check page structure
    print(f"\n📋 PAGE STRUCTURE:")
    print(f"  Total divs: {len(soup.find_all('div'))}")
    print(f"  Total spans: {len(soup.find_all('span'))}")
    print(f"  Total links: {len(soup.find_all('a'))}")
    print(f"  Total images: {len(soup.find_all('img'))}")

    # Show a sample of the raw content for manual inspection
    print(f"\n📝 CONTENT SAMPLE (first 500 chars):")
    print("-" * 20)
    clean_text = BeautifulSoup(html_content, 'html.parser').get_text()
    sample = ' '.join(clean_text.split())[:500]
    print(sample)
    print("-" * 20)

def check_antibot_protection(html_content):
    """Check if content shows signs of anti-bot protection"""
    protection_indicators = [
        "Just a moment",
        "Enable JavaScript and cookies",
        "Checking your browser",
        "Please wait while we verify",
        "Ray ID:",
        "Cloudflare",
        "DDoS protection by",
        "Browser integrity check"
    ]

    return any(indicator in html_content for indicator in protection_indicators)

async def main():
    try:
        await analyze_content()
        print(f"\n✅ Content analysis completed!")
        print(f"\n💡 Next steps:")
        print(f"1. Check if anti-bot protection is the real issue")
        print(f"2. Update CSS selectors based on actual page structure")
        print(f"3. Test with different extraction patterns")
        return True
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
