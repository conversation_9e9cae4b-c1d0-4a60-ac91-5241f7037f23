#!/usr/bin/env python3
"""
Runner Script for Complete Two-Phase Scraper

This script provides an easy interface to run the complete two-phase scraper
with various options and configurations.

Usage Examples:
    # Run complete workflow (Phase 1 + Phase 2) for all default cities
    python run_complete_scraper.py

    # Run only specific cities
    python run_complete_scraper.py --cities "Baltimore,Philadelphia,Pittsburgh"

    # Run with custom settings
    python run_complete_scraper.py --max-pages 20 --delay 1.5 --workers 3

    # Run Phase 2 only using existing URL file
    python run_complete_scraper.py --phase2-only

    # Run Phase 2 only with specific JSON file
    python run_complete_scraper.py --phase2-only --existing-json fresh_all_urls_20250818_234554.json

    # Quick test run (limited pages and URLs)
    python run_complete_scraper.py --test-mode
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from complete_two_phase_scraper import CompleteTwoPhaseScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def show_available_cities():
    """Show available cities for scraping"""
    cities = [
        "South New Jersey (New Jersey)",
        "Philadelphia (Pennsylvania)",
        "Pittsburgh (Pennsylvania)",
        "Wilmington (Delaware)",
        "Dover (Delaware)",
        "Baltimore (Maryland)",
        "Annapolis (Maryland)"
    ]

    print("\n📍 Available Cities:")
    for i, city in enumerate(cities, 1):
        print(f"  {i}. {city}")
    print()

def show_existing_files():
    """Show existing URL files that can be used for Phase 2"""
    print("\n📁 Available URL Files for Phase 2:")

    # Look for various URL file patterns
    patterns = ['phase1_all_urls_', 'fresh_all_urls_', 'phase2_batch_']
    found_files = []

    for file in os.listdir('.'):
        if file.endswith('.json') and any(pattern in file for pattern in patterns):
            try:
                # Get file size and modification time
                size_mb = os.path.getsize(file) / (1024 * 1024)
                mtime = datetime.fromtimestamp(os.path.getmtime(file))

                # Count URLs in file
                try:
                    with open(file, 'r') as f:
                        data = json.load(f)
                        url_count = len(data) if isinstance(data, list) else 0
                except:
                    url_count = "Unknown"

                found_files.append({
                    'name': file,
                    'size_mb': size_mb,
                    'mtime': mtime,
                    'url_count': url_count
                })
            except:
                continue

    if not found_files:
        print("  No URL files found.")
        return []

    # Sort by modification time (newest first)
    found_files.sort(key=lambda x: x['mtime'], reverse=True)

    for i, file_info in enumerate(found_files, 1):
        print(f"  {i}. {file_info['name']}")
        print(f"     📊 {file_info['url_count']} URLs, {file_info['size_mb']:.2f} MB")
        print(f"     📅 Modified: {file_info['mtime'].strftime('%Y-%m-%d %H:%M:%S')}")
        print()

    return [f['name'] for f in found_files]

def interactive_mode():
    """Run in interactive mode to guide user through options"""
    print("=" * 80)
    print("🚀 COMPLETE TWO-PHASE SCRAPER - INTERACTIVE MODE")
    print("=" * 80)

    # Ask about workflow type
    print("\n1️⃣ Choose workflow type:")
    print("  1. Complete workflow (Phase 1 + Phase 2)")
    print("  2. Phase 2 only (use existing URLs)")
    print("  3. Test mode (limited run for testing)")

    while True:
        choice = input("\nEnter choice (1-3): ").strip()
        if choice in ['1', '2', '3']:
            break
        print("❌ Invalid choice. Please enter 1, 2, or 3.")

    workflow_type = int(choice)
    phase2_only = workflow_type == 2
    test_mode = workflow_type == 3
    existing_json = None

    # If Phase 2 only, show available files
    if phase2_only:
        available_files = show_existing_files()
        if not available_files:
            print("❌ No URL files found. Please run Phase 1 first or provide a URL file.")
            return None

        print("Choose a file for Phase 2:")
        while True:
            try:
                file_choice = input(f"Enter number (1-{len(available_files)}): ").strip()
                file_index = int(file_choice) - 1
                if 0 <= file_index < len(available_files):
                    existing_json = available_files[file_index]
                    break
                else:
                    print(f"❌ Invalid choice. Please enter 1-{len(available_files)}.")
            except ValueError:
                print("❌ Please enter a valid number.")

    # City selection (for Phase 1)
    custom_cities = None
    if not phase2_only:
        print("\n2️⃣ City selection:")
        print("  1. All default cities")
        print("  2. Select specific cities")

        while True:
            city_choice = input("Enter choice (1-2): ").strip()
            if city_choice in ['1', '2']:
                break
            print("❌ Invalid choice. Please enter 1 or 2.")

        if city_choice == '2':
            show_available_cities()
            cities_input = input("Enter city names separated by commas: ").strip()
            if cities_input:
                custom_cities = [city.strip() for city in cities_input.split(',')]
                print(f"✅ Selected cities: {custom_cities}")

    # Settings
    print("\n3️⃣ Scraping settings:")

    # Max pages (for Phase 1)
    max_pages = 15
    if not phase2_only:
        while True:
            try:
                pages_input = input(f"Max pages per city/source (default {max_pages}): ").strip()
                if not pages_input:
                    break
                max_pages = int(pages_input)
                if max_pages > 0:
                    break
                else:
                    print("❌ Please enter a positive number.")
            except ValueError:
                print("❌ Please enter a valid number.")

    # Delay and workers (for Phase 2)
    delay = 2.0 if test_mode else 1.0
    max_workers = 3 if test_mode else 5
    checkpoint_interval = 100 if test_mode else 500

    delay_input = input(f"Delay between requests in seconds (default {delay}): ").strip()
    if delay_input:
        try:
            delay = float(delay_input)
        except ValueError:
            print("❌ Invalid delay, using default")

    workers_input = input(f"Number of concurrent workers (default {max_workers}): ").strip()
    if workers_input:
        try:
            max_workers = int(workers_input)
        except ValueError:
            print("❌ Invalid worker count, using default")

    # Summary
    print("\n📋 CONFIGURATION SUMMARY:")
    print("=" * 50)
    print(f"Workflow: {'Phase 2 Only' if phase2_only else 'Complete (Phase 1 + Phase 2)'}")
    if existing_json:
        print(f"URL File: {existing_json}")
    if custom_cities:
        print(f"Cities: {', '.join(custom_cities)}")
    elif not phase2_only:
        print("Cities: All default cities")
    if not phase2_only:
        print(f"Max Pages: {max_pages}")
    print(f"Delay: {delay}s")
    print(f"Workers: {max_workers}")
    print(f"Checkpoint: Every {checkpoint_interval} URLs")
    if test_mode:
        print("🧪 TEST MODE: Limited run")

    # Confirmation
    confirm = input("\n✅ Start scraping with these settings? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ Scraping cancelled.")
        return None

    return {
        'phase2_only': phase2_only,
        'existing_json': existing_json,
        'custom_cities': custom_cities,
        'max_pages': max_pages,
        'delay': delay,
        'max_workers': max_workers,
        'checkpoint_interval': checkpoint_interval,
        'test_mode': test_mode
    }

def run_scraper(config):
    """Run the scraper with given configuration"""
    # Initialize scraper
    scraper = CompleteTwoPhaseScraper(
        delay=config['delay'],
        max_workers=config['max_workers'],
        checkpoint_interval=config['checkpoint_interval']
    )

    # Adjust for test mode
    if config.get('test_mode'):
        # Limit cities for testing
        if not config['phase2_only'] and not config['custom_cities']:
            config['custom_cities'] = ['Baltimore']  # Just one city for testing
        config['max_pages'] = min(config['max_pages'], 3)  # Max 3 pages for testing
        logger.info("🧪 Running in TEST MODE - limited scope")

    try:
        # Run workflow
        result = scraper.run_complete_workflow(
            max_pages=config['max_pages'],
            custom_cities=config['custom_cities'],
            phase2_only=config['phase2_only'],
            existing_json=config['existing_json']
        )

        if result:
            print("\n" + "=" * 80)
            print("🎉 SCRAPING COMPLETED SUCCESSFULLY!")
            print("=" * 80)
            print(f"📋 Final results saved to: {result}")
            print(f"📁 Check the file for extracted profile data")

            # Show quick stats if possible
            try:
                import pandas as pd
                df = pd.read_excel(result, sheet_name='All_Profile_Data')
                total_profiles = len(df)
                successful = len(df[df['status'] == 'success'])
                unique_phones = len(df[(df['phone_number'].notna()) & (~df['duplicate_phone'])])

                print(f"\n📊 Quick Stats:")
                print(f"   Total profiles processed: {total_profiles}")
                print(f"   Successful extractions: {successful}")
                print(f"   Unique phone numbers: {unique_phones}")
            except Exception:
                pass  # Stats failed, not critical

        else:
            print("\n❌ SCRAPING FAILED - Check logs for details")
            return False

    except KeyboardInterrupt:
        print("\n🛑 Scraping interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Scraping failed with error: {e}")
        logger.error(f"Scraping error: {e}")
        return False

    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Complete Two-Phase Scraper Runner',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                          # Interactive mode
  %(prog)s --cities "Baltimore,Philadelphia"        # Specific cities
  %(prog)s --phase2-only                           # Phase 2 only
  %(prog)s --test-mode                             # Quick test
  %(prog)s --max-pages 20 --delay 2.0             # Custom settings
        """
    )

    # Main options
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='Run in interactive mode (default if no args)')
    parser.add_argument('--cities', type=str,
                       help='Comma-separated list of cities to scrape')
    parser.add_argument('--phase2-only', action='store_true',
                       help='Skip Phase 1, use existing URL file')
    parser.add_argument('--existing-json', type=str,
                       help='Specific JSON file for Phase 2')
    parser.add_argument('--test-mode', action='store_true',
                       help='Run in test mode (limited scope)')

    # Phase 1 settings
    parser.add_argument('--max-pages', type=int, default=15,
                       help='Maximum pages per city/source (default: 15)')

    # Phase 2 settings
    parser.add_argument('--delay', type=float, default=1.0,
                       help='Delay between requests (default: 1.0)')
    parser.add_argument('--workers', type=int, default=5,
                       help='Concurrent workers (default: 5)')
    parser.add_argument('--checkpoint-interval', type=int, default=500,
                       help='Checkpoint interval (default: 500)')

    # Utility options
    parser.add_argument('--show-cities', action='store_true',
                       help='Show available cities and exit')
    parser.add_argument('--show-files', action='store_true',
                       help='Show available URL files and exit')

    args = parser.parse_args()

    # Handle utility options
    if args.show_cities:
        show_available_cities()
        return

    if args.show_files:
        show_existing_files()
        return

    # Check if we should run interactive mode
    run_interactive = args.interactive or len(sys.argv) == 1

    if run_interactive:
        # Interactive mode
        config = interactive_mode()
        if not config:
            return
    else:
        # Command line mode
        config = {
            'phase2_only': args.phase2_only,
            'existing_json': args.existing_json,
            'custom_cities': [city.strip() for city in args.cities.split(',')] if args.cities else None,
            'max_pages': args.max_pages,
            'delay': args.delay,
            'max_workers': args.workers,
            'checkpoint_interval': args.checkpoint_interval,
            'test_mode': args.test_mode
        }

        # Adjust test mode settings
        if args.test_mode:
            config['delay'] = 2.0
            config['max_workers'] = 3
            config['checkpoint_interval'] = 100
            config['max_pages'] = min(config['max_pages'], 3)

    # Run the scraper
    success = run_scraper(config)

    if success:
        print("\n🎯 Scraping completed successfully!")
    else:
        print("\n💥 Scraping failed or was interrupted.")
        sys.exit(1)

if __name__ == "__main__":
    main()
