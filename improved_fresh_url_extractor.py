#!/usr/bin/env python3
"""
Improved Fresh URL Extractor - Extract validated URLs from target cities
Ensures URLs are actually from the specific cities we're targeting
Uses new ScraperAPI key: ********************************
"""

import requests
import re
import time
import json
from typing import List, Dict, Set, Optional
from bs4 import BeautifulSoup
from datetime import datetime
from urllib.parse import urljoin, urlparse

class ImprovedFreshURLExtractor:
    def __init__(self, scraperapi_key: str = "********************************"):
        """Initialize improved fresh URL extractor"""

        self.scraperapi_key = scraperapi_key
        self.scraperapi_url = "https://api.scraperapi.com/"

        # Only use aaok and aypapi (no escortalligator)
        self.sources = ['aaok', 'aypapi']

        # Target cities with proper URL mapping
        self.target_cities = [
            {
                "name": "South New Jersey",
                "state": "New Jersey",
                "url_name": "south%20new%20jersey",
                "state_code": "new%20jersey",
                "expected_patterns": ["south", "jersey", "newjersey"]
            },
            {
                "name": "Philadelphia",
                "state": "Pennsylvania",
                "url_name": "philadelphia",
                "state_code": "pennsylvania",
                "expected_patterns": ["philadelphia", "philly", "pennsylvania"]
            },
            {
                "name": "Pittsburgh",
                "state": "Pennsylvania",
                "url_name": "pittsburgh",
                "state_code": "pennsylvania",
                "expected_patterns": ["pittsburgh", "pennsylvania"]
            },
            {
                "name": "Wilmington",
                "state": "Delaware",
                "url_name": "wilmington",
                "state_code": "delaware",
                "expected_patterns": ["wilmington", "delaware"]
            },
            {
                "name": "Dover",
                "state": "Delaware",
                "url_name": "dover",
                "state_code": "delaware",
                "expected_patterns": ["dover", "delaware"]
            },
            {
                "name": "Baltimore",
                "state": "Maryland",
                "url_name": "baltimore",
                "state_code": "maryland",
                "expected_patterns": ["baltimore", "maryland"]
            },
            {
                "name": "Annapolis",
                "state": "Maryland",
                "url_name": "annapolis",
                "state_code": "maryland",
                "expected_patterns": ["annapolis", "maryland"]
            }
        ]

        # Statistics
        self.stats = {
            'search_pages_scraped': 0,
            'urls_extracted': 0,
            'urls_validated': 0,
            'api_requests': 0,
            'cities_processed': 0
        }

        print("🔄 Improved Fresh URL Extractor initialized")
        print(f"🏙️ Target cities: {len(self.target_cities)}")
        print(f"📡 Sources: {', '.join(self.sources)}")
        print(f"🔑 Using new ScraperAPI key: ...{self.scraperapi_key[-8:]}")

    def fetch_with_scraperapi(self, url: str) -> Optional[str]:
        """Fetch URL using ScraperAPI with new key"""
        try:
            params = {
                'api_key': self.scraperapi_key,
                'url': url,
                'render': 'false',
                'country_code': 'us',
                'device_type': 'desktop'
            }

            response = requests.get(self.scraperapi_url, params=params, timeout=60)
            self.stats['api_requests'] += 1

            if response.status_code == 200:
                return response.text
            else:
                print(f"⚠️ ScraperAPI failed for {url}: Status {response.status_code}")
                if response.status_code == 403:
                    print("   API credits may be exhausted or rate limited")
                return None

        except Exception as e:
            print(f"❌ Error fetching {url}: {e}")
            return None

    def validate_url_city_match(self, url: str, expected_patterns: List[str]) -> bool:
        """Validate that URL actually belongs to expected city"""
        url_lower = url.lower()

        # Check if URL contains any of the expected patterns
        for pattern in expected_patterns:
            if pattern.lower() in url_lower:
                return True

        # Also check the path structure
        url_parts = url.split('/')
        for part in url_parts:
            part_clean = part.replace('%20', ' ').lower()
            for pattern in expected_patterns:
                if pattern.lower() in part_clean:
                    return True

        return False

    def extract_profile_urls_from_html(self, html: str, source: str, city_info: Dict) -> List[Dict]:
        """Extract profile URLs from search page HTML with strict validation"""
        if not html:
            return []

        try:
            soup = BeautifulSoup(html, 'html.parser')
            profile_urls = []

            # Look for links with class 'listtitle' (primary pattern)
            listtitle_links = soup.find_all('a', class_='listtitle', href=True)

            # Also look for general escort post links
            all_links = soup.find_all('a', href=True)
            escort_links = [link for link in all_links if 'post/escorts' in link.get('href', '')]

            # Combine all potential links
            candidate_links = listtitle_links + escort_links

            for link in candidate_links:
                href = link.get('href', '')

                # Only process links that contain 'post/escorts'
                if 'post/escorts' not in href:
                    continue

                # Skip escortalligator links (only aaok and aypapi)
                if 'escortalligator' in href:
                    continue

                # Build full URL
                if href.startswith('http'):
                    full_url = href
                elif href.startswith('/'):
                    full_url = f"https://{source}.com.listcrawler.eu{href}"
                else:
                    continue

                # CRITICAL: Validate that this URL actually belongs to our target city
                if not self.validate_url_city_match(full_url, city_info['expected_patterns']):
                    continue  # Skip URLs that don't match our target city

                # Extract age information from surrounding elements
                age_info = self.extract_age_from_context(link)

                # Only include if age ≤ 30 or age not found
                if age_info is None or age_info <= 30:
                    profile_urls.append({
                        'url': full_url,
                        'estimated_age': age_info,
                        'source': source,
                        'validated': True
                    })

            # Remove duplicates
            seen_urls = set()
            unique_urls = []
            for url_data in profile_urls:
                if url_data['url'] not in seen_urls:
                    seen_urls.add(url_data['url'])
                    unique_urls.append(url_data)

            self.stats['urls_validated'] += len(unique_urls)
            return unique_urls

        except Exception as e:
            print(f"❌ Error extracting URLs from HTML: {e}")
            return []

    def extract_age_from_context(self, link_element) -> Optional[int]:
        """Extract age information from link context"""
        try:
            # Check the link text itself
            link_text = link_element.get_text(strip=True)
            age_match = re.search(r'\b(\d{2})\b', link_text)
            if age_match:
                age = int(age_match.group(1))
                if 18 <= age <= 99:
                    return age

            # Check parent elements for age info
            parent = link_element.parent
            for _ in range(3):
                if parent is None:
                    break

                # Look for age in class names like 'titleAge'
                age_divs = parent.find_all('div', class_=re.compile(r'.*[Aa]ge.*'))
                for age_div in age_divs:
                    age_text = age_div.get_text(strip=True)
                    age_match = re.search(r'\b(\d{2})\b', age_text)
                    if age_match:
                        age = int(age_match.group(1))
                        if 18 <= age <= 99:
                            return age

                # Check all text in parent for age patterns
                parent_text = parent.get_text()
                age_patterns = [
                    r'Age[:\s]*(\d{2})',
                    r'(\d{2})\s*years?\s*old',
                    r'I\'m\s*(\d{2})',
                    r'Age\s*(\d{2})'
                ]

                for pattern in age_patterns:
                    age_match = re.search(pattern, parent_text, re.IGNORECASE)
                    if age_match:
                        age = int(age_match.group(1))
                        if 18 <= age <= 99:
                            return age

                parent = parent.parent

            return None

        except Exception:
            return None

    def scrape_city_validated_urls(self, city_info: Dict, max_pages: int = 20) -> List[Dict]:
        """Scrape validated URLs from current search pages for a specific city"""
        city_name = city_info['name']
        state_name = city_info['state']
        url_name = city_info['url_name']
        state_code = city_info['state_code']

        print(f"\n🏙️ Extracting validated URLs for {city_name}, {state_name}")
        print(f"   🔍 Expected patterns: {', '.join(city_info['expected_patterns'])}")

        all_urls = []

        for source in self.sources:
            print(f"   📡 Scraping {source.upper()} search pages...")

            # Build base URL
            base_url = f"https://{source}.com.listcrawler.eu/brief/escorts/usa/{state_code}/{url_name}"

            page = 1
            consecutive_empty = 0
            validated_urls_found = 0

            while page <= max_pages and consecutive_empty < 5:
                search_url = f"{base_url}/{page}"
                print(f"      📄 Page {page}: {search_url}")

                # Fetch search page
                html = self.fetch_with_scraperapi(search_url)
                if not html:
                    consecutive_empty += 1
                    page += 1
                    continue

                # Extract and validate URLs from this page
                page_urls = self.extract_profile_urls_from_html(html, source, city_info)

                if not page_urls:
                    consecutive_empty += 1
                    print(f"         ❌ No validated URLs found on page {page}")
                else:
                    consecutive_empty = 0
                    validated_urls_found += len(page_urls)

                    # Add city/state info to each URL
                    for url_data in page_urls:
                        url_data.update({
                            'city': city_name,
                            'state': state_name,
                            'page': page
                        })

                    all_urls.extend(page_urls)
                    print(f"         ✅ Found {len(page_urls)} validated URLs on page {page}")

                self.stats['search_pages_scraped'] += 1
                page += 1
                time.sleep(1)  # Rate limiting

            print(f"   ✅ {source.upper()}: {validated_urls_found} validated URLs")

        print(f"🎯 {city_name} total: {len(all_urls)} validated URLs")
        self.stats['urls_extracted'] += len(all_urls)
        self.stats['cities_processed'] += 1

        return all_urls

    def validate_final_url_set(self, urls: List[Dict]) -> List[Dict]:
        """Final validation of extracted URLs"""
        validated_urls = []

        for url_data in urls:
            # Double-check URL format
            if not self.validate_url_format(url_data['url']):
                continue

            # Ensure city match
            if not self.validate_url_city_match(url_data['url'], url_data.get('expected_patterns', [])):
                continue

            validated_urls.append(url_data)

        return validated_urls

    def validate_url_format(self, url: str) -> bool:
        """Validate URL format"""
        try:
            parsed = urlparse(url)
            return (
                parsed.scheme in ['http', 'https'] and
                'listcrawler.eu' in parsed.netloc and
                'post/escorts' in parsed.path and
                ('aaok' in parsed.netloc or 'aypapi' in parsed.netloc)
            )
        except:
            return False

    def extract_all_validated_urls(self) -> List[Dict]:
        """Extract validated URLs from all target cities with strict validation"""
        print("🚀 IMPROVED FRESH URL EXTRACTION - Starting")
        print("=" * 60)
        print("🔍 Using strict city validation to ensure proper URLs")

        all_validated_urls = []

        for city_info in self.target_cities:
            print(f"\n{'='*60}")
            city_urls = self.scrape_city_validated_urls(city_info)

            # Final validation pass
            validated_urls = self.validate_final_url_set(city_urls)
            invalid_count = len(city_urls) - len(validated_urls)

            if invalid_count > 0:
                print(f"   🔍 Filtered out {invalid_count} invalid URLs in final validation")

            all_validated_urls.extend(validated_urls)

            # Save individual city results
            city_filename = f"validated_urls_{city_info['name'].replace(' ', '_')}_{city_info['state'].replace(' ', '_')}.json"
            with open(city_filename, 'w') as f:
                json.dump(validated_urls, f, indent=2)
            print(f"💾 Saved {len(validated_urls)} validated URLs to {city_filename}")

        # Save all validated URLs
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        all_urls_filename = f"validated_all_urls_{timestamp}.json"
        with open(all_urls_filename, 'w') as f:
            json.dump(all_validated_urls, f, indent=2)

        # Print comprehensive statistics
        print(f"\n🎯 IMPROVED URL EXTRACTION COMPLETE")
        print(f"=" * 60)
        print(f"✅ Total validated URLs: {len(all_validated_urls)}")
        print(f"🔍 URLs passed validation: {self.stats['urls_validated']}")
        print(f"🏙️ Cities processed: {self.stats['cities_processed']}")
        print(f"📄 Search pages scraped: {self.stats['search_pages_scraped']}")
        print(f"🌐 API requests made: {self.stats['api_requests']}")
        print(f"💾 All validated URLs saved to: {all_urls_filename}")

        # URL breakdown by city
        city_breakdown = {}
        source_breakdown = {'aaok': 0, 'aypapi': 0}

        for url_data in all_validated_urls:
            city_key = f"{url_data['city']}, {url_data['state']}"
            city_breakdown[city_key] = city_breakdown.get(city_key, 0) + 1

            source = url_data['source']
            source_breakdown[source] = source_breakdown.get(source, 0) + 1

        print(f"\n🏙️ Validated URLs by City:")
        for city, count in sorted(city_breakdown.items()):
            print(f"   {city}: {count} URLs")

        print(f"\n📡 Validated URLs by Source:")
        for source, count in source_breakdown.items():
            print(f"   {source}: {count} URLs")

        print(f"\n🎉 SUCCESS: {len(all_validated_urls)} properly validated URLs ready for processing!")
        print(f"=" * 60)
        return all_validated_urls

def main():
    """Main execution"""
    import argparse

    parser = argparse.ArgumentParser(description='Improved Fresh URL Extractor with Validation')
    parser.add_argument('--max-pages', type=int, default=20, help='Maximum pages per city/source')

    args = parser.parse_args()

    # Initialize extractor
    extractor = ImprovedFreshURLExtractor()

    try:
        # Extract validated URLs
        validated_urls = extractor.extract_all_validated_urls()

        if validated_urls:
            print(f"\n🎉 SUCCESS: {len(validated_urls)} validated URLs extracted")
            print(f"📁 Ready for Phase 2 processing with proper city validation")

            # Create batch files for processing
            batch_size = 200
            num_batches = (len(validated_urls) + batch_size - 1) // batch_size

            print(f"\n📦 Creating {num_batches} validated batch files:")
            for i in range(num_batches):
                start_idx = i * batch_size
                end_idx = min((i + 1) * batch_size, len(validated_urls))
                batch_urls = validated_urls[start_idx:end_idx]

                filename = f'validated_batch_{i+1:02d}_{len(batch_urls)}_urls.json'
                with open(filename, 'w') as f:
                    json.dump(batch_urls, f, indent=2)

                print(f'   {filename}: {len(batch_urls)} URLs')

            print(f"\n✅ Ready for validated batch processing!")
        else:
            print(f"\n⚠️ No validated URLs extracted")

    except KeyboardInterrupt:
        print(f"\n🛑 Extraction interrupted by user")
    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
