#!/usr/bin/env python3
"""
Playwright-Enhanced NYC Scraper - Handles JavaScript rendering and cookie management
Solves the "Just a moment...Enable JavaScript and cookies to continue" issue
"""

import sys
import os
import json
import time
import threading
import argparse
import asyncio
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import pandas as pd
import re
from pathlib import Path
from bs4 import BeautifulSoup
from urllib.parse import urlparse
import random

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
    from playwright_stealth import stealth_async
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("Warning: Playwright not installed. Install with: pip install playwright playwright-stealth")
    print("Then run: playwright install chromium")

from nyc_boroughs_scraper import NYCBoroughsScraper

class PlaywrightNYCScraper:
    def __init__(self, max_workers: int = 3, headless: bool = True):
        """Initialize Playwright-enhanced NYC scraper"""

        if not PLA<PERSON><PERSON>IGHT_AVAILABLE:
            raise ImportError("Playwright is required. Install with: pip install playwright playwright-stealth")

        self.max_workers = min(max_workers, 3)  # Limit concurrent browsers to avoid detection
        self.headless = headless
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results_lock = threading.Lock()
        self.all_extracted_data = []
        self.save_interval = 500  # Save every 500 URLs (smaller for more frequent saves)
        self.last_save_count = 0

        # Browser management
        self.playwright = None
        self.browser = None
        self.contexts = {}

        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] - %(message)s',
            handlers=[
                logging.FileHandler('playwright_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        self.logger.info(f"Playwright Enhanced Scraper initialized with {self.max_workers} workers")
        self.logger.info(f"Headless mode: {headless}")
        self.logger.info(f"Auto-save enabled: Excel file saved every {self.save_interval} URLs")

    async def init_browser(self):
        """Initialize Playwright browser"""
        if self.playwright is None:
            self.playwright = await async_playwright().start()

        if self.browser is None:
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )

        return self.browser

    async def get_context(self, worker_id: str = "default") -> BrowserContext:
        """Get or create browser context for worker"""
        if worker_id not in self.contexts:
            browser = await self.init_browser()

            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                extra_http_headers={
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                }
            )

            # Enable stealth mode
            page = await context.new_page()
            await stealth_async(page)
            await page.close()

            self.contexts[worker_id] = context
            self.logger.info(f"Created browser context for worker {worker_id}")

        return self.contexts[worker_id]

    async def fetch_page_content(self, url: str, worker_id: str = "default", max_retries: int = 3) -> Optional[str]:
        """Fetch page content using Playwright with proper JavaScript handling"""

        for attempt in range(max_retries):
            try:
                context = await self.get_context(worker_id)
                page = await context.new_page()

                # Random delay to mimic human behavior
                await asyncio.sleep(random.uniform(0.5, 2.0))

                # Navigate to page with increased timeout
                response = await page.goto(url, wait_until='domcontentloaded', timeout=30000)

                if response and response.status == 200:
                    # Wait for potential JavaScript to load
                    await page.wait_for_timeout(random.randint(1000, 3000))

                    # Check for Cloudflare or similar protection
                    content = await page.content()

                    if "Just a moment" in content or "Enable JavaScript and cookies" in content:
                        self.logger.warning(f"Worker {worker_id} detected anti-bot protection on {url}, waiting...")
                        await page.wait_for_timeout(5000)  # Wait 5 seconds
                        content = await page.content()

                    # Check if we got actual content
                    if len(content) > 1000 and "profile" in content.lower():
                        await page.close()
                        self.logger.debug(f"Worker {worker_id} successfully fetched {len(content)} chars from {url}")
                        return content
                    else:
                        self.logger.warning(f"Worker {worker_id} got suspicious content from {url} (length: {len(content)})")

                await page.close()

            except Exception as e:
                self.logger.error(f"Worker {worker_id} attempt {attempt + 1}/{max_retries} failed for {url}: {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(random.uniform(2.0, 5.0))  # Wait before retry
                continue

        self.logger.error(f"Worker {worker_id} failed to fetch {url} after {max_retries} attempts")
        return None

    def detect_website_type(self, url: str, html_content: str) -> str:
        """Detect which website type we're dealing with"""
        domain = urlparse(url).netloc.lower()

        if 'aypapi' in domain or 'escortbabylon' in domain:
            return 'aypapi'
        elif 'aaok' in domain or 'adultsearch' in domain:
            return 'aaok'
        else:
            # Try to detect from content
            if 'aypapi' in html_content.lower() or 'escortbabylon' in html_content.lower():
                return 'aypapi'
            elif 'aaok' in html_content.lower() or 'adultsearch' in html_content.lower():
                return 'aaok'

        return 'unknown'

    def extract_aypapi_data(self, html_content: str, url: str) -> Dict:
        """Extract data specifically from AYPAPI/EscortBabylon structure"""

        extracted_data = {
            'url': url,
            'name': '',
            'age': '',
            'location': '',
            'phone': '',
            'description': '',
            'services': '',
            'rates': '',
            'availability': '',
            'additional_info': '',
            'website_type': 'aypapi'
        }

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Name extraction for AYPAPI
            name_selectors = [
                '.viewposttitle',
                'h1.profile-name',
                '.profile-header h1',
                'h1',
                '.title',
                '.name'
            ]

            for selector in name_selectors:
                name_elem = soup.select_one(selector)
                if name_elem:
                    extracted_data['name'] = name_elem.get_text(strip=True)
                    break

            # Age extraction - try selector first, then regex
            age_elem = soup.select_one('.postTitleAge')
            if age_elem:
                extracted_data['age'] = age_elem.get_text(strip=True)
            else:
                age_patterns = [
                    r'Age[:\s]*(\d{2})',
                    r'(\d{2})\s*years?\s*old',
                    r'Age\s*(\d{2})',
                ]

                for pattern in age_patterns:
                    age_match = re.search(pattern, html_content, re.IGNORECASE)
                    if age_match:
                        extracted_data['age'] = age_match.group(1)
                        break

            # Phone extraction - try selector first, then regex
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                extracted_data['phone'] = phone_elem.get_text(strip=True)
            else:
                phone_patterns = [
                    r'(?:\+?1[-.\s]?)?(?:\(?(\d{3})\)?[-.\s]?)?(\d{3})[-.\s]?(\d{4})',
                    r'(?:Phone|Tel|Call)[:\s]*([0-9\-\.\(\)\s]+)',
                ]

                for pattern in phone_patterns:
                    phone_match = re.search(pattern, html_content)
                    if phone_match:
                        extracted_data['phone'] = phone_match.group(0).strip()
                        break

            # Location extraction
            location_selectors = [
                '.location',
                '.address',
                '.city-info'
            ]

            for selector in location_selectors:
                location_elem = soup.select_one(selector)
                if location_elem:
                    extracted_data['location'] = location_elem.get_text(strip=True)
                    break

            # Description extraction
            desc_selectors = [
                '.profile-description',
                '.description',
                '.bio',
                '.about'
            ]

            for selector in desc_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    extracted_data['description'] = desc_elem.get_text(strip=True)
                    break

        except Exception as e:
            self.logger.error(f"Error extracting AYPAPI data from {url}: {e}")

        return extracted_data

    def extract_aaok_data(self, html_content: str, url: str) -> Dict:
        """Extract data specifically from AAOK/AdultSearch structure"""

        extracted_data = {
            'url': url,
            'name': '',
            'age': '',
            'location': '',
            'phone': '',
            'description': '',
            'services': '',
            'rates': '',
            'availability': '',
            'additional_info': '',
            'website_type': 'aaok'
        }

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Name extraction for AAOK
            name_selectors = [
                '.viewposttitle',
                'h1.profile-title',
                '.escort-name',
                'h1',
                '.title'
            ]

            for selector in name_selectors:
                name_elem = soup.select_one(selector)
                if name_elem:
                    extracted_data['name'] = name_elem.get_text(strip=True)
                    break

            # Age extraction
            age_elem = soup.find('div', class_='postTitleAge')
            if age_elem:
                extracted_data['age'] = age_elem.get_text(strip=True)

            # Phone extraction - try selector first, then regex
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                extracted_data['phone'] = phone_elem.get_text(strip=True)
            else:
                phone_patterns = [
                    r'(?:\+?1[-.\s]?)?(?:\(?(\d{3})\)?[-.\s]?)?(\d{3})[-.\s]?(\d{4})',
                    r'(?:Phone|Tel|Call)[:\s]*([0-9\-\.\(\)\s]+)',
                ]

                for pattern in phone_patterns:
                    phone_match = re.search(pattern, html_content)
                    if phone_match:
                        extracted_data['phone'] = phone_match.group(0).strip()
                        break

            # Location extraction
            location_selectors = [
                '.location-info',
                '.city-location',
                '.address'
            ]

            for selector in location_selectors:
                location_elem = soup.select_one(selector)
                if location_elem:
                    extracted_data['location'] = location_elem.get_text(strip=True)
                    break

            # Description extraction
            desc_selectors = [
                '.profile-content',
                '.description',
                '.bio'
            ]

            for selector in desc_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    extracted_data['description'] = desc_elem.get_text(strip=True)
                    break

        except Exception as e:
            self.logger.error(f"Error extracting AAOK data from {url}: {e}")

        return extracted_data

    def extract_all_data_enhanced(self, html_content: str, url: str) -> Dict:
        """Enhanced extraction that works with both website types"""

        website_type = self.detect_website_type(url, html_content)

        if website_type == 'aypapi':
            return self.extract_aypapi_data(html_content, url)
        elif website_type == 'aaok':
            return self.extract_aaok_data(html_content, url)
        else:
            # Fallback extraction
            extracted_data = {
                'url': url,
                'name': '',
                'age': '',
                'location': '',
                'phone': '',
                'description': '',
                'services': '',
                'rates': '',
                'availability': '',
                'additional_info': '',
                'website_type': 'unknown'
            }

            try:
                soup = BeautifulSoup(html_content, 'html.parser')

                # Generic name extraction
                h1_elem = soup.find('h1')
                if h1_elem:
                    extracted_data['name'] = h1_elem.get_text(strip=True)

                # Generic age extraction - try selector first
                age_elem = soup.find('div', class_='postTitleAge')
                if age_elem:
                    extracted_data['age'] = age_elem.get_text(strip=True)
                else:
                    age_match = re.search(r'Age[:\s]*(\d{2})', html_content, re.IGNORECASE)
                    if age_match:
                        extracted_data['age'] = age_match.group(1)

                # Generic phone extraction - try selector first
                phone_elem = soup.select_one('.viewposttelephone')
                if phone_elem:
                    extracted_data['phone'] = phone_elem.get_text(strip=True)
                else:
                    phone_match = re.search(r'(?:\+?1[-.\s]?)?(?:\(?(\d{3})\)?[-.\s]?)?(\d{3})[-.\s]?(\d{4})', html_content)
                    if phone_match:
                        extracted_data['phone'] = phone_match.group(0).strip()

            except Exception as e:
                self.logger.error(f"Error in fallback extraction for {url}: {e}")

            return extracted_data

    async def process_urls_batch(self, urls: List[str], worker_id: str) -> List[Dict]:
        """Process a batch of URLs using Playwright"""

        results = []

        self.logger.info(f"Worker {worker_id} starting batch of {len(urls)} URLs")

        for i, url in enumerate(urls):
            if i > 0 and i % 50 == 0:
                self.logger.info(f"Worker {worker_id} processed {i}/{len(urls)} URLs")

            # Fetch HTML using Playwright
            html_content = await self.fetch_page_content(url, worker_id)

            if html_content and len(html_content) > 100:
                # Extract data
                extracted_data = self.extract_all_data_enhanced(html_content, url)

                # Add metadata
                extracted_data.update({
                    'extracted_at': datetime.now().isoformat(),
                    'worker_id': worker_id,
                    'extraction_method': 'playwright_enhanced'
                })

                # Check if we extracted meaningful data
                has_meaningful_data = any([
                    extracted_data.get('name'),
                    extracted_data.get('age'),
                    extracted_data.get('phone')
                ])

                if has_meaningful_data:
                    results.append(extracted_data)
                    self.logger.debug(f"Worker {worker_id} extracted meaningful data from {url}")
                else:
                    self.logger.warning(f"Worker {worker_id} got content but no meaningful data from {url}")
            else:
                self.logger.warning(f"Worker {worker_id} failed to get valid content from {url}")

        self.logger.info(f"Worker {worker_id} completed batch: {len(results)}/{len(urls)} successful extractions")
        return results

    def save_intermediate_results(self, filename_suffix: str = ""):
        """Save current results to Excel file"""
        try:
            if not self.all_extracted_data:
                self.logger.warning("No data to save")
                return

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if filename_suffix:
                filename = f"playwright_enhanced_{len(self.all_extracted_data)}_urls_{timestamp}_{filename_suffix}.xlsx"
            else:
                filename = f"playwright_enhanced_{len(self.all_extracted_data)}_urls_{timestamp}.xlsx"

            # Convert to DataFrame
            df = pd.DataFrame(self.all_extracted_data)

            # Reorder columns for better readability
            column_order = [
                'name', 'age', 'location', 'phone', 'description',
                'services', 'rates', 'availability', 'additional_info',
                'url', 'website_type', 'extracted_at', 'worker_id', 'extraction_method'
            ]

            # Only include columns that exist
            existing_columns = [col for col in column_order if col in df.columns]
            df = df[existing_columns]

            df.to_excel(filename, index=False)
            self.logger.info(f"Saved {len(df)} records to {filename}")

        except Exception as e:
            self.logger.error(f"Error saving intermediate results: {e}")

    async def run_playwright_extraction(self, max_urls: int = None):
        """Run enhanced extraction using Playwright"""

        # Load URLs from the nested JSON structure
        if not os.path.exists(self.urls_file):
            self.logger.error(f"URLs file not found: {self.urls_file}")
            return False

        with open(self.urls_file, 'r') as f:
            url_data = json.load(f)

        # Extract URLs from the nested structure
        all_urls = []
        for borough_source, data in url_data.items():
            if 'urls' in data and data['urls']:
                all_urls.extend(data['urls'])
                self.logger.info(f"Loaded {len(data['urls'])} URLs from {borough_source}")

        if max_urls:
            all_urls = all_urls[:max_urls]
            self.logger.info(f"Limited to first {max_urls} URLs for testing")

        self.logger.info(f"Starting Playwright enhanced extraction for {len(all_urls)} URLs")
        self.logger.info(f"Using {self.max_workers} concurrent browser workers")

        # Split URLs into batches for workers
        batch_size = max(1, len(all_urls) // self.max_workers)
        url_batches = []

        for i in range(0, len(all_urls), batch_size):
            batch = all_urls[i:i + batch_size]
            url_batches.append(batch)

        # Ensure we don't exceed max_workers
        if len(url_batches) > self.max_workers:
            # Merge extra batches into the last workers
            while len(url_batches) > self.max_workers:
                extra_batch = url_batches.pop()
                url_batches[-1].extend(extra_batch)

        self.logger.info(f"Split into {len(url_batches)} batches: {[len(batch) for batch in url_batches]}")

        # Process batches concurrently using asyncio
        async def process_worker_batch(batch_urls, worker_id):
            worker_results = []
            try:
                worker_results = await self.process_urls_batch(batch_urls, f"pw-worker-{worker_id}")
                self.logger.info(f"Worker {worker_id} completed: {len(worker_results)} successful extractions")
            except Exception as e:
                self.logger.error(f"Worker {worker_id} failed: {e}")
            finally:
                # Clean up worker context
                worker_key = f"pw-worker-{worker_id}"
                if worker_key in self.contexts:
                    try:
                        await self.contexts[worker_key].close()
                        del self.contexts[worker_key]
                    except:
                        pass
            return worker_results

        # Run all workers concurrently
        tasks = []
        for i, batch in enumerate(url_batches):
            task = process_worker_batch(batch, i)
            tasks.append(task)

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Collect all results
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"Worker exception: {result}")
            elif isinstance(result, list):
                with self.results_lock:
                    self.all_extracted_data.extend(result)

                    # Save intermediate results periodically
                    if len(self.all_extracted_data) - self.last_save_count >= self.save_interval:
                        self.save_intermediate_results("checkpoint")
                        self.last_save_count = len(self.all_extracted_data)

        # Final save
        self.save_intermediate_results("final")

        # Cleanup
        await self.cleanup()

        self.logger.info(f"Playwright enhanced extraction completed")
        self.logger.info(f"Successfully extracted data from {len(self.all_extracted_data)} URLs")

        return True

    async def cleanup(self):
        """Cleanup browser resources"""
        try:
            for context in self.contexts.values():
                await context.close()

            if self.browser:
                await self.browser.close()

            if self.playwright:
                await self.playwright.stop()

            self.logger.info("Browser resources cleaned up")

        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

def main():
    """Main function to run Playwright enhanced scraper"""

    parser = argparse.ArgumentParser(description="Playwright Enhanced NYC Scraper")
    parser.add_argument('--max-urls', type=int, help='Maximum URLs to process (for testing)')
    parser.add_argument('--max-workers', type=int, default=3, help='Maximum concurrent workers')
    parser.add_argument('--visible', action='store_true', help='Run in visible mode (not headless)')

    args = parser.parse_args()

    async def run_scraper():
        scraper = PlaywrightNYCScraper(
            max_workers=args.max_workers,
            headless=not args.visible
        )

        try:
            success = await scraper.run_playwright_extraction(max_urls=args.max_urls)
            if success:
                print("✓ Playwright enhanced extraction completed successfully")
                return True
            else:
                print("✗ Playwright enhanced extraction failed")
                return False
        except KeyboardInterrupt:
            print("\n⚠ Interrupted by user")
            await scraper.cleanup()
            return False
        except Exception as e:
            print(f"✗ Unexpected error: {e}")
            await scraper.cleanup()
            return False

    # Run the async scraper
    result = asyncio.run(run_scraper())
    sys.exit(0 if result else 1)

if __name__ == "__main__":
    main()
