#!/usr/bin/env python3
"""
Parallel Two-Phase NYC Scraper: 
Phase 1: Multiple workers extract URLs from search pages in parallel
Phase 2: Multiple workers batch process dedicated pages in parallel
"""

import sys
import os
import json
import time
import threading
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import pandas as pd
import re
from pathlib import Path
from bs4 import BeautifulSoup

from nyc_boroughs_scraper import NYCBoroughsScraper

class ParallelTwoPhaseNYCScraper:
    def __init__(self, mistral_api_keys: List[str] = None, max_workers: int = 5):
        """Initialize parallel two-phase NYC scraper"""
        # Default to your 5 API keys if none provided
        default_keys = [
            "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G",
            "OHUPD3rpUQBbbd9FHpwnQpdQXIckRXqv", 
            "zeUtrAhXZm7RXe2Knt0xWGb19j3vb3f4",
            "Z9G5EWlDgYq8RtkV8xPfs7hZuAYghzg0",
            "e7QxoqwJNSPjcXFVmnEVgpAInrkWlRLS"
        ]
        
        self.mistral_api_keys = mistral_api_keys or default_keys
        self.max_workers = max_workers
        self.urls_file = "parallel_nyc_all_urls.json"
        self.target_boroughs = ['Brooklyn', 'Bronx', 'Queens', 'Manhattan', 'Staten Island']
        self.results_lock = threading.Lock()
        self.all_extracted_data = []
        self.save_interval = 1000  # Save every 1000 URLs
        self.last_save_count = 0

        # Retry configuration for Mistral API
        self.max_retries = 3
        self.base_retry_delay = 2.0  # Base delay for exponential backoff
        self.rate_limit_delay = 30.0  # Delay for 429 errors

        # Global rate limiting coordination
        self.global_rate_limit_lock = threading.Lock()
        self.last_rate_limit_time = {}  # Track rate limits per API key
        self.api_key_delays = {}  # Dynamic delays per API key
        
        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] - %(message)s',
            handlers=[
                logging.FileHandler('parallel_two_phase_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"Initialized with {len(self.mistral_api_keys)} API keys and {max_workers} workers")
        self.logger.info(f"Auto-save enabled: Excel file saved every {self.save_interval} scraped URLs")
        self.logger.info(f"Intelligent retry: {self.max_retries} retries with exponential backoff")
        self.logger.info(f"Rate limiting: Smart coordination between workers with adaptive delays")
        
    def get_nyc_combinations(self) -> List[Dict[str, str]]:
        """Get all NYC borough-source combinations"""
        temp_scraper = NYCBoroughsScraper(self.mistral_api_keys[0])
        all_combinations = temp_scraper.get_nyc_boroughs()
        
        # Filter for target boroughs
        nyc_combinations = []
        for combo in all_combinations:
            if combo['city'] in self.target_boroughs:
                nyc_combinations.append(combo)
        
        self.logger.info(f"Found {len(nyc_combinations)} NYC borough-source combinations")
        return nyc_combinations

    def save_intermediate_results(self, force_save: bool = False):
        """Save intermediate results every 1000 URLs or when forced"""
        with self.results_lock:
            current_count = len(self.all_extracted_data)

            # Check if we should save (every 1000 URLs or forced)
            if force_save or (current_count >= self.last_save_count + self.save_interval):
                if current_count > 0:
                    try:
                        # Create DataFrame
                        df = pd.DataFrame(self.all_extracted_data)

                        # Generate filename with timestamp and count
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"nyc_intermediate_{current_count}_urls_{timestamp}.xlsx"

                        # Reorder columns for better readability
                        column_order = [
                            'state', 'city', 'source', 'title', 'name', 'age', 'phone',
                            'description', 'social_media', 'email', 'website', 'posted_date',
                            'post_id', 'url', 'search_url', 'scraped_at', 'worker_id'
                        ]

                        existing_columns = [col for col in column_order if col in df.columns]
                        remaining_columns = [col for col in df.columns if col not in existing_columns]
                        final_columns = existing_columns + remaining_columns
                        df = df[final_columns]

                        # Sort data
                        df = df.sort_values(['state', 'city', 'source', 'scraped_at'])

                        # Save to Excel
                        df.to_excel(filename, index=False)

                        self.logger.info(f"📊 INTERMEDIATE SAVE: {current_count} URLs saved to {filename}")

                        # Update last save count
                        self.last_save_count = current_count

                        # Summary by source
                        aaok_count = len([r for r in self.all_extracted_data if r.get('source') == 'aaok'])
                        aypapi_count = len([r for r in self.all_extracted_data if r.get('source') == 'aypapi'])
                        self.logger.info(f"   Current totals: aaok={aaok_count}, aypapi={aypapi_count}")

                        # Summary by borough
                        boroughs = list(set(r.get('city') for r in self.all_extracted_data))
                        borough_summary = []
                        for borough in sorted(boroughs):
                            borough_count = len([r for r in self.all_extracted_data if r.get('city') == borough])
                            borough_summary.append(f"{borough}={borough_count}")
                        self.logger.info(f"   By borough: {', '.join(borough_summary)}")

                    except Exception as e:
                        self.logger.error(f"Failed to save intermediate results: {e}")

                return True
            return False

    def call_mistral_with_retry(self, mistral_client, prompt: str, worker_id: int, api_key: str, max_tokens: int = 2800) -> Optional[str]:
        """Call Mistral API with intelligent retry logic and rate limiting handling"""
        # Apply intelligent rate limiting before making the call
        self.handle_rate_limiting(api_key, worker_id)

        for attempt in range(self.max_retries + 1):
            try:
                response = mistral_client.chat.complete(
                    model="mistral-large-latest",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1,
                    max_tokens=max_tokens
                )

                # Success - record success and return the response
                result_text = response.choices[0].message.content.strip()
                self.record_success(api_key, worker_id)
                if attempt > 0:
                    self.logger.info(f"Worker {worker_id} Mistral API succeeded on attempt {attempt + 1}")
                return result_text

            except Exception as e:
                error_str = str(e).lower()

                # Check if it's a rate limiting error (429)
                if "429" in error_str or "too many requests" in error_str or "rate limit" in error_str:
                    # Record the rate limit for intelligent coordination
                    self.record_rate_limit(api_key, worker_id)

                    if attempt < self.max_retries:
                        delay = self.rate_limit_delay * (1.5 ** attempt)  # Exponential backoff for rate limits
                        self.logger.warning(f"Worker {worker_id} hit rate limit (attempt {attempt + 1}/{self.max_retries + 1}). Waiting {delay:.1f}s...")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"Worker {worker_id} rate limit exceeded after {self.max_retries + 1} attempts")
                        return None

                # Check if it's a server error (5xx) or connection error
                elif any(code in error_str for code in ["500", "502", "503", "504", "connection", "timeout"]):
                    if attempt < self.max_retries:
                        delay = self.base_retry_delay * (2 ** attempt)  # Exponential backoff for server errors
                        self.logger.warning(f"Worker {worker_id} server error (attempt {attempt + 1}/{self.max_retries + 1}). Waiting {delay:.1f}s... Error: {e}")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"Worker {worker_id} server error after {self.max_retries + 1} attempts: {e}")
                        return None

                # Check if it's a token limit error
                elif "token" in error_str and ("limit" in error_str or "exceeded" in error_str):
                    self.logger.error(f"Worker {worker_id} token limit exceeded. Reducing batch size may help: {e}")
                    return None

                # Other errors - retry with shorter delay
                else:
                    if attempt < self.max_retries:
                        delay = self.base_retry_delay * (1.2 ** attempt)  # Mild exponential backoff
                        self.logger.warning(f"Worker {worker_id} API error (attempt {attempt + 1}/{self.max_retries + 1}). Waiting {delay:.1f}s... Error: {e}")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"Worker {worker_id} API error after {self.max_retries + 1} attempts: {e}")
                        return None

        return None

    def handle_rate_limiting(self, api_key: str, worker_id: int):
        """Intelligent rate limiting coordination between workers"""
        with self.global_rate_limit_lock:
            current_time = time.time()
            key_preview = api_key[:8] + "..." + api_key[-4:]

            # Check if this API key recently hit rate limits
            if api_key in self.last_rate_limit_time:
                time_since_limit = current_time - self.last_rate_limit_time[api_key]

                # If recent rate limit, apply progressive delay
                if time_since_limit < 60:  # Within last minute
                    delay = max(1.0, 10.0 - time_since_limit)
                    self.logger.info(f"Worker {worker_id} applying rate limit delay {delay:.1f}s for key {key_preview}")
                    time.sleep(delay)

            # Update API key usage tracking
            if api_key not in self.api_key_delays:
                self.api_key_delays[api_key] = 0.5

            # Apply current delay for this API key
            current_delay = self.api_key_delays[api_key]
            if current_delay > 0.1:
                time.sleep(current_delay)

    def record_rate_limit(self, api_key: str, worker_id: int):
        """Record when an API key hits rate limits"""
        with self.global_rate_limit_lock:
            self.last_rate_limit_time[api_key] = time.time()

            # Increase delay for this API key
            if api_key in self.api_key_delays:
                self.api_key_delays[api_key] = min(5.0, self.api_key_delays[api_key] * 1.5)
            else:
                self.api_key_delays[api_key] = 2.0

            key_preview = api_key[:8] + "..." + api_key[-4:]
            self.logger.warning(f"Worker {worker_id} recorded rate limit for key {key_preview}, new delay: {self.api_key_delays[api_key]:.1f}s")

    def record_success(self, api_key: str, worker_id: int):
        """Record successful API call to gradually reduce delays"""
        with self.global_rate_limit_lock:
            if api_key in self.api_key_delays and self.api_key_delays[api_key] > 0.5:
                self.api_key_delays[api_key] = max(0.5, self.api_key_delays[api_key] * 0.9)

                # Log significant delay reductions
                if self.api_key_delays[api_key] <= 1.0:
                    key_preview = api_key[:8] + "..." + api_key[-4:]
                    self.logger.info(f"Worker {worker_id} reduced delay for key {key_preview} to {self.api_key_delays[api_key]:.1f}s")

    def extract_urls_for_combination(self, combination: Dict[str, str], worker_id: int) -> Dict[str, any]:
        """Extract URLs for a single borough-source combination (Phase 1 worker)"""
        borough_name = combination['city']
        source = combination['source']
        base_url = combination['url']
        combo_key = f"{borough_name}_{source}"
        
        # Assign API key to worker (round-robin)
        worker_api_key = self.mistral_api_keys[worker_id % len(self.mistral_api_keys)]
        key_preview = worker_api_key[:8] + "..." + worker_api_key[-4:]
        
        self.logger.info(f"Worker {worker_id} extracting URLs for {borough_name} from {source} (API key: {key_preview})")
        
        try:
            # Create scraper for this worker
            scraper = NYCBoroughsScraper(worker_api_key)
            scraper.request_delay = 0.3  # Faster for URL extraction
            
            # Extract URLs from all search pages
            all_dedicated_urls = []
            page_num = 1
            empty_page_count = 0
            
            while True:
                page_url = re.sub(r'/\d+$', f'/{page_num}', base_url)
                
                # Get search page HTML
                search_html = scraper.execute_curl_request(page_url, scraper.search_curl_template)
                if not search_html:
                    self.logger.warning(f"Worker {worker_id} failed to get page {page_num} for {borough_name}")
                    empty_page_count += 1
                else:
                    # Extract dedicated page URLs (filtered by age ≤30)
                    page_dedicated_urls = scraper.extract_dedicated_urls(search_html)
                    if not page_dedicated_urls:
                        empty_page_count += 1
                        self.logger.info(f"Worker {worker_id} no URLs on page {page_num} for {borough_name} (empty {empty_page_count}/{scraper.max_consecutive_empty_pages})")
                    else:
                        empty_page_count = 0
                        all_dedicated_urls.extend(page_dedicated_urls)
                        self.logger.info(f"Worker {worker_id} found {len(page_dedicated_urls)} URLs on page {page_num} for {borough_name}")
                
                # Check stopping conditions
                if page_num > scraper.min_pages_to_scrape and empty_page_count >= scraper.max_consecutive_empty_pages:
                    self.logger.info(f"Worker {worker_id} stopping after {scraper.max_consecutive_empty_pages} consecutive empty pages (scraped {page_num} pages)")
                    break
                elif page_num <= scraper.min_pages_to_scrape:
                    self.logger.info(f"Worker {worker_id} continuing - need minimum {scraper.min_pages_to_scrape} pages (currently at {page_num})")
                
                time.sleep(scraper.request_delay)
                page_num += 1
                
                if page_num > scraper.max_pages_per_city:
                    self.logger.info(f"Worker {worker_id} reached maximum page limit ({scraper.max_pages_per_city})")
                    break
            
            # Remove duplicates while preserving order
            unique_urls = list(dict.fromkeys(all_dedicated_urls))
            
            result = {
                'borough': borough_name,
                'source': source,
                'base_url': base_url,
                'pages_scraped': page_num - 1,
                'total_urls': len(unique_urls),
                'urls': unique_urls,
                'extracted_at': datetime.now().isoformat(),
                'worker_id': worker_id
            }
            
            self.logger.info(f"Worker {worker_id} completed {borough_name} ({source}): {len(unique_urls)} unique URLs from {page_num-1} pages")
            return {combo_key: result}
            
        except Exception as e:
            self.logger.error(f"Worker {worker_id} failed on {borough_name} ({source}): {e}")
            return None
    
    def phase1_parallel_extract_urls(self) -> bool:
        """Phase 1: Parallel extraction of URLs from search pages"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 1: PARALLEL URL EXTRACTION FROM SEARCH PAGES")
        self.logger.info("=" * 60)
        
        # Check if URLs file already exists
        if os.path.exists(self.urls_file):
            self.logger.info(f"URLs file {self.urls_file} already exists. Loading existing data...")
            try:
                with open(self.urls_file, 'r') as f:
                    existing_data = json.load(f)
                self.logger.info(f"Loaded {len(existing_data)} existing URL collections")
                return True
            except Exception as e:
                self.logger.warning(f"Failed to load existing URLs file: {e}")
        
        # Get all NYC combinations
        combinations = self.get_nyc_combinations()
        
        self.logger.info(f"Processing {len(combinations)} combinations with {self.max_workers} parallel workers")
        
        # Process combinations in parallel
        all_urls_data = {}
        successful_workers = 0
        failed_workers = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_combo = {
                executor.submit(self.extract_urls_for_combination, combo, i): combo 
                for i, combo in enumerate(combinations)
            }
            
            # Process completed tasks
            for future in as_completed(future_to_combo):
                combo = future_to_combo[future]
                try:
                    result = future.result()
                    if result is not None:
                        successful_workers += 1
                        all_urls_data.update(result)
                        
                        # Save progress after each completion
                        with self.results_lock:
                            try:
                                with open(self.urls_file, 'w') as f:
                                    json.dump(all_urls_data, f, indent=2)
                            except Exception as e:
                                self.logger.error(f"Failed to save progress: {e}")
                        
                        self.logger.info(f"✓ Completed: {combo['city']} from {combo['source']}")
                    else:
                        failed_workers += 1
                        self.logger.error(f"✗ Failed: {combo['city']} from {combo['source']}")
                except Exception as e:
                    failed_workers += 1
                    self.logger.error(f"✗ Exception in {combo['city']} from {combo['source']}: {e}")
        
        # Final summary
        total_urls = sum(data['total_urls'] for data in all_urls_data.values())
        total_pages = sum(data['pages_scraped'] for data in all_urls_data.values())
        
        self.logger.info("=" * 60)
        self.logger.info("PHASE 1 COMPLETED - PARALLEL URL EXTRACTION SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"Successful workers: {successful_workers}, Failed workers: {failed_workers}")
        self.logger.info(f"Total combinations processed: {len(all_urls_data)}")
        self.logger.info(f"Total search pages scraped: {total_pages}")
        self.logger.info(f"Total dedicated URLs extracted: {total_urls}")
        
        for combo_key, data in all_urls_data.items():
            self.logger.info(f"  {data['borough']} ({data['source']}): {data['total_urls']} URLs from {data['pages_scraped']} pages")
        
        self.logger.info(f"All URLs saved to: {self.urls_file}")
        return failed_workers == 0
    
    def process_combination_batch(self, combo_key: str, combo_data: Dict, batch_size: int, worker_id: int) -> List[Dict]:
        """Process a single combination with batch Mistral processing (Phase 2 worker)"""
        borough = combo_data['borough']
        source = combo_data['source']
        urls = combo_data['urls']
        base_url = combo_data['base_url']
        
        # Assign API key to worker (round-robin)
        worker_api_key = self.mistral_api_keys[worker_id % len(self.mistral_api_keys)]
        key_preview = worker_api_key[:8] + "..." + worker_api_key[-4:]
        
        self.logger.info(f"Worker {worker_id} processing {borough} ({source}): {len(urls)} URLs (API key: {key_preview})")
        
        if not urls:
            self.logger.info(f"Worker {worker_id} no URLs to process for {borough} ({source})")
            return []
        
        try:
            # Create scraper for this worker
            scraper = NYCBoroughsScraper(worker_api_key)
            
            # Download all HTML content first
            self.logger.info(f"Worker {worker_id} downloading HTML for {len(urls)} pages...")
            html_data = []
            
            for i, url in enumerate(urls):
                if i > 0 and i % 100 == 0:
                    self.logger.info(f"Worker {worker_id} downloaded {i}/{len(urls)} pages")
                
                html = scraper.execute_curl_request(url, scraper.dedicated_curl_template)
                if html:
                    html_data.append((url, html))
                else:
                    self.logger.warning(f"Worker {worker_id} failed to get HTML for {url}")
                
                time.sleep(0.05)  # Small delay
            
            self.logger.info(f"Worker {worker_id} successfully downloaded {len(html_data)} HTML pages")
            
            # Process in batches
            all_extracted_data = []
            total_batches = (len(html_data) + batch_size - 1) // batch_size
            
            for batch_num in range(total_batches):
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, len(html_data))
                batch = html_data[start_idx:end_idx]
                
                self.logger.info(f"Worker {worker_id} processing batch {batch_num + 1}/{total_batches} ({len(batch)} pages) for {borough}")
                
                # Process batch with Mistral
                batch_results = self.process_batch_with_mistral(batch, scraper, borough, source, base_url, worker_id)
                all_extracted_data.extend(batch_results)
                
                # Rate limiting between batches
                time.sleep(0.8)
            
            self.logger.info(f"Worker {worker_id} completed {borough} ({source}): {len(all_extracted_data)} records from {total_batches} batches")
            
            # Save checkpoint
            checkpoint_file = f"parallel_phase2_{borough}_{source}_checkpoint.xlsx"
            if all_extracted_data:
                try:
                    df = pd.DataFrame(all_extracted_data)
                    df.to_excel(checkpoint_file, index=False)
                    self.logger.info(f"Worker {worker_id} saved checkpoint: {checkpoint_file}")
                except Exception as e:
                    self.logger.warning(f"Worker {worker_id} failed to save checkpoint: {e}")
            
            return all_extracted_data
            
        except Exception as e:
            self.logger.error(f"Worker {worker_id} failed processing {borough} ({source}): {e}")
            return []

    def process_batch_with_mistral(self, html_batch: List, scraper, borough: str, source: str, base_url: str, worker_id: int) -> List[Dict]:
        """Process a batch of HTML pages with Mistral AI"""
        if not html_batch:
            return []

        try:
            # Prepare batch content for Mistral
            batch_content = []
            for i, (url, html_content) in enumerate(html_batch):
                # Extract text content from HTML
                soup = BeautifulSoup(html_content, 'html.parser')

                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()

                # Get text content
                text_content = soup.get_text()

                # Clean up text
                lines = (line.strip() for line in text_content.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                text_content = ' '.join(chunk for chunk in chunks if chunk)

                # Limit text length to prevent token overflow
                if len(text_content) > 2200:
                    text_content = text_content[:2200] + "..."

                batch_content.append({
                    'page_index': i,
                    'url': url,
                    'content': text_content
                })

            # Create batch prompt
            prompt = f"""
            You are extracting data from {len(batch_content)} escort profile pages. For each page, extract the following information:

            1. title: Profile title or headline
            2. name: Person's name
            3. age: Age (must be ≤30, skip if >30)
            4. phone: Phone number
            5. description: Profile description
            6. social_media: Social media handles/links (Instagram, Twitter, Snapchat, OnlyFans, etc.)
            7. email: Email address
            8. website: Website links
            9. posted_date: When the post was created
            10. post_id: Unique post identifier

            IMPORTANT: Only include profiles where age ≤30 AND gender is woman. Skip any profiles with age >30 or not women.

            Pages to process:
            """

            for page_data in batch_content:
                prompt += f"\n--- PAGE {page_data['page_index']} (URL: {page_data['url']}) ---\n"
                prompt += page_data['content'][:1600]  # Limit content per page
                prompt += "\n"

            prompt += """

            Return a JSON array with one object per valid page (only for women with age ≤30). Each object should have:
            {
                "page_index": <index>,
                "url": "<url>",
                "title": "<title>",
                "name": "<name>",
                "age": "<age>",
                "phone": "<phone>",
                "description": "<description>",
                "social_media": "<social_media>",
                "email": "<email>",
                "website": "<website>",
                "posted_date": "<posted_date>",
                "post_id": "<post_id>"
            }

            If any field is not found, use null. Only return valid JSON array.
            """

            # Make Mistral API call with retry logic
            worker_api_key = self.mistral_api_keys[worker_id % len(self.mistral_api_keys)]
            result_text = self.call_mistral_with_retry(
                mistral_client=scraper.mistral_client,
                prompt=prompt,
                worker_id=worker_id,
                api_key=worker_api_key,
                max_tokens=2800
            )

            if result_text is None:
                self.logger.error(f"Worker {worker_id} failed to get Mistral response after all retries")
                return []

            # Parse JSON response
            try:
                batch_results = json.loads(result_text)
                if not isinstance(batch_results, list):
                    batch_results = [batch_results]

                # Add metadata to each result
                for result in batch_results:
                    if result and isinstance(result, dict):
                        result['city'] = borough
                        result['state'] = 'New York'
                        result['source'] = source
                        result['search_url'] = base_url
                        result['scraped_at'] = datetime.now().isoformat()
                        result['worker_id'] = worker_id

                self.logger.info(f"Worker {worker_id} batch processed {len(html_batch)} pages, extracted {len(batch_results)} valid profiles")
                return batch_results

            except json.JSONDecodeError:
                # Try to extract JSON array from the response
                json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
                if json_match:
                    batch_results = json.loads(json_match.group())

                    # Add metadata
                    for result in batch_results:
                        if result and isinstance(result, dict):
                            result['city'] = borough
                            result['state'] = 'New York'
                            result['source'] = source
                            result['search_url'] = base_url
                            result['scraped_at'] = datetime.now().isoformat()
                            result['worker_id'] = worker_id

                    self.logger.info(f"Worker {worker_id} batch processed {len(html_batch)} pages, extracted {len(batch_results)} valid profiles (fallback parsing)")
                    return batch_results

                self.logger.warning(f"Worker {worker_id} failed to parse batch Mistral response")
                return []

        except Exception as e:
            self.logger.error(f"Worker {worker_id} batch Mistral extraction failed: {e}")
            return []

    def phase2_parallel_batch_scrape(self, batch_size: int = 10) -> bool:
        """Phase 2: Parallel batch scraping of dedicated pages"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 2: PARALLEL BATCH SCRAPING DEDICATED PAGES")
        self.logger.info("=" * 60)

        # Load URLs data
        if not os.path.exists(self.urls_file):
            self.logger.error(f"URLs file {self.urls_file} not found. Run Phase 1 first.")
            return False

        try:
            with open(self.urls_file, 'r') as f:
                urls_data = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load URLs file: {e}")
            return False

        self.logger.info(f"Using {len(self.mistral_api_keys)} API keys with {self.max_workers} parallel workers")
        self.logger.info(f"Batch processing: {batch_size} pages per Mistral API call")

        # Summary of what we'll process
        total_urls = sum(data['total_urls'] for data in urls_data.values())
        total_batches = (total_urls + batch_size - 1) // batch_size

        self.logger.info(f"Total URLs to process: {total_urls}")
        self.logger.info(f"Estimated batches: {total_batches}")
        self.logger.info(f"Estimated API calls: {total_batches} (vs {total_urls} individual calls)")
        self.logger.info(f"API call reduction: {((total_urls - total_batches) / total_urls * 100):.1f}%")

        # Process combinations in parallel
        successful_workers = 0
        failed_workers = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_combo = {
                executor.submit(self.process_combination_batch, combo_key, combo_data, batch_size, i): (combo_key, combo_data)
                for i, (combo_key, combo_data) in enumerate(urls_data.items())
            }

            # Process completed tasks
            for future in as_completed(future_to_combo):
                combo_key, combo_data = future_to_combo[future]
                try:
                    result = future.result()
                    if result is not None:
                        successful_workers += 1

                        # Thread-safe data collection with auto-save
                        with self.results_lock:
                            self.all_extracted_data.extend(result)

                        # Check if we should save intermediate results
                        self.save_intermediate_results()

                        self.logger.info(f"✓ Completed: {combo_data['borough']} from {combo_data['source']} ({len(result)} records)")
                    else:
                        failed_workers += 1
                        self.logger.error(f"✗ Failed: {combo_data['borough']} from {combo_data['source']}")
                except Exception as e:
                    failed_workers += 1
                    self.logger.error(f"✗ Exception in {combo_data['borough']} from {combo_data['source']}: {e}")

        # Force save any remaining data
        self.save_intermediate_results(force_save=True)

        # Save final results
        if self.all_extracted_data:
            final_file = "parallel_two_phase_nyc_final.xlsx"
            try:
                df = pd.DataFrame(self.all_extracted_data)

                # Reorder columns
                column_order = [
                    'state', 'city', 'source', 'title', 'name', 'age', 'phone',
                    'description', 'social_media', 'email', 'website', 'posted_date',
                    'post_id', 'url', 'search_url', 'scraped_at', 'worker_id'
                ]

                existing_columns = [col for col in column_order if col in df.columns]
                remaining_columns = [col for col in df.columns if col not in existing_columns]
                final_columns = existing_columns + remaining_columns
                df = df[final_columns]

                # Sort data
                df = df.sort_values(['state', 'city', 'source', 'scraped_at'])

                df.to_excel(final_file, index=False)
                self.logger.info(f"Final results saved to: {final_file}")

                # Summary
                self.logger.info("=" * 60)
                self.logger.info("PHASE 2 COMPLETED - PARALLEL BATCH SCRAPING SUMMARY")
                self.logger.info("=" * 60)
                self.logger.info(f"Successful workers: {successful_workers}, Failed workers: {failed_workers}")
                self.logger.info(f"Total records extracted: {len(self.all_extracted_data)}")

                # Summary by source
                aaok_count = len([r for r in self.all_extracted_data if r.get('source') == 'aaok'])
                aypapi_count = len([r for r in self.all_extracted_data if r.get('source') == 'aypapi'])
                self.logger.info(f"  - aaok.com: {aaok_count} records")
                self.logger.info(f"  - aypapi.com: {aypapi_count} records")

                # Summary by borough
                boroughs = list(set(r.get('city') for r in self.all_extracted_data))
                self.logger.info(f"NYC Boroughs processed: {len(boroughs)}")
                for borough in sorted(boroughs):
                    borough_count = len([r for r in self.all_extracted_data if r.get('city') == borough])
                    self.logger.info(f"  - {borough}: {borough_count} records")

                # Efficiency metrics
                actual_api_calls = sum(1 for r in self.all_extracted_data) // batch_size
                self.logger.info(f"Batch Processing Efficiency:")
                self.logger.info(f"  - Estimated API calls made: ~{actual_api_calls}")
                self.logger.info(f"  - vs Individual calls: {len(self.all_extracted_data)}")
                self.logger.info(f"  - Efficiency gain: ~{((len(self.all_extracted_data) - actual_api_calls) / len(self.all_extracted_data) * 100):.1f}%")

                # Show intermediate saves made
                total_saves = len(self.all_extracted_data) // self.save_interval
                self.logger.info(f"Intermediate saves: {total_saves} Excel files created during processing")

            except Exception as e:
                self.logger.error(f"Failed to save final results: {e}")
                return False

        return failed_workers == 0

def main():
    """Main entry point for parallel two-phase NYC scraper"""
    parser = argparse.ArgumentParser(description='Parallel Two-Phase NYC Boroughs Scraper')
    parser.add_argument('--phase', choices=['1', '2', 'both'], default='both',
                       help='Which phase to run: 1=extract URLs, 2=scrape pages, both=run both phases')
    parser.add_argument('--workers', type=int, default=5,
                       help='Number of parallel workers (default: 5)')
    parser.add_argument('--batch-size', type=int, default=10,
                       help='Number of pages per Mistral API batch for Phase 2 (default: 10)')
    parser.add_argument('--mistral-keys',
                       help='Comma-separated Mistral AI API keys (optional - defaults to built-in keys)')
    parser.add_argument('--clean', action='store_true',
                       help='Clean up existing files and start fresh')

    args = parser.parse_args()

    # Validate parameters
    if args.workers < 1 or args.workers > 10:
        print("Error: Number of workers must be between 1 and 10")
        return 1

    if args.batch_size < 1 or args.batch_size > 50:
        print("Error: Batch size must be between 1 and 50")
        return 1

    # Parse Mistral API keys
    mistral_keys = None
    if args.mistral_keys:
        mistral_keys = [key.strip() for key in args.mistral_keys.split(',')]
        print(f"Using {len(mistral_keys)} provided API keys")
    else:
        print("Using built-in API keys")

    # Clean files if requested
    if args.clean:
        files_to_clean = [
            "parallel_nyc_all_urls.json",
            "parallel_two_phase_nyc_final.xlsx",
            "parallel_phase2_*_checkpoint.xlsx"
        ]

        import glob
        for pattern in files_to_clean:
            for file in glob.glob(pattern):
                try:
                    os.remove(file)
                    print(f"Cleaned up: {file}")
                except Exception as e:
                    print(f"Failed to clean {file}: {e}")

        print("Cleanup completed. Starting fresh.")

    # Create scraper
    scraper = ParallelTwoPhaseNYCScraper(mistral_api_keys=mistral_keys, max_workers=args.workers)

    print("Parallel Two-Phase NYC Boroughs Scraper")
    print("=" * 50)
    print(f"Configuration: {args.workers} workers, {len(scraper.mistral_api_keys)} API keys, batch size {args.batch_size}")
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Features: Age ≤30, Women only, Parallel processing, Batch Mistral AI")
    print()

    start_time = time.time()
    success = True

    # Run Phase 1
    if args.phase in ['1', 'both']:
        print("Starting Phase 1: Parallel URL Extraction...")
        success = scraper.phase1_parallel_extract_urls()
        if not success:
            print("Phase 1 failed!")
            return 1
        print("Phase 1 completed successfully!")
        print()

    # Run Phase 2
    if args.phase in ['2', 'both']:
        print(f"Starting Phase 2: Parallel Batch Dedicated Page Scraping...")
        success = scraper.phase2_parallel_batch_scrape(batch_size=args.batch_size)
        if not success:
            print("Phase 2 failed!")
            return 1
        print("Phase 2 completed successfully!")

    end_time = time.time()
    processing_time = end_time - start_time

    print()
    print("=" * 50)
    print("PARALLEL TWO-PHASE SCRAPING COMPLETED!")
    print(f"Total processing time: {processing_time/60:.1f} minutes")
    print(f"Performance: {args.workers}x parallel workers + {args.batch_size}x batch processing")
    print("=" * 50)

    return 0

if __name__ == "__main__":
    sys.exit(main())
