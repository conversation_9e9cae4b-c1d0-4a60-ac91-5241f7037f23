#!/usr/bin/env python3
"""
Two-Phase City Scraper
Phase 1: Collect all search page URLs from specified cities
Phase 2: Scrape all individual profile URLs

Target Cities:
- South New Jersey
- Philadelphia, Pennsylvania
- Pittsburgh, Pennsylvania
- Wilmington, Delaware
- Dover, Delaware
- Baltimore, Maryland
- Annapolis, Maryland
"""

import sys
import os
import json
import time
import requests
import re
from typing import List, Dict, Set, Optional
from datetime import datetime
from bs4 import BeautifulSoup
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Fixed Mistral AI import
try:
    from mistralai.client import MistralClient
    from mistralai.models.chat_completion import ChatMessage
    MISTRAL_AVAILABLE = True
except ImportError:
    MISTRAL_AVAILABLE = False
    MistralClient = None
    ChatMessage = None

class TwoPhaseCityScraper:
    def __init__(self, scraperapi_key: str = "********************************",
                 mistral_api_key: str = "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"):
        """Initialize two-phase scraper"""

        # API Configuration
        self.scraperapi_key = scraperapi_key
        self.mistral_api_key = mistral_api_key
        self.scraperapi_url = "https://api.scraperapi.com/"

        # Initialize Mistral client
        self.mistral_client = None
        if mistral_api_key and MISTRAL_AVAILABLE:
            try:
                self.mistral_client = MistralClient(api_key=mistral_api_key)
                print("✅ Mistral AI client initialized")
            except Exception as e:
                print(f"⚠️ Mistral client failed: {e}")

        # Target cities configuration
        self.target_cities = [
            {"name": "South New Jersey", "state": "New Jersey", "url_name": "south%20new%20jersey"},
            {"name": "Philadelphia", "state": "Pennsylvania", "url_name": "philadelphia"},
            {"name": "Pittsburgh", "state": "Pennsylvania", "url_name": "pittsburgh"},
            {"name": "Wilmington", "state": "Delaware", "url_name": "wilmington"},
            {"name": "Dover", "state": "Delaware", "url_name": "dover"},
            {"name": "Baltimore", "state": "Maryland", "url_name": "baltimore"},
            {"name": "Annapolis", "state": "Maryland", "url_name": "annapolis"}
        ]

        # URL sources (no escortalligator as per conversation)
        self.url_sources = ['aaok', 'aypapi']

        # Phone deduplication
        self.known_phones: Set[str] = set()
        self.phone_to_data: Dict[str, Dict] = {}
        self.phone_lock = threading.Lock()

        # Statistics
        self.stats = {
            'search_pages_scraped': 0,
            'total_urls_found': 0,
            'profiles_scraped': 0,
            'duplicates_skipped': 0,
            'failed_extractions': 0,
            'api_requests': 0
        }

        # Results storage
        self.all_profile_urls = []
        self.scraped_profiles = []
        self.results_lock = threading.Lock()

        print(f"🚀 Two-Phase City Scraper initialized")
        print(f"🏙️ Target cities: {len(self.target_cities)}")
        print(f"📡 Sources: {', '.join(self.url_sources)}")

    def fetch_with_scraperapi(self, url: str, render_js: bool = False) -> Optional[str]:
        """Fetch URL using ScraperAPI"""
        try:
            params = {
                'api_key': self.scraperapi_key,
                'url': url,
                'render': 'true' if render_js else 'false',
                'country_code': 'us',
                'device_type': 'desktop',
                'premium': 'true'
            }

            response = requests.get(self.scraperapi_url, params=params, timeout=60)
            self.stats['api_requests'] += 1

            if response.status_code == 200:
                return response.text
            else:
                print(f"⚠️ ScraperAPI failed for {url}: Status {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ Error fetching {url}: {e}")
            return None

    def clean_phone_number(self, phone_str: str) -> Optional[str]:
        """Clean phone number for deduplication"""
        if not phone_str or pd.isna(phone_str):
            return None

        phone_clean = re.sub(r'[^\d]', '', str(phone_str))

        if len(phone_clean) < 10:
            return None

        if len(phone_clean) == 11 and phone_clean.startswith('1'):
            phone_clean = phone_clean[1:]
        elif len(phone_clean) > 11:
            return None

        return phone_clean

    def is_phone_duplicate(self, phone: str) -> bool:
        """Check if phone is already scraped"""
        cleaned = self.clean_phone_number(phone)
        if not cleaned:
            return False

        with self.phone_lock:
            return cleaned in self.known_phones

    def add_phone_to_database(self, phone: str, data: Dict):
        """Add phone to deduplication database"""
        cleaned = self.clean_phone_number(phone)
        if cleaned:
            with self.phone_lock:
                if cleaned not in self.known_phones:
                    self.known_phones.add(cleaned)
                    self.phone_to_data[cleaned] = data

    def extract_profile_urls_from_search(self, html: str, source: str, city: str, state: str) -> List[Dict]:
        """Extract profile URLs from search page with age filtering"""
        if not html:
            return []

        try:
            soup = BeautifulSoup(html, 'html.parser')
            profile_urls = []

            # Find all profile links
            links = soup.find_all('a', href=True)

            for link in links:
                href = link['href']
                if 'post/escorts' in href:
                    # Check for age information
                    age_found = False
                    age_value = None

                    # Search in link text and surrounding elements
                    context_text = link.get_text() + ' '
                    if link.parent:
                        context_text += link.parent.get_text()

                    age_matches = re.findall(r'(?:Age[:\s]*|^|\s)(\d{2})(?:\s|$)', context_text, re.IGNORECASE)
                    if age_matches:
                        try:
                            age_value = int(age_matches[0])
                            if 18 <= age_value <= 30:  # Age filter
                                age_found = True
                        except ValueError:
                            pass

                    # Include if age ≤30 or age not found (will filter later)
                    if not age_found or (age_value and age_value <= 30):
                        full_url = href
                        if href.startswith('/'):
                            full_url = f"https://{source}.com.listcrawler.eu{href}"

                        profile_urls.append({
                            'url': full_url,
                            'city': city,
                            'state': state,
                            'source': source,
                            'estimated_age': age_value
                        })

            # Remove duplicates
            seen_urls = set()
            unique_urls = []
            for url_data in profile_urls:
                if url_data['url'] not in seen_urls:
                    seen_urls.add(url_data['url'])
                    unique_urls.append(url_data)

            return unique_urls

        except Exception as e:
            print(f"❌ Error extracting URLs from {source} search page: {e}")
            return []

    def scrape_search_pages_for_city(self, city_info: Dict, max_pages: int = 20) -> List[Dict]:
        """Phase 1: Scrape all search pages for a city from both sources"""
        city_name = city_info['name']
        state_name = city_info['state']
        url_name = city_info['url_name']

        print(f"\n🏙️ Phase 1: Scraping search pages for {city_name}, {state_name}")

        all_urls = []

        for source in self.url_sources:
            print(f"   📡 Scraping {source.upper()} search pages...")

            state_url = state_name.lower().replace(' ', '%20')
            base_url = f"https://{source}.com.listcrawler.eu/brief/escorts/usa/{state_url}/{url_name}"

            page = 1
            consecutive_empty = 0

            while page <= max_pages and consecutive_empty < 3:
                search_url = f"{base_url}/{page}"
                print(f"      📄 Page {page}: {search_url}")

                html = self.fetch_with_scraperapi(search_url)
                if not html:
                    consecutive_empty += 1
                    page += 1
                    continue

                # Extract profile URLs from this search page
                page_urls = self.extract_profile_urls_from_search(html, source, city_name, state_name)

                if not page_urls:
                    consecutive_empty += 1
                    print(f"         ❌ No URLs found on page {page}")
                else:
                    consecutive_empty = 0
                    all_urls.extend(page_urls)
                    print(f"         ✅ Found {len(page_urls)} URLs on page {page}")

                self.stats['search_pages_scraped'] += 1
                page += 1
                time.sleep(1)  # Rate limiting

            source_count = len([u for u in all_urls if u['source'] == source])
            print(f"   ✅ {source.upper()}: {source_count} total URLs")

        print(f"🎯 {city_name} total: {len(all_urls)} profile URLs")
        self.stats['total_urls_found'] += len(all_urls)
        return all_urls

    def extract_profile_data_with_mistral(self, html: str, url_data: Dict) -> Optional[Dict]:
        """Phase 2: Extract profile data using Mistral AI"""
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Clean HTML
            for script in soup(["script", "style"]):
                script.decompose()

            text_content = soup.get_text()
            lines = (line.strip() for line in text_content.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text_content = ' '.join(chunk for chunk in chunks if chunk)

            # Basic regex extraction as fallback
            phone_match = re.search(r'(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})', text_content)
            age_match = re.search(r'(?:Age[:\s]*|I\'m\s|I\sam\s)(\d{2})', text_content, re.IGNORECASE)
            name_match = re.search(r'(?:I\'m\s|My name is\s|Call me\s)([A-Za-z]+)', text_content, re.IGNORECASE)

            # Try Mistral AI for enhanced extraction
            if self.mistral_client:
                try:
                    prompt = f"""
Extract data from this escort profile. Return ONLY a JSON object with these fields:
- "name": person's name (string or null)
- "age": age in years (integer or null)
- "phone": phone number (string or null)
- "description": brief description (string or null)
- "social_media": social media handles as object
- "email": email address (string or null)
- "is_female": true if female profile, false otherwise

Only include profiles for women aged 30 or under. Return null for all fields if male or over 30.

Profile text: {text_content[:2000]}
"""

                    messages = [ChatMessage(role="user", content=prompt)]
                    response = self.mistral_client.chat(
                        model="mistral-large-latest",
                        messages=messages,
                        temperature=0.1,
                        max_tokens=500
                    )

                    mistral_text = response.choices[0].message.content.strip()
                    json_match = re.search(r'\{.*\}', mistral_text, re.DOTALL)

                    if json_match:
                        mistral_data = json.loads(json_match.group())

                        if mistral_data.get('is_female') and mistral_data.get('phone'):
                            phone = mistral_data.get('phone')

                            # Check for duplicates
                            if self.is_phone_duplicate(phone):
                                self.stats['duplicates_skipped'] += 1
                                return None

                            result = {
                                'name': mistral_data.get('name', ''),
                                'age': mistral_data.get('age'),
                                'phone': phone,
                                'description': mistral_data.get('description', ''),
                                'social_media': json.dumps(mistral_data.get('social_media', {})),
                                'email': mistral_data.get('email', ''),
                                'city': url_data['city'],
                                'state': url_data['state'],
                                'source': url_data['source'],
                                'url': url_data['url'],
                                'post_id': self.extract_post_id(url_data['url']),
                                'scraped_at': datetime.now().isoformat()
                            }

                            self.add_phone_to_database(phone, result)
                            return result

                except Exception as e:
                    print(f"⚠️ Mistral AI failed for {url_data['url']}: {e}")

            # Fallback to regex
            if phone_match and age_match:
                phone = phone_match.group(1)
                age = int(age_match.group(1))

                if age > 30:
                    return None

                if self.is_phone_duplicate(phone):
                    self.stats['duplicates_skipped'] += 1
                    return None

                result = {
                    'name': name_match.group(1) if name_match else '',
                    'age': age,
                    'phone': phone,
                    'description': text_content[:200],
                    'social_media': '{}',
                    'email': '',
                    'city': url_data['city'],
                    'state': url_data['state'],
                    'source': url_data['source'],
                    'url': url_data['url'],
                    'post_id': self.extract_post_id(url_data['url']),
                    'scraped_at': datetime.now().isoformat()
                }

                self.add_phone_to_database(phone, result)
                return result

            return None

        except Exception as e:
            print(f"❌ Error extracting profile from {url_data['url']}: {e}")
            return None

    def extract_post_id(self, url: str) -> str:
        """Extract post ID from URL"""
        match = re.search(r'/(\d+)/?$', url)
        return match.group(1) if match else ''

    def scrape_profile_url(self, url_data: Dict) -> Optional[Dict]:
        """Phase 2: Scrape individual profile URL"""
        url = url_data['url']

        try:
            html = self.fetch_with_scraperapi(url, render_js=True)
            if not html:
                self.stats['failed_extractions'] += 1
                return None

            profile_data = self.extract_profile_data_with_mistral(html, url_data)
            if profile_data:
                self.stats['profiles_scraped'] += 1
                return profile_data
            else:
                self.stats['failed_extractions'] += 1
                return None

        except Exception as e:
            print(f"❌ Error scraping {url}: {e}")
            self.stats['failed_extractions'] += 1
            return None

    def phase1_collect_all_urls(self) -> List[Dict]:
        """Phase 1: Collect all profile URLs from all cities"""
        print("🚀 PHASE 1: Collecting all search page URLs")
        print("=" * 60)

        all_urls = []

        for city_info in self.target_cities:
            city_urls = self.scrape_search_pages_for_city(city_info)
            all_urls.extend(city_urls)

            # Save intermediate results
            city_filename = f"phase1_urls_{city_info['name'].replace(' ', '_')}_{city_info['state'].replace(' ', '_')}.json"
            with open(city_filename, 'w') as f:
                json.dump(city_urls, f, indent=2)
            print(f"💾 Saved {len(city_urls)} URLs to {city_filename}")

        # Save all URLs
        all_urls_filename = f"phase1_all_urls_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(all_urls_filename, 'w') as f:
            json.dump(all_urls, f, indent=2)

        print(f"\n🎯 PHASE 1 COMPLETE")
        print(f"✅ Total URLs collected: {len(all_urls)}")
        print(f"💾 All URLs saved to: {all_urls_filename}")
        print(f"📊 Search pages scraped: {self.stats['search_pages_scraped']}")
        print(f"🌐 API requests made: {self.stats['api_requests']}")

        return all_urls

    def phase2_scrape_all_profiles(self, profile_urls: List[Dict], max_workers: int = 3) -> List[Dict]:
        """Phase 2: Scrape all individual profile URLs"""
        print(f"\n🚀 PHASE 2: Scraping {len(profile_urls)} individual profiles")
        print("=" * 60)

        results = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_url = {executor.submit(self.scrape_profile_url, url_data): url_data for url_data in profile_urls}

            completed = 0
            for future in as_completed(future_to_url):
                url_data = future_to_url[future]
                result = future.result()

                if result:
                    with self.results_lock:
                        results.append(result)
                        self.scraped_profiles.append(result)

                completed += 1
                if completed % 10 == 0:
                    print(f"📊 Progress: {completed}/{len(profile_urls)} URLs processed")
                    print(f"   ✅ Valid profiles: {len(results)}")
                    print(f"   🔄 Duplicates: {self.stats['duplicates_skipped']}")
                    print(f"   ❌ Failed: {self.stats['failed_extractions']}")

        print(f"\n🎯 PHASE 2 COMPLETE")
        print(f"✅ Valid profiles scraped: {len(results)}")
        print(f"🔄 Duplicates skipped: {self.stats['duplicates_skipped']}")
        print(f"❌ Failed extractions: {self.stats['failed_extractions']}")

        return results

    def save_results_to_excel(self, results: List[Dict], filename: str):
        """Save results to Excel with proper formatting"""
        if not results:
            print("⚠️ No results to save")
            return

        df = pd.DataFrame(results)

        # Column order
        column_order = ['state', 'city', 'source', 'name', 'age', 'phone', 'description',
                       'social_media', 'email', 'url', 'post_id', 'scraped_at']

        df = df.reindex(columns=[col for col in column_order if col in df.columns] +
                               [col for col in df.columns if col not in column_order])

        df.to_excel(filename, index=False)
        print(f"💾 Results saved to {filename}")

        # Print statistics
        print(f"\n📊 FINAL STATISTICS")
        print(f"=" * 40)
        print(f"🏙️ Cities processed: {df['city'].nunique()}")
        print(f"🗺️ States processed: {df['state'].nunique()}")
        print(f"📡 Sources used: {', '.join(df['source'].unique())}")
        print(f"📱 Unique phone numbers: {len(self.known_phones)}")
        print(f"👥 Age range: {df['age'].min()}-{df['age'].max()}")
        print(f"🌐 Total API requests: {self.stats['api_requests']}")

    def run_full_two_phase_scraper(self, max_workers: int = 3):
        """Run complete two-phase scraping process"""
        try:
            # Phase 1: Collect all URLs
            all_urls = self.phase1_collect_all_urls()

            if not all_urls:
                print("❌ No URLs collected in Phase 1")
                return

            # Phase 2: Scrape all profiles
            results = self.phase2_scrape_all_profiles(all_urls, max_workers)

            if results:
                # Save results
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"two_phase_results_{len(results)}_profiles_{timestamp}.xlsx"
                self.save_results_to_excel(results, filename)

                print(f"\n🎉 TWO-PHASE SCRAPING COMPLETE!")
                print(f"📋 {len(results)} profiles scraped from {len(self.target_cities)} cities")
                print(f"💾 Results saved to: {filename}")
            else:
                print("⚠️ No valid profiles found")

        except KeyboardInterrupt:
            print("\n🛑 Scraping interrupted by user")
            if self.scraped_profiles:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"two_phase_interrupted_{len(self.scraped_profiles)}_profiles_{timestamp}.xlsx"
                self.save_results_to_excel(self.scraped_profiles, filename)

        except Exception as e:
            print(f"❌ Scraping failed: {e}")
            if self.scraped_profiles:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"two_phase_error_{len(self.scraped_profiles)}_profiles_{timestamp}.xlsx"
                self.save_results_to_excel(self.scraped_profiles, filename)

def main():
    """Main execution"""
    import argparse

    parser = argparse.ArgumentParser(description='Two-Phase City Scraper')
    parser.add_argument('--phase1-only', action='store_true', help='Only run Phase 1 (collect URLs)')
    parser.add_argument('--phase2-only', help='Only run Phase 2 with existing URLs file')
    parser.add_argument('--workers', type=int, default=3, help='Number of worker threads for Phase 2')

    args = parser.parse_args()

    scraper = TwoPhaseCityScraper()

    if args.phase1_only:
        # Only collect URLs
        all_urls = scraper.phase1_collect_all_urls()
        print(f"✅ Phase 1 complete. {len(all_urls)} URLs collected.")

    elif args.phase2_only:
        # Only scrape profiles from existing URLs file
        try:
            with open(args.phase2_only, 'r') as f:
                all_urls = json.load(f)
            print(f"📁 Loaded {len(all_urls)} URLs from {args.phase2_only}")

            results = scraper.phase2_scrape_all_profiles(all_urls, args.workers)

            if results:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"phase2_only_results_{len(results)}_profiles_{timestamp}.xlsx"
                scraper.save_results_to_excel(results, filename)

        except FileNotFoundError:
            print(f"❌ URLs file not found: {args.phase2_only}")
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON in file: {args.phase2_only}")

    else:
        # Run full two-phase process
        scraper.run_full_two_phase_scraper(args.workers)

if __name__ == "__main__":
    main()
