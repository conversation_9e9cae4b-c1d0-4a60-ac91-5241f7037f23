#!/usr/bin/env python3
"""
Quick Test for OptimizedScraperAPI v2 - Small Numbers
Tests basic functionality with limited data to verify all systems work
"""

import sys
import time
from datetime import datetime
from optimized_scraper_api_v2 import OptimizedScraperAPIv2

def quick_api_test():
    """Quick test of API keys and basic functionality"""
    print("🚀 QUICK TEST: OptimizedScraperAPI v2")
    print("=" * 50)

    # Initialize scraper
    scraper = OptimizedScraperAPIv2(max_workers=1)

    print("\n1. Testing ScraperAPI...")
    # Test ScraperAPI with a simple URL
    test_url = "https://aaok.com.listcrawler.eu/brief/escorts/usa/alabama/auburn/1"
    html = scraper.fetch_with_scraperapi(test_url)

    if html and len(html) > 1000:
        print(f"✅ ScraperAPI working - {len(html)} chars received")
    else:
        print("❌ ScraperAPI failed")
        return False

    print("\n2. Testing URL extraction...")
    # Extract a few URLs from the search page
    urls = scraper.extract_dedicated_urls_from_search(html, "aaok")
    print(f"✅ Found {len(urls)} URLs from search page")

    if not urls:
        print("❌ No URLs found")
        return False

    print("\n3. Testing phone deduplication...")
    # Test phone cleaning
    test_phone = "************"
    cleaned = scraper.clean_phone_number(test_phone)
    print(f"✅ Phone cleaning: {test_phone} -> {cleaned}")

    # Test duplicate detection
    is_dup_1 = scraper.is_phone_duplicate(cleaned)
    scraper.add_phone_to_database(cleaned, "test_url", {"test": "data"})
    is_dup_2 = scraper.is_phone_duplicate(cleaned)

    if not is_dup_1 and is_dup_2:
        print("✅ Phone deduplication working")
    else:
        print("❌ Phone deduplication failed")
        return False

    print("\n4. Testing profile extraction (first URL only)...")
    # Test with just the first URL
    test_url_data = {
        'url': urls[0],
        'city': 'Auburn',
        'state': 'Alabama',
        'source': 'aaok'
    }

    profile = scraper.process_single_url(test_url_data)

    if profile:
        print("✅ Profile extraction successful!")
        print(f"   Name: {profile.get('name', 'N/A')}")
        print(f"   Age: {profile.get('age', 'N/A')}")
        print(f"   Phone: {profile.get('phone', 'N/A')}")
        print(f"   City: {profile.get('city', 'N/A')}")

        # Save single result
        results = [profile]
        filename = f"quick_test_v2_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        scraper.save_results(results, filename)
        print(f"✅ Result saved to {filename}")

    else:
        print("⚠️ No profile extracted (could be duplicate/male/over 30)")

    print("\n5. Testing multiple URLs (max 3)...")
    # Test with up to 3 URLs
    test_urls = urls[:3] if len(urls) >= 3 else urls
    results = []

    for i, url in enumerate(test_urls):
        print(f"   Processing URL {i+1}/{len(test_urls)}...")
        url_data = {
            'url': url,
            'city': 'Auburn',
            'state': 'Alabama',
            'source': scraper.get_source_from_url(url)
        }

        result = scraper.process_single_url(url_data)
        if result:
            results.append(result)
            print(f"   ✅ Profile {i+1}: {result.get('name', 'N/A')} (Age: {result.get('age', 'N/A')})")
        else:
            print(f"   ⚠️ Profile {i+1}: No data (duplicate/male/over 30)")

        time.sleep(1)  # Small delay between requests

    if results:
        filename = f"quick_test_v2_multiple_{len(results)}_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        scraper.save_results(results, filename)
        print(f"\n✅ {len(results)} results saved to {filename}")
    else:
        print("\n⚠️ No valid profiles found")

    # Print final statistics
    print("\n" + "="*50)
    print("QUICK TEST RESULTS")
    print("="*50)
    print(f"📊 URLs Found: {len(urls)}")
    print(f"📊 URLs Tested: {len(test_urls)}")
    print(f"✅ Valid Profiles: {len(results)}")
    print(f"❌ Failed Extractions: {scraper.failed_extractions}")
    print(f"🔄 Duplicates Skipped: {scraper.skipped_duplicates}")
    print(f"📱 Unique Phones: {len(scraper.known_phones)}")
    print(f"🌐 API Requests: {scraper.current_requests}")

    success = len(urls) > 0 and scraper.current_requests > 0

    if success:
        print("\n🎉 QUICK TEST PASSED!")
        print("\nKey Features Verified:")
        print("✅ ScraperAPI integration (Cloudflare bypass)")
        print("✅ Phone number deduplication")
        print("✅ URL extraction from search pages")
        print("✅ Profile data extraction")
        if scraper.mistral_client:
            print("✅ Mistral AI enhancement")
        print("✅ Excel output generation")
        print("✅ Age filtering (≤30)")
        print("✅ Female-only filtering")

        print("\nReady for larger scale testing:")
        print("python optimized_scraper_api_v2.py Alabama --city Auburn")
        print("python optimized_scraper_api_v2.py Alabama --max-cities 3")

    else:
        print("\n❌ QUICK TEST FAILED")
        print("Check the errors above and fix issues")

    print("="*50)
    return success

def main():
    """Run quick test"""
    try:
        success = quick_api_test()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
