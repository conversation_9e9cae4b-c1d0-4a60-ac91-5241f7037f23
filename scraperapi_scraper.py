#!/usr/bin/env python3
"""
ScraperAPI Enhanced Scraper - Uses ScraperAPI to handle anti-bot protection
Bypasses JavaScript rendering and Cloudflare protection automatically
"""

import json
import os
import time
import argparse
import requests
import random
from datetime import datetime
from typing import List, Dict, Optional
import pandas as pd
import re
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from collections import Counter

class ScraperAPIScraper:
    def __init__(self, api_keys: List[str], max_workers: int = 3, enable_social_media: bool = True, mistral_api_key: str = None):
        self.api_keys = api_keys
        self.current_key_index = 0
        self.requests_with_current_key = 0
        self.max_requests_per_key = 4500  # Switch to next key after 4500 requests
        self.key_lock = threading.Lock()  # Thread-safe key rotation

        self.max_workers = max_workers
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results = []
        self.results_lock = threading.Lock()
        self.processed_count = 0

        # ScraperAPI settings
        self.scraperapi_url = "https://api.scraperapi.com/"

        # Social media extraction settings
        self.enable_social_media = enable_social_media
        self.mistral_api_key = mistral_api_key
        if self.enable_social_media and self.mistral_api_key:
            self.mistral_base_url = "https://api.mistral.ai/v1/chat/completions"
            self.mistral_headers = {
                "Authorization": f"Bearer {self.mistral_api_key}",
                "Content-Type": "application/json"
            }

        # Rate limiting (ScraperAPI handles most of this, but we'll be conservative)
        self.min_delay = 0.5  # Minimum delay between requests
        self.max_delay = 1.5  # Maximum delay between requests
        self.last_request_time = {}  # Track last request time per worker

    def get_current_api_key(self) -> str:
        """Get current API key and rotate if needed"""
        with self.key_lock:
            # Check if we need to rotate to next key
            if self.requests_with_current_key >= self.max_requests_per_key:
                if self.current_key_index < len(self.api_keys) - 1:
                    self.current_key_index += 1
                    self.requests_with_current_key = 0
                    print(f"🔄 Rotated to API key {self.current_key_index + 1}/{len(self.api_keys)}")
                else:
                    print(f"⚠️  All API keys used up to limit. Continuing with last key...")

            # Increment request count
            self.requests_with_current_key += 1
            current_key = self.api_keys[self.current_key_index]

            return current_key

    def fetch_with_scraperapi(self, url: str, worker_id: str) -> Optional[str]:
        """Fetch URL using ScraperAPI with proper error handling"""

        # Rate limiting per worker
        current_time = time.time()
        if worker_id in self.last_request_time:
            time_since_last = current_time - self.last_request_time[worker_id]
            min_interval = random.uniform(self.min_delay, self.max_delay)
            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                time.sleep(sleep_time)

        self.last_request_time[worker_id] = time.time()

        max_retries = 3
        for attempt in range(max_retries):
            try:
                # Get current API key (with rotation)
                api_key = self.get_current_api_key()

                print(f"Worker {worker_id}: Fetching {url} via ScraperAPI (attempt {attempt + 1}/{max_retries}) [Key {self.current_key_index + 1}]")

                # ScraperAPI parameters
                payload = {
                    'api_key': api_key,
                    'url': url,
                    'render': 'false',  # Set to 'true' if JavaScript rendering needed
                    'country_code': 'us',
                    'premium': 'true',  # Use premium proxies for better success rate
                    'session_number': random.randint(1, 1000)  # Rotate sessions
                }

                # Make request to ScraperAPI
                response = requests.get(
                    self.scraperapi_url,
                    params=payload,
                    timeout=60  # ScraperAPI can be slower due to proxy routing
                )

                if response.status_code == 200:
                    html = response.text

                    if len(html) < 500:
                        print(f"Worker {worker_id}: Content too short ({len(html)} chars), retrying...")
                        if attempt < max_retries - 1:
                            time.sleep(random.uniform(5, 10))
                            continue

                    # Check for anti-bot protection (should be rare with ScraperAPI)
                    protection_indicators = [
                        "Just a moment",
                        "Enable JavaScript and cookies",
                        "Checking your browser",
                        "Please wait while we verify"
                    ]

                    if any(indicator in html for indicator in protection_indicators):
                        print(f"Worker {worker_id}: Anti-bot protection still present, retrying with render=true...")
                        # Retry with JavaScript rendering
                        payload['render'] = 'true'
                        if attempt < max_retries - 1:
                            time.sleep(random.uniform(10, 15))
                            continue

                    print(f"Worker {worker_id}: ✅ Successfully fetched {len(html)} characters")
                    return html

                elif response.status_code == 422:
                    print(f"Worker {worker_id}: ❌ ScraperAPI error 422 - Invalid request parameters")
                    return None
                elif response.status_code == 429:
                    print(f"Worker {worker_id}: ⚠️ Rate limit exceeded, waiting...")
                    if attempt < max_retries - 1:
                        time.sleep(random.uniform(30, 60))
                        continue
                elif response.status_code == 500:
                    print(f"Worker {worker_id}: ⚠️ ScraperAPI server error, retrying...")
                    if attempt < max_retries - 1:
                        time.sleep(random.uniform(10, 20))
                        continue
                else:
                    print(f"Worker {worker_id}: ❌ HTTP {response.status_code}: {response.text[:200]}")
                    if attempt < max_retries - 1:
                        time.sleep(random.uniform(5, 10))
                        continue

            except requests.exceptions.Timeout:
                print(f"Worker {worker_id}: ⚠️ Request timeout, retrying...")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(10, 15))
                    continue
            except requests.exceptions.RequestException as e:
                print(f"Worker {worker_id}: ❌ Request error: {str(e)[:100]}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(5, 10))
                    continue
            except Exception as e:
                print(f"Worker {worker_id}: ❌ Unexpected error: {str(e)[:100]}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(5, 10))
                    continue

        print(f"Worker {worker_id}: ❌ Failed to fetch {url} after {max_retries} attempts")
        return None

    def extract_social_media(self, description: str) -> str:
        """Extract social media platforms from description using Mistral AI"""
        if not self.enable_social_media or not self.mistral_api_key:
            return "None"

        if not description or str(description).lower() in ['nan', '', 'none']:
            return "None"

        try:
            prompt = f"""
You are a social media content analyzer. Identify if this description mentions any social media platforms or related activities.

Look for mentions of: Instagram, X (formerly Twitter), Twitter, OnlyFans, OF, Snapchat, Video Cam, Media, TikTok, YouTube, Facebook, LinkedIn, webcam, streaming, content creation, social media influencer, cam girl, cam boy, live streaming, video chat, FaceTime, WhatsApp, Telegram, etc.

Return ONLY the social media platforms found (comma-separated) or "None" if no social media is mentioned.

Description to analyze: "{str(description)[:500]}"

Return ONLY a simple response like: "Instagram, OnlyFans" or "None"
"""

            payload = {
                "model": "mistral-large-latest",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.1,
                "max_tokens": 100
            }

            response = requests.post(
                self.mistral_base_url,
                headers=self.mistral_headers,
                json=payload,
                timeout=15
            )

            if response.status_code == 200:
                result = response.json()
                social_media = result['choices'][0]['message']['content'].strip()
                return social_media if social_media else "None"
            else:
                return "None"

        except Exception as e:
            print(f"Social media extraction error: {e}")
            return "None"

    def extract_data_from_html(self, html: str, url: str) -> Dict:
        """Extract profile data from HTML using proper selectors"""

        data = {
            'url': url,
            'name': '',
            'age': '',
            'phone': '',
            'location': '',
            'description': '',
            'website_type': '',
            'city': '',
            'social': '',
            'extraction_success': False,
            'scraped_at': datetime.now().isoformat(),
            'content_length': len(html)
        }

        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Detect website type
            domain = url.lower()
            if 'aypapi' in domain:
                data['website_type'] = 'aypapi'
            elif 'aaok' in domain:
                data['website_type'] = 'aaok'
            else:
                data['website_type'] = 'unknown'

            # Extract name - first try viewpostname, then fallback to viewposttitle
            name_elem = soup.select_one('.viewpostname')
            if name_elem:
                # First try to get text after the Nym: span
                nym_span = name_elem.select_one('span')
                if nym_span:
                    # Get all text after the span
                    name_text = name_elem.get_text(strip=True)
                    span_text = nym_span.get_text(strip=True)
                    if span_text in name_text:
                        extracted_name = name_text.replace(span_text, '').strip()
                        if extracted_name:  # Only use if not empty
                            data['name'] = extracted_name
                else:
                    # Fallback to full text content
                    name_text = name_elem.get_text(strip=True)
                    if 'Nym:' in name_text:
                        extracted_name = name_text.replace('Nym:', '').strip()
                        if extracted_name:  # Only use if not empty
                            data['name'] = extracted_name
                    elif name_text:
                        data['name'] = name_text

            # If name is still empty, try to extract from title
            if not data['name']:
                title_elem = soup.select_one('.viewposttitle')
                if title_elem:
                    title_text = title_elem.get_text(strip=True)
                    # Remove age from title to get name
                    age_elem = soup.select_one('.postTitleAge')
                    if age_elem:
                        age = age_elem.get_text(strip=True)
                        # Remove age and dash pattern from title
                        title_without_age = title_text.replace(f' - {age}', '').replace(f'- {age}', '').replace(f' -{age}', '').replace(f'-{age}', '').strip()
                        if title_without_age:
                            data['name'] = title_without_age
                    else:
                        # No age found, use title as is but limit length
                        if title_text:
                            data['name'] = title_text[:100].strip()

            # Extract age from postTitleAge span
            age_elem = soup.select_one('.postTitleAge')
            if age_elem:
                age_text = age_elem.get_text(strip=True)
                if age_text.isdigit() and 18 <= int(age_text) <= 65:
                    data['age'] = age_text

            # Extract phone from viewposttelephone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                if phone_clean.strip():
                    data['phone'] = phone_clean.strip()

            # Extract location from the second li in viewpostlocationIconBabylon
            location_container = soup.select_one('.viewpostlocationIconBabylon ul')
            if location_container:
                li_elements = location_container.select('li')
                if len(li_elements) >= 2:
                    location_text = li_elements[1].get_text(strip=True)
                    # Remove "Location:" prefix if present
                    if 'Location:' in location_text:
                        data['location'] = location_text.replace('Location:', '').strip()
                    else:
                        data['location'] = location_text

            # Extract description from viewpostbody
            body_elem = soup.select_one('.viewpostbody')
            if body_elem:
                data['description'] = body_elem.get_text(strip=True)[:500]

            # Get page title and extract city (2nd word)
            title = soup.title
            if title:
                page_title = title.get_text(strip=True)
                data['page_title'] = page_title

                # Extract city from page title (2nd word)
                words = page_title.split()
                if len(words) >= 2:
                    data['city'] = words[1]  # Take the 2nd word as city

            # Extract social media information from description
            if data['description'] and self.enable_social_media:
                data['social'] = self.extract_social_media(data['description'])
            else:
                data['social'] = "None"

            # Check if we got meaningful data
            if data['name'] or data['age'] or data['phone']:
                data['extraction_success'] = True

            # Additional validation - check for actual profile content
            if data['extraction_success']:
                # Look for profile indicators to confirm this is a real profile page
                profile_indicators = ['escort', 'profile', 'contact', 'call', 'text']
                page_text = soup.get_text().lower()
                if not any(indicator in page_text for indicator in profile_indicators):
                    data['extraction_success'] = False
                    data['error'] = 'No profile content detected'

        except Exception as e:
            print(f"❌ Error extracting data from {url}: {e}")
            data['error'] = str(e)

        return data

    def process_url_batch(self, urls: List[str], worker_id: str) -> List[Dict]:
        """Process a batch of URLs"""
        results = []

        print(f"Worker {worker_id}: Starting batch of {len(urls)} URLs")

        for i, url in enumerate(urls):
            print(f"\nWorker {worker_id}: Processing {i+1}/{len(urls)}: {url}")

            # Fetch HTML using ScraperAPI
            html = self.fetch_with_scraperapi(url, worker_id)

            if html:
                # Extract data
                data = self.extract_data_from_html(html, url)
                results.append(data)

                if data['extraction_success']:
                    print(f"Worker {worker_id}: ✅ SUCCESS")
                    print(f"    Name: '{data['name']}'")
                    print(f"    Age: {data['age']}")
                    print(f"    Phone: '{data['phone']}'")
                    print(f"    Type: {data['website_type']}")
                else:
                    print(f"Worker {worker_id}: ❌ No meaningful data extracted")
                    if 'error' in data:
                        print(f"    Error: {data['error']}")
            else:
                # Add failed entry
                failed_data = {
                    'url': url,
                    'extraction_success': False,
                    'error': 'Failed to fetch page content via ScraperAPI',
                    'scraped_at': datetime.now().isoformat()
                }
                results.append(failed_data)
                print(f"Worker {worker_id}: ❌ Failed to fetch content")

            # Add results to main collection and save periodically
            with self.results_lock:
                self.results.append(data if html else failed_data)
                self.processed_count += 1

                # Save intermediate results every 1000 URLs
                if self.processed_count % 1000 == 0:
                    self.save_results(self.results, f"checkpoint_{self.processed_count}")
                    print(f"\n💾 CHECKPOINT: Saved {self.processed_count} URLs")

        successful = len([r for r in results if r.get('extraction_success', False)])
        print(f"Worker {worker_id}: ✅ Batch completed: {successful}/{len(results)} successful")
        return results

    def process_urls_parallel(self, urls: List[str], max_urls: Optional[int] = None) -> List[Dict]:
        """Process URLs using multiple workers"""

        if max_urls:
            urls = urls[:max_urls]

        print(f"🚀 Starting ScraperAPI processing of {len(urls)} URLs with {self.max_workers} workers")
        print(f"🔑 Using {len(self.api_keys)} API keys with {self.max_requests_per_key} requests per key")
        print(f"🔑 Starting with key: {self.api_keys[0][:10]}...")

        # Split URLs into batches for workers
        batch_size = max(1, len(urls) // self.max_workers)
        url_batches = []

        for i in range(0, len(urls), batch_size):
            batch = urls[i:i + batch_size]
            url_batches.append(batch)

        # Ensure we don't exceed max_workers
        while len(url_batches) > self.max_workers:
            extra_batch = url_batches.pop()
            url_batches[-1].extend(extra_batch)

        print(f"📊 Split into {len(url_batches)} batches: {[len(batch) for batch in url_batches]}")

        # Process batches in parallel
        all_results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_batch = {
                executor.submit(self.process_url_batch, batch, f"api-{i}"): batch
                for i, batch in enumerate(url_batches)
            }

            # Collect results
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    print(f"✅ Completed batch of {len(batch)} URLs")
                except Exception as e:
                    print(f"❌ Worker batch failed: {e}")

        return all_results

    def generate_social_media_summary(self, results: List[Dict]) -> Dict:
        """Generate summary statistics for social media data"""
        social_found = [r for r in results if r.get('social') and r['social'] != 'None' and r['social'] != '']

        # Count all platforms
        all_platforms = []
        for result in social_found:
            social = result.get('social', '')
            if social and social != 'None':
                platforms = [p.strip() for p in str(social).split(',')]
                all_platforms.extend(platforms)

        platform_counts = Counter(all_platforms)

        return {
            'total_records': len(results),
            'with_social_media': len(social_found),
            'social_media_percentage': (len(social_found) / len(results) * 100) if results else 0,
            'top_platforms': platform_counts.most_common(10)
        }

    def process_phone_numbers(self, results: List[Dict]) -> List[Dict]:
        """Process phone numbers: clean and add country code"""
        print(f"\n📱 Processing phone numbers for {len(results)} records...")

        for result in results:
            if result.get('phone'):
                original_phone = result['phone']

                # Step 1: Clean phone number (remove non-digits)
                clean_phone = re.sub(r'[^0-9]', '', str(original_phone))

                # Step 2: Add country code '1' if not already present
                if clean_phone and not clean_phone.startswith('1'):
                    clean_phone = '1' + clean_phone

                result['phone'] = clean_phone

        return results

    def update_locations_with_city(self, results: List[Dict]) -> List[Dict]:
        """Update location field with city information"""
        print(f"\n🏙️ Updating locations with city data for {len(results)} records...")

        for result in results:
            if result.get('city') and result.get('location'):
                original_location = result['location']
                if original_location and str(original_location).lower() != 'nan':
                    result['location'] = f"{original_location}, {result['city']}"
                else:
                    result['location'] = result['city']

        return results

    def save_results(self, results: List[Dict], suffix: str = "final"):
        """Save results to Excel file with detailed analysis"""

        if not results:
            print("⚠️  No results to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scraperapi_results_{len(results)}_urls_{timestamp}_{suffix}.xlsx"

        try:
            df = pd.DataFrame(results)

            # Reorder columns
            column_order = [
                'url', 'name', 'age', 'phone', 'location', 'description',
                'website_type', 'extraction_success', 'page_title', 'content_length', 'scraped_at', 'error'
            ]

            # Only include columns that exist
            existing_columns = [col for col in column_order if col in df.columns]
            df = df[existing_columns]

            # Create multiple sheets
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # All data
                df.to_excel(writer, sheet_name='All_Data', index=False)

                # Successful extractions only
                successful_df = df[df['extraction_success'] == True]
                if not successful_df.empty:
                    successful_df.to_excel(writer, sheet_name='Successful', index=False)

                # Failed extractions
                failed_df = df[df['extraction_success'] == False]
                if not failed_df.empty:
                    failed_df.to_excel(writer, sheet_name='Failed', index=False)

                # AAOK vs AYPAPI breakdown
                aaok_df = df[df['website_type'] == 'aaok']
                aypapi_df = df[df['website_type'] == 'aypapi']

                if not aaok_df.empty:
                    aaok_df.to_excel(writer, sheet_name='AAOK_Sites', index=False)
                if not aypapi_df.empty:
                    aypapi_df.to_excel(writer, sheet_name='AYPAPI_Sites', index=False)

                # Statistics sheet
                stats_data = {
                    'Metric': [
                        'Total URLs Processed',
                        'Successful Extractions',
                        'Failed Extractions',
                        'Success Rate (%)',
                        'AAOK URLs',
                        'AYPAPI URLs',
                        'Unknown Type URLs',
                        'URLs with Names',
                        'URLs with Ages',
                        'URLs with Phones',
                        'URLs with Descriptions',
                        'Average Content Length',
                        'API Keys Used',
                        'Current Key Index',
                        'Requests with Current Key',
                        'ScraperAPI Cost Estimate ($)'
                    ],
                    'Value': [
                        len(df),
                        len(successful_df),
                        len(failed_df),
                        f"{(len(successful_df) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                        len(df[df['website_type'] == 'aaok']),
                        len(df[df['website_type'] == 'aypapi']),
                        len(df[df['website_type'] == 'unknown']),
                        len(df[df['name'].notna() & (df['name'].str.strip() != '')]),
                        len(df[df['age'].notna() & (df['age'].str.strip() != '')]),
                        len(df[df['phone'].notna() & (df['phone'].str.strip() != '')]),
                        len(df[df['description'].notna() & (df['description'].str.strip() != '')]),
                        f"{df['content_length'].mean():.0f}" if 'content_length' in df.columns else 'N/A',
                        len(self.api_keys),
                        self.current_key_index + 1,
                        self.requests_with_current_key,
                        f"{len(df) * 0.001:.3f}"  # Assuming $0.001 per request
                    ]
                }
                pd.DataFrame(stats_data).to_excel(writer, sheet_name='Statistics', index=False)

            # Calculate and display statistics
            successful = len(successful_df)
            failed = len(failed_df)
            success_rate = (successful / len(df)) * 100 if len(df) > 0 else 0

            print(f"\n💾 SAVED: {filename}")
            print(f"📊 RESULTS SUMMARY:")
            print(f"   Total URLs: {len(df)}")
            print(f"   Successful: {successful} ({success_rate:.1f}%)")
            print(f"   Failed: {failed}")
            print(f"   AAOK sites: {len(df[df['website_type'] == 'aaok'])}")
            print(f"   AYPAPI sites: {len(df[df['website_type'] == 'aypapi'])}")

            # Show sample successful extractions
            if not successful_df.empty:
                print(f"\n✅ SAMPLE SUCCESSFUL EXTRACTIONS:")
                for i, row in successful_df.head(5).iterrows():
                    name = row.get('name', 'N/A')
                    age = row.get('age', 'N/A')
                    phone = row.get('phone', 'N/A')
                    website = row.get('website_type', 'N/A')
                    print(f"   • {name} | Age: {age} | Phone: {phone} | Type: {website}")

            # Show failure analysis
            if not failed_df.empty and 'error' in failed_df.columns:
                error_counts = failed_df['error'].value_counts()
                print(f"\n❌ TOP FAILURE REASONS:")
                for error, count in error_counts.head(5).items():
                    print(f"   • {error}: {count} URLs")

            # Cost estimate
            estimated_cost = len(df) * 0.001  # $0.001 per request (premium)
            print(f"\n💰 Estimated ScraperAPI cost: ${estimated_cost:.3f}")

        except Exception as e:
            print(f"❌ Error saving results: {e}")

def main():
    parser = argparse.ArgumentParser(description="ScraperAPI Enhanced Scraper with Multiple Keys")
    parser.add_argument('--max-urls', type=int, help='Maximum URLs to process')
    parser.add_argument('--workers', type=int, default=4, help='Number of parallel workers (default: 4)')
    parser.add_argument('--resume', type=str, help='Resume from checkpoint file')
    parser.add_argument('--disable-social', action='store_true', help='Disable social media extraction')

    args = parser.parse_args()

    # Multiple API keys - each will be used for up to 4500 requests
    api_keys = [
        'ac8efce1d3b1a9b7d2ccfcf48c261069',
        'd3768c1360e993fe0d2e8b4a817acd86',
        'b8ff5d207ea6a6841e7621c1f1d6ae2d',
        'e0d3b6961693cdd4bd0e14f5bfc33c42',
        '0f19b6a92c79c17d90d4b9f7f6a567cc',
        '8a6d37488b145a4890fe0adc2e8e12d0'
    ]

    # Mistral AI API key for social media extraction
    mistral_api_key = "nsgxaPTwCJ3jc69RjD7RaQrNuq3l9O6z"

    print(f"🔑 Loaded {len(api_keys)} ScraperAPI keys")
    print(f"📊 Total capacity: {len(api_keys) * 4500} requests")
    print(f"🧠 Social media extraction: {'Enabled' if mistral_api_key else 'Disabled'}")

    scraper = ScraperAPIScraper(
        api_keys=api_keys,
        max_workers=args.workers,
        enable_social_media=True,
        mistral_api_key=mistral_api_key
    )

    # Check for resume
    processed_urls = set()
    if args.resume:
        if os.path.exists(args.resume):
            print(f"📂 Resuming from checkpoint: {args.resume}")
            checkpoint_df = pd.read_excel(args.resume)
            processed_urls = set(checkpoint_df['url'].tolist())
            print(f"📊 Found {len(processed_urls)} already processed URLs")
        else:
            print(f"❌ Resume file not found: {args.resume}")
            return False

    # Load URLs
    if not os.path.exists(scraper.urls_file):
        print(f"❌ URLs file not found: {scraper.urls_file}")
        return False

    print(f"📂 Loading URLs from {scraper.urls_file}")
    with open(scraper.urls_file, 'r') as f:
        url_data = json.load(f)

    # Extract URLs from different sources and cities for testing
    all_urls = []
    urls_per_source = {}

    for borough_source, data in url_data.items():
        if 'urls' in data and data['urls']:
            urls_per_source[borough_source] = data['urls']
            print(f"📍 Loaded {len(data['urls'])} URLs from {borough_source}")

    # Extract all URLs from all sources
    all_urls = []
    for source in urls_per_source:
        all_urls.extend(urls_per_source[source])

    # Filter out already processed URLs if resuming
    if processed_urls:
        original_count = len(all_urls)
        all_urls = [url for url in all_urls if url not in processed_urls]
        print(f"📊 Filtered out {original_count - len(all_urls)} already processed URLs")
        print(f"📊 Remaining URLs to process: {len(all_urls)}")
    else:
        print(f"📊 Total URLs to process: {len(all_urls)}")

    # Apply max_urls limit if specified
    if args.max_urls:
        all_urls = all_urls[:args.max_urls]
        print(f"🧪 LIMITED to {args.max_urls} URLs for testing")

    # Process URLs
    try:
        start_time = time.time()
        results = scraper.process_urls_parallel(all_urls, args.max_urls)

        # Post-process the results: clean phones and update locations
        print(f"\n🔧 Post-processing {len(results)} results...")
        results = scraper.process_phone_numbers(results)
        results = scraper.update_locations_with_city(results)

        # Save final results
        scraper.save_results(results, "final")

        end_time = time.time()
        processing_time = (end_time - start_time) / 60

        print(f"\n🎉 SCRAPING COMPLETED SUCCESSFULLY!")
        print(f"⏰ Total processing time: {processing_time:.1f} minutes")
        print(f"⚡ Average speed: {len(results) / (processing_time * 60):.2f} URLs/second")

        # Success summary
        successful_results = [r for r in results if r.get('extraction_success', False)]
        success_rate = (len(successful_results) / len(results)) * 100 if results else 0

        print(f"\n📈 FINAL RESULTS:")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Profiles Extracted: {len(successful_results)}")
        print(f"   Total Cost: ~${len(results) * 0.001:.3f}")

        # Generate social media summary
        if scraper.enable_social_media:
            social_summary = scraper.generate_social_media_summary(results)
            print(f"\n📱 SOCIAL MEDIA ANALYSIS:")
            print(f"   Records with social media: {social_summary['with_social_media']}/{social_summary['total_records']} ({social_summary['social_media_percentage']:.1f}%)")
            if social_summary['top_platforms']:
                print(f"   Top platforms:")
                for platform, count in social_summary['top_platforms'][:5]:
                    if platform and platform != 'None':
                        print(f"      {platform}: {count}")

        # Show sample of enhanced data
        print(f"\n📋 SAMPLE ENHANCED DATA:")
        for i, result in enumerate(successful_results[:3], 1):
            name = result.get('name', 'N/A')[:40] + '...' if len(str(result.get('name', ''))) > 40 else result.get('name', 'N/A')
            print(f"   {i}. {name}")
            print(f"      Phone: {result.get('phone', 'N/A')}")
            print(f"      City: {result.get('city', 'N/A')}")
            print(f"      Social: {result.get('social', 'None')}")

        return True

    except KeyboardInterrupt:
        print(f"\n⚠️  SCRAPING INTERRUPTED BY USER")
        print(f"💾 Saving partial results...")
        scraper.save_results(scraper.results, "interrupted")
        return False
    except Exception as e:
        print(f"\n❌ UNEXPECTED ERROR: {e}")
        return False

if __name__ == "__main__":
    result = main()
    exit(0 if result else 1)
