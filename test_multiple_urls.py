#!/usr/bin/env python3
"""
Test multiple URLs to find working profile pages
"""

import json
from nyc_boroughs_scraper import NYCBoroughsScraper

def test_multiple_urls():
    """Test multiple URLs to find working ones"""
    
    # Load URLs
    with open('parallel_nyc_all_urls_deduplicated.json', 'r') as f:
        data = json.load(f)
    
    # Get first combination
    first_combo = list(data.values())[0]
    test_urls = first_combo['urls'][:10]  # Test first 10 URLs
    
    print(f"Testing {len(test_urls)} URLs to find working profile pages...")
    print("=" * 60)
    
    # Create scraper
    scraper = NYCBoroughsScraper("TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G")
    
    working_urls = []
    
    for i, url in enumerate(test_urls):
        print(f"\nTesting URL {i+1}: {url}")
        
        # Try to get HTML
        html = scraper.execute_curl_request(url, scraper.dedicated_curl_template)
        
        if html:
            print(f"  ✓ Downloaded {len(html)} characters")
            
            # Check if it looks like a profile page
            if "profile" in html.lower() or "escort" in html.lower() or "age" in html.lower():
                print(f"  ✓ Looks like a profile page")
                working_urls.append(url)
                
                # Show a snippet
                snippet = html[:500].replace('\n', ' ')
                print(f"  Preview: {snippet}...")
                
                if len(working_urls) >= 3:  # Found enough working URLs
                    break
            else:
                print(f"  ✗ Doesn't look like a profile page")
        else:
            print(f"  ✗ Failed to download")
    
    print(f"\n" + "=" * 60)
    print(f"RESULTS: Found {len(working_urls)} working URLs out of {len(test_urls)} tested")
    
    if working_urls:
        print("Working URLs:")
        for url in working_urls:
            print(f"  - {url}")
        return working_urls[0]  # Return first working URL
    else:
        print("❌ No working URLs found!")
        return None

if __name__ == "__main__":
    working_url = test_multiple_urls()
    
    if working_url:
        print(f"\n🎯 Use this URL for testing: {working_url}")
    else:
        print("\n❌ No working URLs found - this explains why we're getting 0 profiles!")
