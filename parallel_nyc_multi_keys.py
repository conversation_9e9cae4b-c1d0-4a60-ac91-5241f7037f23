#!/usr/bin/env python3
"""
Parallel NYC Boroughs Scraper with Multiple Mistral API Keys
Multi-worker parallel processing system with distributed API key usage
"""

import sys
import os
import json
import time
import threading
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional
from datetime import datetime
import pandas as pd
from pathlib import Path

from nyc_boroughs_scraper import NYCBoroughsScraper

class ParallelNYCScraperMultiKeys:
    def __init__(self, mistral_api_keys: List[str] = None, max_workers: int = 3):
        """Initialize parallel NYC scraper with multiple API keys"""
        # Default to your 5 API keys if none provided
        default_keys = [
            "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G",
            "OHUPD3rpUQBbbd9FHpwnQpdQXIckRXqv", 
            "zeUtrAhXZm7RXe2Knt0xWGb19j3vb3f4",
            "Z9G5EWlDgYq8RtkV8xPfs7hZuAYghzg0",
            "e7QxoqwJNSPjcXFVmnEVgpAInrkWlRLS"
        ]
        
        self.mistral_api_keys = mistral_api_keys or default_keys
        self.max_workers = max_workers
        self.progress_file = "parallel_nyc_multi_keys_progress.json"
        self.results_lock = threading.Lock()
        self.all_scraped_data = []
        self.completed_combinations = set()
        
        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] - %(message)s',
            handlers=[
                logging.FileHandler('parallel_multi_keys_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Log API key distribution
        self.logger.info(f"Initialized with {len(self.mistral_api_keys)} Mistral API keys for {max_workers} workers")
        for i in range(min(max_workers, len(self.mistral_api_keys))):
            key_preview = self.mistral_api_keys[i][:8] + "..." + self.mistral_api_keys[i][-4:]
            self.logger.info(f"Worker {i} will use API key: {key_preview}")
        
    def get_remaining_combinations(self) -> List[Dict[str, str]]:
        """Get remaining borough-source combinations to process"""
        # Create a temporary scraper to get all combinations (use first key)
        temp_scraper = NYCBoroughsScraper(self.mistral_api_keys[0])
        all_combinations = temp_scraper.get_nyc_boroughs()
        
        # Load existing progress
        completed = set()
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r') as f:
                    progress_data = json.load(f)
                    completed = set(progress_data.get('completed_combinations', []))
                    self.logger.info(f"Loaded {len(completed)} completed combinations from progress file")
            except Exception as e:
                self.logger.warning(f"Could not load progress file: {e}")
        
        # Filter out completed combinations
        remaining = []
        for combo in all_combinations:
            combo_key = f"{combo['state']}_{combo['city']}_{combo['source']}"
            if combo_key not in completed:
                remaining.append(combo)
        
        self.logger.info(f"Found {len(remaining)} remaining combinations to process")
        return remaining
    
    def save_progress(self, completed_combination: str = None):
        """Thread-safe progress saving"""
        with self.results_lock:
            if completed_combination:
                self.completed_combinations.add(completed_combination)
            
            progress_data = {
                'timestamp': datetime.now().isoformat(),
                'completed_combinations': list(self.completed_combinations),
                'total_records': len(self.all_scraped_data),
                'max_workers': self.max_workers,
                'api_keys_count': len(self.mistral_api_keys)
            }
            
            try:
                with open(self.progress_file, 'w') as f:
                    json.dump(progress_data, f, indent=2)
                self.logger.info(f"Progress saved: {len(self.completed_combinations)} completed, {len(self.all_scraped_data)} total records")
            except Exception as e:
                self.logger.error(f"Failed to save progress: {e}")
    
    def scrape_single_combination(self, combination: Dict[str, str], worker_id: int) -> Optional[List[Dict]]:
        """Scrape a single borough-source combination with dedicated API key"""
        borough_name = combination['city']
        source = combination['source']
        combo_key = f"{combination['state']}_{borough_name}_{source}"
        
        # Assign API key to worker (round-robin distribution)
        worker_api_key = self.mistral_api_keys[worker_id % len(self.mistral_api_keys)]
        key_preview = worker_api_key[:8] + "..." + worker_api_key[-4:]
        
        self.logger.info(f"Worker {worker_id} starting: {borough_name} from {source} (API key: {key_preview})")
        
        try:
            # Create a dedicated scraper instance for this worker with its own API key
            worker_scraper = NYCBoroughsScraper(worker_api_key)
            worker_scraper.request_delay = 0.5  # Faster since we have multiple API keys
            
            # Scrape this specific combination
            combination_data = worker_scraper.scrape_city_pages(combination)
            
            if combination_data:
                self.logger.info(f"Worker {worker_id} completed {borough_name} ({source}): {len(combination_data)} records")
                
                # Thread-safe data collection
                with self.results_lock:
                    self.all_scraped_data.extend(combination_data)
                
                # Save individual checkpoint
                checkpoint_name = f"parallel_multi_keys_{borough_name}_{source}_checkpoint.xlsx"
                try:
                    df = pd.DataFrame(combination_data)
                    df.to_excel(checkpoint_name, index=False)
                    self.logger.info(f"Worker {worker_id} saved checkpoint: {checkpoint_name}")
                except Exception as e:
                    self.logger.warning(f"Worker {worker_id} failed to save checkpoint: {e}")
                
                # Update progress
                self.save_progress(combo_key)
                
                return combination_data
            else:
                self.logger.warning(f"Worker {worker_id} found no data for {borough_name} ({source})")
                self.save_progress(combo_key)  # Mark as completed even if no data
                return []
                
        except Exception as e:
            self.logger.error(f"Worker {worker_id} failed on {borough_name} ({source}): {e}")
            return None
    
    def run_parallel_scraping(self) -> bool:
        """Run parallel scraping of remaining combinations with multiple API keys"""
        self.logger.info(f"Starting parallel NYC boroughs scraping with {self.max_workers} workers and {len(self.mistral_api_keys)} API keys")
        
        # Get remaining combinations
        remaining_combinations = self.get_remaining_combinations()
        
        if not remaining_combinations:
            self.logger.info("No remaining combinations to process")
            return True
        
        self.logger.info(f"Processing {len(remaining_combinations)} combinations in parallel:")
        for combo in remaining_combinations:
            self.logger.info(f"  - {combo['city']} from {combo['source']}")
        
        # Process combinations in parallel
        successful_workers = 0
        failed_workers = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_combo = {
                executor.submit(self.scrape_single_combination, combo, i): combo 
                for i, combo in enumerate(remaining_combinations)
            }
            
            # Process completed tasks
            for future in as_completed(future_to_combo):
                combo = future_to_combo[future]
                try:
                    result = future.result()
                    if result is not None:
                        successful_workers += 1
                        self.logger.info(f"✓ Completed: {combo['city']} from {combo['source']}")
                    else:
                        failed_workers += 1
                        self.logger.error(f"✗ Failed: {combo['city']} from {combo['source']}")
                except Exception as e:
                    failed_workers += 1
                    self.logger.error(f"✗ Exception in {combo['city']} from {combo['source']}: {e}")
        
        self.logger.info(f"Parallel processing completed: {successful_workers} successful, {failed_workers} failed")
        return failed_workers == 0
    
    def save_final_results(self) -> str:
        """Save final consolidated results"""
        if not self.all_scraped_data:
            self.logger.warning("No data to save")
            return None
        
        try:
            # Create DataFrame
            df = pd.DataFrame(self.all_scraped_data)
            
            # Reorder columns for better readability
            column_order = [
                'state', 'city', 'source', 'title', 'name', 'age', 'phone', 
                'description', 'social_media', 'email', 'website', 'posted_date', 
                'post_id', 'url', 'search_url', 'scraped_at'
            ]
            
            # Reorder columns (only include existing columns)
            existing_columns = [col for col in column_order if col in df.columns]
            remaining_columns = [col for col in df.columns if col not in existing_columns]
            final_columns = existing_columns + remaining_columns
            df = df[final_columns]
            
            # Sort by state, city, source, then scraped_at
            df = df.sort_values(['state', 'city', 'source', 'scraped_at'])
            
            # Save to Excel
            output_file = "parallel_nyc_multi_keys_final.xlsx"
            df.to_excel(output_file, index=False)
            
            self.logger.info(f"Final results saved to {output_file}: {len(df)} records")
            
            # Clean up progress file on successful completion
            try:
                if os.path.exists(self.progress_file):
                    os.remove(self.progress_file)
                    self.logger.info("Progress file cleaned up after successful completion")
            except Exception as e:
                self.logger.warning(f"Could not clean up progress file: {e}")
            
            return output_file
            
        except Exception as e:
            self.logger.error(f"Failed to save final results: {e}")
            return None

def main():
    """Main entry point for parallel NYC boroughs scraper with multiple API keys"""
    parser = argparse.ArgumentParser(description='Parallel NYC Boroughs Web Scraper with Multiple Mistral API Keys')
    parser.add_argument('--workers', type=int, default=5, help='Number of parallel workers (default: 5)')
    parser.add_argument('--mistral-keys', help='Comma-separated Mistral AI API keys (optional - defaults to built-in keys)')
    parser.add_argument('--clean-progress', action='store_true', help='Clean up progress files and start fresh')
    
    args = parser.parse_args()
    
    # Validate worker count
    if args.workers < 1 or args.workers > 10:
        print("Error: Number of workers must be between 1 and 10")
        return 1
    
    # Parse Mistral API keys
    mistral_keys = None
    if args.mistral_keys:
        mistral_keys = [key.strip() for key in args.mistral_keys.split(',')]
        print(f"Using {len(mistral_keys)} provided API keys")
    else:
        print("Using built-in API keys")
    
    # Clean progress files if requested
    if args.clean_progress:
        progress_file = "parallel_nyc_multi_keys_progress.json"
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print(f"Cleaned up progress file: {progress_file}")
        
        # Also clean checkpoint files
        import glob
        checkpoint_pattern = "parallel_multi_keys_*_checkpoint.xlsx"
        checkpoint_files = glob.glob(checkpoint_pattern)
        for cf in checkpoint_files:
            os.remove(cf)
            print(f"Cleaned up checkpoint file: {cf}")
        
        print("Progress files cleaned. Starting fresh.")
    
    # Create and run parallel scraper
    scraper = ParallelNYCScraperMultiKeys(mistral_api_keys=mistral_keys, max_workers=args.workers)
    
    print(f"Starting parallel NYC boroughs scraping with {args.workers} workers and {len(scraper.mistral_api_keys)} API keys...")
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Features: Age ≤30, Women only, Multiple Mistral AI keys, Min 25 pages per combination")
    print("Expected improvement: No rate limiting, 3-5x faster processing")
    print()
    
    start_time = time.time()
    
    success = scraper.run_parallel_scraping()
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    if success:
        output_file = scraper.save_final_results()
        
        print(f"\n✓ Parallel scraping with multiple API keys completed successfully!")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Total records collected: {len(scraper.all_scraped_data)}")
        
        if output_file:
            print(f"Final results saved to: {output_file}")
        
        # Show summary by source
        aaok_count = len([r for r in scraper.all_scraped_data if r.get('source') == 'aaok'])
        aypapi_count = len([r for r in scraper.all_scraped_data if r.get('source') == 'aypapi'])
        print(f"  - aaok.com: {aaok_count} records")
        print(f"  - aypapi.com: {aypapi_count} records")
        
        # Show summary by borough
        boroughs = list(set(r.get('city') for r in scraper.all_scraped_data))
        print(f"NYC Boroughs processed: {len(boroughs)}")
        for borough in sorted(boroughs):
            borough_count = len([r for r in scraper.all_scraped_data if r.get('city') == borough])
            print(f"  - {borough}: {borough_count} records")
        
        return 0
    else:
        print(f"\n✗ Parallel scraping completed with some failures")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Partial results collected: {len(scraper.all_scraped_data)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
