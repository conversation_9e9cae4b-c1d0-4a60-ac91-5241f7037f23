#!/usr/bin/env python3
"""
Test script for the web scraper
Tests individual components and runs a small sample scrape
"""

import sys
from pathlib import Path
from web_scraper import WebScraper

def test_url_parsing():
    """Test URL list parsing"""
    print("Testing URL list parsing...")
    scraper = WebScraper()
    cities = scraper.parse_url_list()
    
    if cities:
        print(f"✓ Successfully parsed {len(cities)} cities")
        print("Sample cities:")
        for i, city in enumerate(cities[:5]):
            print(f"  {i+1}. {city['city']}, {city['state']} - {city['url']}")
        return True
    else:
        print("✗ Failed to parse cities")
        return False

def test_curl_loading():
    """Test cURL command loading"""
    print("\nTesting cURL command loading...")
    scraper = WebScraper()
    
    search_curl = scraper.search_curl_template
    dedicated_curl = scraper.dedicated_curl_template
    
    if search_curl and dedicated_curl:
        print("✓ Successfully loaded cURL templates")
        print(f"Search cURL length: {len(search_curl)} characters")
        print(f"Dedicated cURL length: {len(dedicated_curl)} characters")
        return True
    else:
        print("✗ Failed to load cURL templates")
        return False

def test_html_parsing():
    """Test HTML parsing with sample files"""
    print("\nTesting HTML parsing...")
    scraper = WebScraper()
    
    # Test search page parsing
    try:
        with open("search page_1.html", 'r', encoding='utf-8') as f:
            search_html = f.read()
        
        urls = scraper.extract_dedicated_urls(search_html)
        print(f"✓ Extracted {len(urls)} URLs from search page")
        
        if urls:
            print("Sample URLs:")
            for i, url in enumerate(urls[:3]):
                print(f"  {i+1}. {url}")
    except Exception as e:
        print(f"✗ Error testing search page parsing: {e}")
        return False
    
    # Test dedicated page parsing
    try:
        with open("dedicate page sample 2.html", 'r', encoding='utf-8') as f:
            dedicated_html = f.read()
        
        data = scraper.extract_dedicated_page_data(dedicated_html, "test_url")
        if data:
            print("✓ Successfully extracted data from dedicated page")
            print("Extracted fields:")
            for key, value in data.items():
                if isinstance(value, str) and len(value) > 50:
                    value = value[:50] + "..."
                print(f"  {key}: {value}")
        else:
            print("✗ Failed to extract data from dedicated page")
            return False
            
    except Exception as e:
        print(f"✗ Error testing dedicated page parsing: {e}")
        return False
    
    return True

def test_small_scrape():
    """Test scraping with just the first city"""
    print("\nTesting small scrape (first city only)...")
    scraper = WebScraper()
    scraper.request_delay = 1  # Faster for testing
    
    try:
        # Get first city
        cities = scraper.parse_url_list()
        if not cities:
            print("✗ No cities available for testing")
            return False
        
        first_city = cities[0]
        print(f"Testing with: {first_city['city']}, {first_city['state']}")
        
        # Scrape first city
        city_data = scraper.scrape_city(first_city)
        
        if city_data:
            print(f"✓ Successfully scraped {len(city_data)} records")
            
            # Save test results
            scraper.scraped_data = city_data
            scraper.save_to_excel("test_output.xlsx")
            print("✓ Test data saved to test_output.xlsx")
            
            # Show sample data
            if city_data:
                print("Sample record:")
                sample = city_data[0]
                for key, value in sample.items():
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    print(f"  {key}: {value}")
            
            return True
        else:
            print("✗ No data scraped from test city")
            return False
            
    except Exception as e:
        print(f"✗ Error during small scrape test: {e}")
        return False

def main():
    """Run all tests"""
    print("Web Scraper Test Suite")
    print("=" * 50)
    
    tests = [
        test_url_parsing,
        test_curl_loading,
        test_html_parsing,
        test_small_scrape
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The scraper is ready to use.")
        print("\nTo run the full scraper:")
        print("  python web_scraper.py --max-cities 5  # Test with 5 cities")
        print("  python web_scraper.py                 # Full scrape")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
