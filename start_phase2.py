#!/usr/bin/env python3
"""
Start Phase 2 Scraping - Simple starter script
"""

import subprocess
import sys
import os

def main():
    """Start phase 2 scraping"""

    print("🚀 Starting Phase 2 Targeted City Scraping")
    print("=" * 50)

    # Check if the URLs file exists
    urls_file = "targeted_all_urls_20250819_001221.json"
    if not os.path.exists(urls_file):
        print(f"❌ URLs file {urls_file} not found!")
        print("Please ensure the file is in the current directory.")
        return

    print(f"✅ Found URLs file: {urls_file}")

    # Run the phase 2 scraper
    try:
        print("\n🎯 Launching scraper...")
        subprocess.run([sys.executable, "run_phase2_scraping.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Scraper failed with error code: {e.returncode}")
    except KeyboardInterrupt:
        print("\n⚠️ Scraping interrupted by user")
    except FileNotFoundError:
        print("❌ run_phase2_scraping.py not found!")

if __name__ == "__main__":
    main()
