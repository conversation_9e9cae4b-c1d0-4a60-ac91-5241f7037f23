#!/usr/bin/env python3
"""
Fresh City Scraper
A stealth web scraper for new cities/states with phone number prefix and no old data checking
"""

import requests
import time
import random
import threading
import json
import os
import pandas as pd
import re
from datetime import datetime
from typing import List, Dict, Optional, Set
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
import urllib.parse
from fake_useragent import UserAgent
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class FreshCityScraper:
    def __init__(self, max_workers: int = 2, enable_social_media: bool = False, add_phone_prefix: bool = True):
        self.max_workers = max_workers
        self.results = []
        self.results_lock = threading.Lock()
        self.processed_count = 0
        self.skipped_count = 0

        # Phone number tracking for current session only (no old data loading)
        self.scraped_phone_numbers: Set[str] = set()
        self.phone_lock = threading.Lock()
        self.add_phone_prefix = add_phone_prefix

        # Stealth settings
        self.ua = UserAgent()
        self.session_pool = {}

        # Rate limiting for stealth
        self.min_delay = 2.0  # Minimum delay between requests
        self.max_delay = 5.0  # Maximum delay between requests
        self.last_request_time = {}  # Track last request time per worker

        # Social media extraction (disabled by default for stealth)
        self.enable_social_media = enable_social_media

        print("🆕 Fresh City Scraper initialized - NO old data loading!")
        print(f"📱 Phone prefix enabled: {self.add_phone_prefix}")

    def clean_phone_number(self, phone_str: str) -> Optional[str]:
        """Clean phone number for consistent comparison"""
        if not phone_str or pd.isna(phone_str):
            return None

        # Convert to string and remove all non-digits
        phone_clean = re.sub(r'[^\d]', '', str(phone_str))

        # Must be at least 10 digits for US phone numbers
        if len(phone_clean) < 10:
            return None

        # Normalize to 10 digits (remove country code if present)
        if len(phone_clean) == 11 and phone_clean.startswith('1'):
            phone_clean = phone_clean[1:]
        elif len(phone_clean) > 11:
            return None  # Invalid length

        return phone_clean

    def add_phone_prefix(self, phone: str) -> str:
        """Add '1' prefix to phone number if enabled"""
        if not self.add_phone_prefix or not phone:
            return phone

        cleaned = self.clean_phone_number(phone)
        if cleaned and len(cleaned) == 10:
            return f"1{cleaned}"
        return phone

    def is_phone_duplicate_in_session(self, phone: str) -> bool:
        """Check if phone number has already been scraped in current session"""
        cleaned_phone = self.clean_phone_number(phone)
        if not cleaned_phone:
            return False

        with self.phone_lock:
            return cleaned_phone in self.scraped_phone_numbers

    def add_phone_to_session(self, phone: str):
        """Add phone number to current session tracking"""
        cleaned_phone = self.clean_phone_number(phone)
        if cleaned_phone:
            with self.phone_lock:
                self.scraped_phone_numbers.add(cleaned_phone)

    def get_stealth_session(self, worker_id: str) -> requests.Session:
        """Get a stealth session for the worker"""
        if worker_id not in self.session_pool:
            session = requests.Session()

            # Set random user agent
            session.headers.update({
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
            })

            # Disable SSL verification for problematic sites
            session.verify = False
            session.timeout = 30

            self.session_pool[worker_id] = session

        return self.session_pool[worker_id]

    def fetch_with_stealth(self, url: str, worker_id: str, quick_check: bool = False) -> Optional[str]:
        """Fetch URL using stealth techniques"""

        # Rate limiting per worker
        current_time = time.time()
        if worker_id in self.last_request_time:
            time_since_last = current_time - self.last_request_time[worker_id]
            min_interval = random.uniform(self.min_delay, self.max_delay)
            if quick_check:
                min_interval = random.uniform(1.0, 2.0)  # Faster for quick checks

            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                time.sleep(sleep_time)

        self.last_request_time[worker_id] = time.time()

        max_retries = 3
        for attempt in range(max_retries):
            try:
                session = self.get_stealth_session(worker_id)

                # Rotate user agent occasionally
                if random.random() < 0.3:
                    session.headers['User-Agent'] = self.ua.random

                print(f"Worker {worker_id}: {'Quick checking' if quick_check else 'Fetching'} {url} (attempt {attempt + 1}/{max_retries})")

                # Add referrer for some sites
                headers = {}
                if 'listcrawler' in url.lower() or 'aaok' in url.lower():
                    headers['Referer'] = 'https://www.google.com/'

                # Make request
                response = session.get(
                    url,
                    headers=headers,
                    timeout=30,
                    allow_redirects=True
                )

                if response.status_code == 200:
                    html = response.text

                    if len(html) < 500:
                        print(f"Worker {worker_id}: Content too short ({len(html)} chars), retrying...")
                        if attempt < max_retries - 1:
                            time.sleep(random.uniform(3, 8))
                            continue

                    # Check for common blocking indicators
                    blocking_indicators = [
                        "Access denied", "Blocked", "403 Forbidden", "Cloudflare",
                        "Just a moment", "Enable JavaScript and cookies",
                        "Checking your browser", "Please wait while we verify",
                        "Ray ID:", "DDoS protection"
                    ]

                    html_lower = html.lower()
                    if any(indicator.lower() in html_lower for indicator in blocking_indicators):
                        print(f"Worker {worker_id}: Blocking detected, retrying with longer delay...")
                        if attempt < max_retries - 1:
                            time.sleep(random.uniform(10, 20))
                            continue

                    print(f"Worker {worker_id}: ✅ Successfully fetched {len(html)} characters")
                    return html

                elif response.status_code == 429:
                    print(f"Worker {worker_id}: Rate limited (429), waiting...")
                    time.sleep(random.uniform(30, 60))
                    continue

                elif response.status_code in [403, 406]:
                    print(f"Worker {worker_id}: Blocked ({response.status_code}), trying with different headers...")
                    if attempt < max_retries - 1:
                        # Try with different user agent
                        session.headers['User-Agent'] = self.ua.random
                        time.sleep(random.uniform(5, 15))
                        continue

                else:
                    print(f"Worker {worker_id}: HTTP {response.status_code} - {response.reason}")
                    if attempt < max_retries - 1:
                        sleep_time = random.uniform(5, 15) * (attempt + 1)
                        print(f"Worker {worker_id}: Waiting {sleep_time:.1f}s before retry...")
                        time.sleep(sleep_time)

            except requests.exceptions.Timeout:
                print(f"Worker {worker_id}: Request timeout on attempt {attempt + 1}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(10, 20))

            except Exception as e:
                print(f"Worker {worker_id}: Request failed on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(5, 15))

        print(f"Worker {worker_id}: ❌ All attempts failed for {url}")
        return None

    def quick_extract_phone_from_html(self, html: str) -> Optional[str]:
        """Quickly extract phone number from HTML for deduplication check"""
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Extract phone from viewposttelephone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                return phone_clean.strip() if phone_clean.strip() else None

            # Fallback: look for phone patterns in text
            phone_patterns = [
                r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
                r'\(\d{3}\)\s*\d{3}[-.]?\d{4}',
                r'\b\d{10}\b'
            ]

            page_text = soup.get_text()
            for pattern in phone_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    return matches[0]

            return None
        except:
            return None

    def extract_data_from_html(self, html: str, url: str, city: str = "", state: str = "") -> Dict:
        """Extract profile data from HTML using proper selectors"""
        data = {
            'url': url,
            'name': '',
            'age': '',
            'phone': '',
            'location': '',
            'description': '',
            'website_type': '',
            'city': city,
            'state': state,
            'social': '',
            'extraction_success': False,
            'scraped_at': datetime.now().isoformat(),
            'content_length': len(html),
            'scraper_type': 'fresh_city'
        }

        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Detect website type
            domain = url.lower()
            if 'aypapi' in domain:
                data['website_type'] = 'aypapi'
            elif 'aaok' in domain or 'listcrawler' in domain:
                data['website_type'] = 'aaok'
            else:
                data['website_type'] = 'unknown'

            # Extract name - first try viewpostname, then fallback to viewposttitle
            name_elem = soup.select_one('.viewpostname')
            if name_elem:
                # First try to get text after the Nym: span
                nym_span = name_elem.select_one('span')
                if nym_span:
                    # Get all text after the span
                    name_text = name_elem.get_text(strip=True)
                    span_text = nym_span.get_text(strip=True)
                    if span_text in name_text:
                        extracted_name = name_text.replace(span_text, '').strip()
                        if extracted_name:  # Only use if not empty
                            data['name'] = extracted_name
                else:
                    # Fallback to full text content
                    name_text = name_elem.get_text(strip=True)
                    if 'Nym:' in name_text:
                        extracted_name = name_text.replace('Nym:', '').strip()
                        if extracted_name:  # Only use if not empty
                            data['name'] = extracted_name
                    elif name_text:
                        data['name'] = name_text

            # If name is still empty, try to extract from title
            if not data['name']:
                title_elem = soup.select_one('.viewposttitle')
                if title_elem:
                    title_text = title_elem.get_text(strip=True)
                    # Remove age from title to get name
                    age_elem = soup.select_one('.postTitleAge')
                    if age_elem:
                        age = age_elem.get_text(strip=True)
                        # Remove age and dash pattern from title
                        title_without_age = title_text.replace(f' - {age}', '').replace(f'- {age}', '').replace(f' -{age}', '').replace(f'-{age}', '').strip()
                        if title_without_age:
                            data['name'] = title_without_age
                    else:
                        # No age found, use title as is but limit length
                        if title_text:
                            data['name'] = title_text[:100].strip()

            # Extract age from postTitleAge span
            age_elem = soup.select_one('.postTitleAge')
            if age_elem:
                age_text = age_elem.get_text(strip=True)
                if age_text.isdigit() and 18 <= int(age_text) <= 65:
                    data['age'] = age_text

            # Extract phone from viewposttelephone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                if phone_clean.strip():
                    # Add prefix if enabled
                    original_phone = phone_clean.strip()
                    data['phone'] = self.add_phone_prefix(original_phone)

            # Extract location from the second li in viewpostlocationIconBabylon
            location_container = soup.select_one('.viewpostlocationIconBabylon ul')
            if location_container:
                li_elements = location_container.select('li')
                if len(li_elements) >= 2:
                    location_text = li_elements[1].get_text(strip=True)
                    # Remove "Location:" prefix if present
                    if 'Location:' in location_text:
                        data['location'] = location_text.replace('Location:', '').strip()
                    else:
                        data['location'] = location_text

            # Extract description from viewpostbody
            body_elem = soup.select_one('.viewpostbody')
            if body_elem:
                data['description'] = body_elem.get_text(strip=True)[:500]

            # Get page title
            title = soup.title
            if title:
                page_title = title.get_text(strip=True)
                data['page_title'] = page_title

            # Simple social media extraction (no API calls for stealth)
            if data['description']:
                social_patterns = [
                    r'instagram[:\s]*@?\w+',
                    r'telegram[:\s]*@?\w+',
                    r'twitter[:\s]*@?\w+',
                    r'snapchat[:\s]*@?\w+',
                    r'@\w+',
                ]

                social_matches = []
                desc_lower = data['description'].lower()
                for pattern in social_patterns:
                    matches = re.findall(pattern, desc_lower)
                    social_matches.extend(matches)

                data['social'] = ', '.join(social_matches[:3]) if social_matches else "None"
            else:
                data['social'] = "None"

            # Check if we got meaningful data
            if data['name'] or data['age'] or data['phone']:
                data['extraction_success'] = True

            # Additional validation - check for actual profile content
            if data['extraction_success']:
                # Look for profile indicators to confirm this is a real profile page
                profile_indicators = ['escort', 'profile', 'contact', 'call', 'text']
                page_text = soup.get_text().lower()
                if not any(indicator in page_text for indicator in profile_indicators):
                    data['extraction_success'] = False
                    data['error'] = 'No profile content detected'

        except Exception as e:
            print(f"❌ Error extracting data from {url}: {e}")
            data['error'] = str(e)

        return data

    def process_url_batch(self, urls: List[str], worker_id: str, city: str = "", state: str = "") -> List[Dict]:
        """Process a batch of URLs with session-only deduplication"""
        results = []

        print(f"Worker {worker_id}: Starting batch of {len(urls)} URLs for {city}, {state}")

        for i, url in enumerate(urls):
            print(f"\nWorker {worker_id}: Processing {i+1}/{len(urls)}: {url}")

            # Step 1: Quick phone check to avoid duplicate processing in current session
            html_preview = self.fetch_with_stealth(url, worker_id, quick_check=True)

            if html_preview:
                quick_phone = self.quick_extract_phone_from_html(html_preview)

                if quick_phone and self.is_phone_duplicate_in_session(quick_phone):
                    print(f"Worker {worker_id}: 🔄 DUPLICATE IN SESSION - Phone {self.clean_phone_number(quick_phone)} already processed")

                    # Add session duplicate entry
                    session_duplicate_data = {
                        'url': url,
                        'name': '',
                        'age': '',
                        'phone': self.add_phone_prefix(quick_phone),
                        'location': '',
                        'description': '',
                        'website_type': 'aaok' if 'aaok' in url.lower() else 'aypapi',
                        'city': city,
                        'state': state,
                        'social': '',
                        'extraction_success': False,
                        'skip_reason': 'session_duplicate',
                        'duplicate_phone': self.clean_phone_number(quick_phone),
                        'scraped_at': datetime.now().isoformat(),
                        'content_length': len(html_preview),
                        'scraper_type': 'fresh_city'
                    }
                    results.append(session_duplicate_data)

                    with self.results_lock:
                        self.skipped_count += 1

                    continue

                # Step 2: Process normally since phone is new in current session
                data = self.extract_data_from_html(html_preview, url, city, state)

                # Add phone to session tracking if extraction was successful
                if data['phone'] and data['extraction_success']:
                    # Use original phone for tracking (without prefix)
                    original_phone = data['phone'].replace('1', '', 1) if data['phone'].startswith('1') else data['phone']
                    self.add_phone_to_session(original_phone)

                results.append(data)

                if data['extraction_success'] and not data.get('skip_reason'):
                    print(f"Worker {worker_id}: ✅ SUCCESS - NEW DATA")
                    print(f"    Name: '{data['name']}'")
                    print(f"    Age: {data['age']}")
                    print(f"    Phone: '{data['phone']}'")
                    print(f"    City: {data['city']}")
                    print(f"    State: {data['state']}")
                    print(f"    Type: {data['website_type']}")
                else:
                    print(f"Worker {worker_id}: ❌ No meaningful data extracted or session duplicate")
                    if 'error' in data:
                        print(f"    Error: {data['error']}")
                    if 'skip_reason' in data:
                        print(f"    Skip Reason: {data['skip_reason']}")
            else:
                # Add failed entry
                failed_data = {
                    'url': url,
                    'name': '',
                    'age': '',
                    'phone': '',
                    'location': '',
                    'description': '',
                    'website_type': 'aaok' if 'aaok' in url.lower() else 'aypapi',
                    'city': city,
                    'state': state,
                    'social': '',
                    'extraction_success': False,
                    'error': 'Failed to fetch page content',
                    'scraped_at': datetime.now().isoformat(),
                    'content_length': 0,
                    'scraper_type': 'fresh_city'
                }
                results.append(failed_data)
                print(f"Worker {worker_id}: ❌ Failed to fetch content")

            # Add results to main collection and save periodically
            with self.results_lock:
                self.results.append(data if html_preview else failed_data)
                self.processed_count += 1

                # Save intermediate results every 50 URLs
                if self.processed_count % 50 == 0:
                    self.save_results(self.results, f"checkpoint_{self.processed_count}")
                    print(f"\n💾 CHECKPOINT: Saved {self.processed_count} URLs, Session duplicates: {self.skipped_count}")

        successful = len([r for r in results if r.get('extraction_success', False) and not r.get('skip_reason')])
        skipped = len([r for r in results if r.get('skip_reason')])
        print(f"Worker {worker_id}: ✅ Batch completed: {successful} successful, {skipped} session duplicates, {len(results)-successful-skipped} failed")
        return results

    def scrape_city_state(self, urls: List[str], city: str, state: str, max_urls: Optional[int] = None) -> List[Dict]:
        """Scrape URLs for a specific city/state combination"""

        if max_urls:
            urls = urls[:max_urls]

        print(f"\n🆕 Starting FRESH scraping for {city}, {state}")
        print(f"📋 URLs to process: {len(urls)}")
        print(f"👥 Workers: {self.max_workers}")
        print(f"📱 Phone prefix enabled: {self.add_phone_prefix}")
        print(f"🕒 Delay between requests: {self.min_delay}-{self.max_delay} seconds")
        print("=" * 60)

        # Split URLs into batches for workers (smaller batches for stealth)
        batch_size = max(1, len(urls) // self.max_workers)
        if batch_size > 25:  # Limit batch size for stealth
            batch_size = 25

        batches = [urls[i:i + batch_size] for i in range(0, len(urls), batch_size)]

        all_results = []
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all batches
            future_to_worker = {}
            for i, batch in enumerate(batches):
                worker_id = f"W{i+1}"
                future = executor.submit(self.process_url_batch, batch, worker_id, city, state)
                future_to_worker[future] = worker_id

            # Collect results
            for future in as_completed(future_to_worker):
                worker_id = future_to_worker[future]
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    print(f"✅ {worker_id} completed")
                except Exception as e:
                    print(f"❌ {worker_id} failed: {e}")

        elapsed_time = time.time() - start_time

        print(f"\n🏁 FRESH SCRAPING COMPLETE for {city}, {state}")
        print(f"⏱️  Total time: {elapsed_time:.1f} seconds")
        print(f"📊 Processed: {len(all_results)} URLs")
        print(f"🔄 Session duplicates: {self.skipped_count}")
        print(f"📱 Unique phone numbers found: {len(self.scraped_phone_numbers)}")

        return all_results

    def save_results(self, results: List[Dict], suffix: str = "final"):
        """Save results to Excel file with detailed analysis"""
        if not results:
            print("⚠️  No results to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Get city and state from first result for filename
        city = results[0].get('city', 'unknown') if results else 'unknown'
        state = results[0].get('state', 'unknown') if results else 'unknown'

        filename = f"fresh_{city}_{state}_{len(results)}_urls_{timestamp}_{suffix}.xlsx"

        try:
            df = pd.DataFrame(results)

            # Reorder columns
            column_order = [
                'url', 'name', 'age', 'phone', 'location', 'description',
                'website_type', 'city', 'state', 'social', 'extraction_success',
                'skip_reason', 'duplicate_phone', 'page_title', 'content_length',
                'scraper_type', 'scraped_at', 'error'
            ]

            # Only include columns that exist
            existing_columns = [col for col in column_order if col in df.columns]
            df = df[existing_columns]

            # Create multiple sheets
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # All data
                df.to_excel(writer, sheet_name='All_Data', index=False)

                # Successful extractions only (new unique data)
                successful_df = df[(df['extraction_success'] == True) & (df['skip_reason'].isna())]
                if not successful_df.empty:
                    successful_df.to_excel(writer, sheet_name='New_Fresh_Data', index=False)

                # Session duplicates
                session_dups_df = df[df['skip_reason'] == 'session_duplicate']
                if not session_dups_df.empty:
                    session_dups_df.to_excel(writer, sheet_name='Session_Duplicates', index=False)

                # Failed extractions
                failed_df = df[(df['extraction_success'] == False) & (df['skip_reason'].isna())]
                if not failed_df.empty:
                    failed_df.to_excel(writer, sheet_name='Failed', index=False)

                # Website type breakdown
                aaok_df = df[df['website_type'] == 'aaok']
                aypapi_df = df[df['website_type'] == 'aypapi']

                if not aaok_df.empty:
                    aaok_df.to_excel(writer, sheet_name='AAOK_Sites', index=False)
                if not aypapi_df.empty:
                    aypapi_df.to_excel(writer, sheet_name='AYPAPI_Sites', index=False)

                # Statistics sheet
                stats_data = {
                    'Metric': [
                        'Target City',
                        'Target State',
                        'Total URLs Processed',
                        'Fresh New Extractions',
                        'Session Duplicates',
                        'Failed Extractions',
                        'Success Rate (%)',
                        'Session Duplicate Rate (%)',
                        'Unique Phone Numbers Found',
                        'Phone Prefix Enabled',
                        'AAOK URLs',
                        'AYPAPI URLs',
                        'Average Content Length',
                        'Scraper Type',
                        'Processing Time (estimated)',
                        'No API Costs!'
                    ],
                    'Value': [
                        city,
                        state,
                        len(df),
                        len(successful_df),
                        len(session_dups_df),
                        len(failed_df),
                        f"{(len(successful_df) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                        f"{(len(session_dups_df) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                        len(self.scraped_phone_numbers),
                        self.add_phone_prefix,
                        len(df[df['website_type'] == 'aaok']),
                        len(df[df['website_type'] == 'aypapi']),
                        f"{df['content_length'].mean():.0f}" if 'content_length' in df.columns else 'N/A',
                        'Fresh City (Stealth)',
                        f"{len(df) * 3:.0f} seconds",
                        'FREE!'
                    ]
                }
                pd.DataFrame(stats_data).to_excel(writer, sheet_name='Statistics', index=False)

            # Calculate and display statistics
            successful = len(successful_df)
            failed = len(failed_df)
            session_dups = len(session_dups_df)
            success_rate = (successful / len(df)) * 100 if len(df) > 0 else 0

            print(f"\n💾 SAVED: {filename}")
            print(f"📊 FRESH SCRAPING RESULTS for {city}, {state}:")
            print(f"   Total URLs: {len(df)}")
            print(f"   Fresh New Data: {successful} ({success_rate:.1f}%)")
            print(f"   Session Duplicates: {session_dups} ({(session_dups/len(df)*100):.1f}%)")
            print(f"   Failed: {failed}")
            print(f"   Unique Phone Numbers: {len(self.scraped_phone_numbers)}")
            print(f"   Phone Prefix: {'1' if self.add_phone_prefix else 'None'}")

            # Show sample fresh data
            if not successful_df.empty:
                print(f"\n✅ SAMPLE FRESH DATA:")
                for i, row in successful_df.head(5).iterrows():
                    name = row.get('name', 'N/A')
                    age = row.get('age', 'N/A')
                    phone = row.get('phone', 'N/A')
                    website = row.get('website_type', 'N/A')
                    print(f"   • {name} | Age: {age} | Phone: {phone} | Type: {website}")

        except Exception as e:
            print(f"❌ Error saving results: {e}")

# Define target cities and states
TARGET_LOCATIONS = [
    {"city": "South New Jersey", "state": "New Jersey", "search_terms": ["south jersey", "cherry hill", "camden"]},
    {"city": "Philadelphia", "state": "Pennsylvania", "search_terms": ["philadelphia", "philly"]},
    {"city": "Pittsburgh", "state": "Pennsylvania", "search_terms": ["pittsburgh", "pitt"]},
    {"city": "Wilmington", "state": "Delaware", "search_terms": ["wilmington"]},
    {"city": "Dover", "state": "Delaware", "search_terms": ["dover"]},
    {"city": "Baltimore", "state": "Maryland", "search_terms": ["baltimore"]},
    {"city": "Annapolis", "state": "Maryland", "search_terms": ["annapolis"]}
]

def generate_sample_urls(city: str, state: str, count: int = 50) -> List[str]:
    """Generate sample URLs for testing - replace with actual URL generation logic"""
    base_urls = [
        "https://aaok.com.listcrawler.eu/post/escorts/usa/{}/{}",
        "https://aypapi.com/post/escorts/usa/{}/{}",
    ]

    # Convert city/state to URL-friendly format
    state_code = {
        "New Jersey": "newjersey",
        "Pennsylvania": "pennsylvania",
        "Delaware": "delaware",
        "Maryland": "maryland"
    }.get(state, state.lower().replace(" ", ""))

    city_code = city.lower().replace(" ", "").replace(",", "")

    urls = []
    for i in range(count):
        base_url = random.choice(base_urls)
        # Generate sample post IDs
        post_id = random.randint(190000000, 200000000)
        url = f"{base_url.format(state_code, city_code)}/{post_id}"
        urls.append(url)

    return urls

def scrape_all_target_cities(max_urls_per_city: int = 100):
    """Scrape all target cities and states"""

    print("🗺️  FRESH CITY SCRAPER - Multi-City Mode")
    print("=" * 60)
    print("🎯 Target Locations:")
    for i, location in enumerate(TARGET_LOCATIONS, 1):
        print(f"   {i}. {location['city']}, {location['state']}")
    print("=" * 60)

    # Initialize scraper
    scraper = FreshCityScraper(
        max_workers=2,
        enable_social_media=False,
        add_phone_prefix=True
    )

    all_results = []

    for location in TARGET_LOCATIONS:
        city = location['city']
        state = location['state']

        print(f"\n🏙️  Starting scrape for {city}, {state}")
        print("-" * 40)

        # Generate or load URLs for this city/state
        # TODO: Replace with actual URL loading logic
        urls = generate_sample_urls(city, state, max_urls_per_city)
        print(f"📋 Generated {len(urls)} sample URLs for {city}")

        # Scrape this city/state
        city_results = scraper.scrape_city_state(urls, city, state, max_urls_per_city)

        # Save individual city results
        scraper.save_results(city_results, f"final_{city.replace(' ', '_')}")

        # Add to overall results
        all_results.extend(city_results)

        # Small delay between cities
        time.sleep(random.uniform(30, 60))

        print(f"✅ Completed {city}, {state}")
        print(f"   New data: {len([r for r in city_results if r.get('extraction_success')])}")
        print(f"   Session duplicates: {len([r for r in city_results if r.get('skip_reason')])}")

    # Save combined results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    combined_filename = f"fresh_all_cities_combined_{len(all_results)}_urls_{timestamp}_final.xlsx"

    if all_results:
        scraper.results = all_results
        scraper.save_results(all_results, "all_cities_combined")

    # Final summary
    total_successful = len([r for r in all_results if r.get('extraction_success')])
    total_session_dups = len([r for r in all_results if r.get('skip_reason')])
    total_failed = len(all_results) - total_successful - total_session_dups

    print(f"\n🎉 ALL CITIES SCRAPING COMPLETED!")
    print(f"📊 FINAL SUMMARY:")
    print(f"   • Cities scraped: {len(TARGET_LOCATIONS)}")
    print(f"   • Total URLs processed: {len(all_results)}")
    print(f"   • Fresh new data: {total_successful}")
    print(f"   • Session duplicates: {total_session_dups}")
    print(f"   • Failed: {total_failed}")
    print(f"   • Success rate: {(total_successful/len(all_results)*100):.1f}%")
    print(f"   • Total unique phone numbers: {len(scraper.scraped_phone_numbers)}")
    print(f"   • Phone prefix: {'1' if scraper.add_phone_prefix else 'None'}")
    print(f"   • No API costs! 💰")

def main():
    """Main function to run the fresh city scraper"""

    print("🆕 FRESH CITY SCRAPER")
    print("=" * 50)
    print("📋 Features:")
    print("   • No old data checking (completely fresh)")
    print("   • Phone number prefix (1xxxxxxxxxx)")
    print("   • Session-only deduplication")
    print("   • Stealth techniques (user agent rotation, delays)")
    print("   • Multi-city support")
    print("   • No API costs")
    print("=" * 50)

    # Check if fake_useragent is installed
    try:
        from fake_useragent import UserAgent
    except ImportError:
        print("⚠️  fake_useragent not found. Please install:")
        print("   pip install fake-useragent")
        return

    # Choose mode
    print("\n🔧 Choose mode:")
    print("1. Scrape all target cities (recommended)")
    print("2. Scrape single city (testing)")
    print("3. Generate sample URLs only")

    # Auto-select mode 1 for production
    mode = 1
    print(f"✅ Auto-selected mode {mode}: Scrape all target cities")

    if mode == 1:
        # Scrape all cities
        max_urls_per_city = 50  # Adjust as needed
        print(f"🎯 Processing up to {max_urls_per_city} URLs per city")
        print(f"📍 Total cities: {len(TARGET_LOCATIONS)}")
        print(f"⏱️  Estimated total time: {len(TARGET_LOCATIONS) * max_urls_per_city * 3 / 60:.1f} minutes")

        scrape_all_target_cities(max_urls_per_city)

    elif mode == 2:
        # Single city testing
        print("🧪 Single city testing mode")
        location = TARGET_LOCATIONS[0]  # Test with first city

        scraper = FreshCityScraper(
            max_workers=2,
            enable_social_media=False,
            add_phone_prefix=True
        )

        urls = generate_sample_urls(location['city'], location['state'], 10)
        results = scraper.scrape_city_state(urls, location['city'], location['state'])
        scraper.save_results(results, "single_city_test")

    elif mode == 3:
        # Generate sample URLs
        print("🔗 Generating sample URLs for all cities...")
        for location in TARGET_LOCATIONS:
            urls = generate_sample_urls(location['city'], location['state'], 10)
            print(f"\n{location['city']}, {location['state']}:")
            for url in urls[:3]:
                print(f"   {url}")
            print(f"   ... and {len(urls)-3} more")

    print(f"\n💡 NOTES:")
    print(f"   • Phone numbers will have '1' prefix (1xxxxxxxxxx)")
    print(f"   • Only session duplicates are checked (no old data)")
    print(f"   • Results saved with city/state in filename")
    print(f"   • Completely FREE - no API costs!")

if __name__ == "__main__":
    main()
