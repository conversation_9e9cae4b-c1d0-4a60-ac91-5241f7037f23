#!/usr/bin/env python3
"""
State-specific web scraper
Allows user to input a state name and scrapes all cities in that state
"""

import sys
import os
import argparse
from typing import List, Dict
from web_scraper import WebScraper

class StateScraper(WebScraper):
    def __init__(self, mistral_api_key: str = None):
        """Initialize state scraper with Mistral API key"""
        super().__init__(mistral_api_key=mistral_api_key)
        
    def get_cities_by_state(self, state_name: str) -> List[Dict[str, str]]:
        """Get all cities for a specific state"""
        all_cities = self.parse_url_list()
        
        # Filter cities by state (case-insensitive)
        state_cities = []
        for city in all_cities:
            if city['state'].lower() == state_name.lower():
                state_cities.append(city)
        
        return state_cities
    
    def list_available_states(self) -> List[str]:
        """List all available states"""
        all_cities = self.parse_url_list()
        states = list(set(city['state'] for city in all_cities))
        return sorted(states)
    
    def scrape_state(self, state_name: str, max_cities: int = None, resume: bool = True) -> bool:
        """Scrape all cities in a specific state with resume capability"""
        self.logger.info(f"Starting state scrape for: {state_name}")

        # Set up progress file for this state
        state_safe_name = state_name.lower().replace(' ', '_').replace('-', '_')
        self.progress_file = f"state_{state_safe_name}_progress.json"

        # Load previous progress if resuming
        if resume:
            progress_data = self.load_progress()
            if progress_data:
                self.logger.info(f"Resuming {state_name} from previous session (last saved: {progress_data.get('timestamp', 'unknown')})")

        # Get cities for this state
        state_cities = self.get_cities_by_state(state_name)
        if not state_cities:
            self.logger.error(f"No cities found for state: {state_name}")
            self.logger.info("Available states:")
            for state in self.list_available_states():
                self.logger.info(f"  - {state}")
            return False

        # Limit cities if specified
        if max_cities:
            state_cities = state_cities[:max_cities]
            self.logger.info(f"Limited to first {max_cities} city-source combinations for testing")

        self.logger.info(f"Found {len(state_cities)} city-source combinations for {state_name}")

        # Group by city name to show progress
        cities_by_name = {}
        for city_info in state_cities:
            city_name = city_info['city']
            if city_name not in cities_by_name:
                cities_by_name[city_name] = []
            cities_by_name[city_name].append(city_info)

        self.logger.info(f"Will process {len(cities_by_name)} cities from both aaok and aypapi sources:")
        for city_name in sorted(cities_by_name.keys()):
            sources = [city['source'] for city in cities_by_name[city_name]]
            self.logger.info(f"  - {city_name} (sources: {', '.join(sources)})")

        # Process each city-source combination
        total_combinations = len(state_cities)
        processed_count = 0

        for i, city_info in enumerate(state_cities):
            city_name = city_info['city']
            source = city_info['source']

            self.logger.info(f"Processing combination {i+1}/{total_combinations}: {city_name} from {source}")

            # Skip if already processed
            city_key = f"{state_name}_{city_name}_{source}"
            if city_key in self.processed_cities:
                self.logger.info(f"Skipping already processed: {city_name} ({source})")
                continue

            try:
                # Scrape city data (multi-page, all available pages)
                city_data = self.scrape_city_pages(city_info)

                # Add to main data collection
                self.scraped_data.extend(city_data)

                # Mark as processed
                self.processed_cities.add(city_key)
                processed_count += 1

                # Save progress after every city-source combination
                progress_data = {
                    'state': state_name,
                    'current_city': city_name,
                    'current_source': source,
                    'progress_index': i + 1,
                    'total_combinations': total_combinations,
                    'processed_count': processed_count
                }
                self.save_progress(progress_data)

                # Save data checkpoint after every city-source combination
                checkpoint_name = f"state_{state_safe_name}_{city_name}_{source}_checkpoint.xlsx"
                self.save_data_checkpoint(checkpoint_name)

                # Rate limiting between city-source combinations
                time.sleep(self.request_delay)

            except Exception as e:
                self.logger.error(f"Error processing {city_name} from {source}: {e}")
                # Save progress even on error
                progress_data = {
                    'state': state_name,
                    'current_city': city_name,
                    'current_source': source,
                    'progress_index': i + 1,
                    'total_combinations': total_combinations,
                    'processed_count': processed_count,
                    'last_error': str(e)
                }
                self.save_progress(progress_data)
                continue

        # Final save
        final_file = f"state_{state_safe_name}_final.xlsx"
        self.save_to_excel(final_file)
        self.logger.info(f"State scrape completed. Final data saved to {final_file}")

        # Clean up progress file on successful completion
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                self.logger.info("Progress file cleaned up after successful completion")
        except Exception as e:
            self.logger.warning(f"Could not clean up progress file: {e}")

        return True

def main():
    """Main entry point for state scraper"""
    parser = argparse.ArgumentParser(description='State-specific Web Scraper with AI Enhancement and Resume')
    parser.add_argument('state', nargs='?', help='State name to scrape (e.g., "Alabama", "New York")')
    parser.add_argument('--list-states', action='store_true', help='List all available states')
    parser.add_argument('--max-cities', type=int, help='Maximum number of city-source combinations to process (for testing)')
    parser.add_argument('--delay', type=float, default=2.0, help='Delay between requests in seconds')
    parser.add_argument('--mistral-key', help='Mistral AI API key for enhanced text extraction')
    parser.add_argument('--no-resume', action='store_true', help='Start fresh without resuming from previous session')
    parser.add_argument('--clean-progress', action='store_true', help='Clean up progress files for this state and start fresh')

    args = parser.parse_args()

    # Get Mistral API key (use your key as default)
    mistral_key = args.mistral_key or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"

    # Create scraper instance
    scraper = StateScraper(mistral_api_key=mistral_key)
    scraper.request_delay = args.delay

    # List states if requested
    if args.list_states:
        print("Available states:")
        for state in scraper.list_available_states():
            print(f"  - {state}")
        return 0

    # Check if state provided
    if not args.state:
        print("Error: Please provide a state name or use --list-states to see available states")
        print("\nUsage examples:")
        print("  python state_scraper.py Alabama")
        print("  python state_scraper.py 'New York'")
        print("  python state_scraper.py --list-states")
        print("  python state_scraper.py Alabama --no-resume  # Start fresh")
        print("  python state_scraper.py Alabama --clean-progress  # Clean and start fresh")
        return 1

    # Clean progress files if requested
    if args.clean_progress:
        state_safe_name = args.state.lower().replace(' ', '_').replace('-', '_')
        progress_file = f"state_{state_safe_name}_progress.json"
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print(f"Cleaned up progress file: {progress_file}")

        # Also clean checkpoint files
        import glob
        checkpoint_pattern = f"state_{state_safe_name}_*_checkpoint.xlsx"
        checkpoint_files = glob.glob(checkpoint_pattern)
        for cf in checkpoint_files:
            os.remove(cf)
            print(f"Cleaned up checkpoint file: {cf}")

        print(f"Progress files cleaned for {args.state}. Starting fresh.")

    # Scrape the specified state with resume capability
    resume = not args.no_resume
    success = scraper.scrape_state(args.state, max_cities=args.max_cities, resume=resume)

    if success:
        print(f"\n✓ Successfully completed scraping for {args.state}")
        print(f"Total records collected: {len(scraper.scraped_data)}")

        # Show summary by source
        aaok_count = len([r for r in scraper.scraped_data if r.get('source') == 'aaok'])
        aypapi_count = len([r for r in scraper.scraped_data if r.get('source') == 'aypapi'])
        print(f"  - aaok.com: {aaok_count} records")
        print(f"  - aypapi.com: {aypapi_count} records")

        # Show summary by city
        cities = list(set(r.get('city') for r in scraper.scraped_data))
        print(f"Cities processed: {len(cities)}")

        return 0
    else:
        print(f"\n✗ Failed to scrape {args.state}")
        return 1

if __name__ == "__main__":
    import time
    sys.exit(main())
