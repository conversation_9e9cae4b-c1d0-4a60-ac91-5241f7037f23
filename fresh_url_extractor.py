#!/usr/bin/env python3
"""
Fresh URL Extractor - Extract current valid URLs from search pages
Uses only aaok and aypapi domains, ignores escortalligator
Extracts fresh URLs directly from current search pages to avoid 499 errors
"""

import requests
import re
import time
import json
from typing import List, Dict, Set, Optional
from bs4 import BeautifulSoup
from datetime import datetime
from urllib.parse import urljoin, urlparse

class FreshURLExtractor:
    def __init__(self, scraperapi_key: str = "********************************"):
        """Initialize fresh URL extractor"""

        self.scraperapi_key = scraperapi_key
        self.scraperapi_url = "https://api.scraperapi.com/"

        # Only use aaok and aypapi (no escortalligator)
        self.sources = ['aaok', 'aypapi']

        # Target cities from user request
        self.target_cities = [
            {"name": "South New Jersey", "state": "New Jersey", "url_name": "south%20new%20jersey"},
            {"name": "Philadelphia", "state": "Pennsylvania", "url_name": "philadelphia"},
            {"name": "Pittsburgh", "state": "Pennsylvania", "url_name": "pittsburgh"},
            {"name": "Wilmington", "state": "Delaware", "url_name": "wilmington"},
            {"name": "Dover", "state": "Delaware", "url_name": "dover"},
            {"name": "Baltimore", "state": "Maryland", "url_name": "baltimore"},
            {"name": "Annapolis", "state": "Maryland", "url_name": "annapolis"}
        ]

        # Statistics
        self.stats = {
            'search_pages_scraped': 0,
            'urls_extracted': 0,
            'api_requests': 0,
            'cities_processed': 0
        }

        print("🔄 Fresh URL Extractor initialized")
        print(f"🏙️ Target cities: {len(self.target_cities)}")
        print(f"📡 Sources: {', '.join(self.sources)}")

    def fetch_with_scraperapi(self, url: str) -> Optional[str]:
        """Fetch URL using ScraperAPI"""
        try:
            params = {
                'api_key': self.scraperapi_key,
                'url': url,
                'render': 'false',
                'country_code': 'us',
                'device_type': 'desktop'
            }

            response = requests.get(self.scraperapi_url, params=params, timeout=60)
            self.stats['api_requests'] += 1

            if response.status_code == 200:
                return response.text
            else:
                print(f"⚠️ ScraperAPI failed for {url}: Status {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ Error fetching {url}: {e}")
            return None

    def extract_profile_urls_from_html(self, html: str, source: str, base_url: str) -> List[Dict]:
        """Extract profile URLs from search page HTML with improved parsing"""
        if not html:
            return []

        try:
            soup = BeautifulSoup(html, 'html.parser')
            profile_urls = []

            # Look for multiple patterns based on the HTML structure you showed

            # Pattern 1: Links with class 'listtitle' (most common)
            listtitle_links = soup.find_all('a', class_='listtitle')

            # Pattern 2: Direct links to post/escorts paths
            all_links = soup.find_all('a', href=True)

            # Combine all potential links
            candidate_links = listtitle_links + all_links

            for link in candidate_links:
                href = link.get('href', '')

                # Only process links that contain 'post/escorts'
                if 'post/escorts' not in href:
                    continue

                # Skip escortalligator links (only aaok and aypapi)
                if 'escortalligator' in href:
                    continue

                # Ensure link belongs to current source
                if source not in href:
                    continue

                # Build full URL
                if href.startswith('http'):
                    full_url = href
                elif href.startswith('/'):
                    full_url = f"https://{source}.com.listcrawler.eu{href}"
                else:
                    full_url = urljoin(base_url, href)

                # Extract age information from surrounding elements
                age_info = self.extract_age_from_context(link)

                # Only include if age ≤ 30 or age not found (will filter in Phase 2)
                if age_info is None or age_info <= 30:
                    profile_urls.append({
                        'url': full_url,
                        'estimated_age': age_info,
                        'source': source
                    })

            # Remove duplicates
            seen_urls = set()
            unique_urls = []
            for url_data in profile_urls:
                if url_data['url'] not in seen_urls:
                    seen_urls.add(url_data['url'])
                    unique_urls.append(url_data)

            return unique_urls

        except Exception as e:
            print(f"❌ Error extracting URLs from HTML: {e}")
            return []

    def extract_age_from_context(self, link_element) -> Optional[int]:
        """Extract age information from link context"""
        try:
            # Check the link text itself
            link_text = link_element.get_text(strip=True)
            age_match = re.search(r'\b(\d{2})\b', link_text)
            if age_match:
                age = int(age_match.group(1))
                if 18 <= age <= 99:  # Reasonable age range
                    return age

            # Check parent elements for age info
            parent = link_element.parent
            for _ in range(3):  # Check up to 3 parent levels
                if parent is None:
                    break

                # Look for age in class names like 'titleAge'
                age_divs = parent.find_all('div', class_=re.compile(r'.*[Aa]ge.*'))
                for age_div in age_divs:
                    age_text = age_div.get_text(strip=True)
                    age_match = re.search(r'\b(\d{2})\b', age_text)
                    if age_match:
                        age = int(age_match.group(1))
                        if 18 <= age <= 99:
                            return age

                # Check all text in parent for age patterns
                parent_text = parent.get_text()
                age_patterns = [
                    r'Age[:\s]*(\d{2})',
                    r'(\d{2})\s*years?\s*old',
                    r'I\'m\s*(\d{2})',
                    r'Age\s*(\d{2})',
                    r'\b(\d{2})\b'  # Just two digits
                ]

                for pattern in age_patterns:
                    age_match = re.search(pattern, parent_text, re.IGNORECASE)
                    if age_match:
                        age = int(age_match.group(1))
                        if 18 <= age <= 99:
                            return age

                parent = parent.parent

            return None

        except Exception:
            return None

    def scrape_city_fresh_urls(self, city_info: Dict, max_pages: int = 15) -> List[Dict]:
        """Scrape fresh URLs from current search pages for a city"""
        city_name = city_info['name']
        state_name = city_info['state']
        url_name = city_info['url_name']

        print(f"\n🏙️ Extracting fresh URLs for {city_name}, {state_name}")

        all_urls = []

        for source in self.sources:
            print(f"   📡 Scraping {source.upper()} search pages...")

            # Build base URL
            state_url = state_name.lower().replace(' ', '%20')
            base_url = f"https://{source}.com.listcrawler.eu/brief/escorts/usa/{state_url}/{url_name}"

            page = 1
            consecutive_empty = 0

            while page <= max_pages and consecutive_empty < 3:
                search_url = f"{base_url}/{page}"
                print(f"      📄 Page {page}: {search_url}")

                # Fetch search page
                html = self.fetch_with_scraperapi(search_url)
                if not html:
                    consecutive_empty += 1
                    page += 1
                    continue

                # Extract URLs from this page
                page_urls = self.extract_profile_urls_from_html(html, source, base_url)

                if not page_urls:
                    consecutive_empty += 1
                    print(f"         ❌ No URLs found on page {page}")
                else:
                    consecutive_empty = 0
                    # Add city/state info to each URL
                    for url_data in page_urls:
                        url_data.update({
                            'city': city_name,
                            'state': state_name,
                            'page': page
                        })

                    all_urls.extend(page_urls)
                    print(f"         ✅ Found {len(page_urls)} URLs on page {page}")

                self.stats['search_pages_scraped'] += 1
                page += 1
                time.sleep(1)  # Rate limiting

            source_count = len([u for u in all_urls if u['source'] == source])
            print(f"   ✅ {source.upper()}: {source_count} fresh URLs")

        print(f"🎯 {city_name} total: {len(all_urls)} fresh URLs")
        self.stats['urls_extracted'] += len(all_urls)
        self.stats['cities_processed'] += 1

        return all_urls

    def validate_url(self, url: str) -> bool:
        """Quick validation of URL format"""
        try:
            parsed = urlparse(url)
            return (
                parsed.scheme in ['http', 'https'] and
                'listcrawler.eu' in parsed.netloc and
                'post/escorts' in parsed.path and
                ('aaok' in parsed.netloc or 'aypapi' in parsed.netloc)
            )
        except:
            return False

    def extract_all_fresh_urls(self) -> List[Dict]:
        """Extract fresh URLs from all target cities"""
        print("🚀 FRESH URL EXTRACTION - Starting")
        print("=" * 60)

        all_fresh_urls = []

        for city_info in self.target_cities:
            city_urls = self.scrape_city_fresh_urls(city_info)

            # Validate URLs
            valid_urls = [url_data for url_data in city_urls if self.validate_url(url_data['url'])]
            invalid_count = len(city_urls) - len(valid_urls)

            if invalid_count > 0:
                print(f"   🔍 Filtered out {invalid_count} invalid URLs")

            all_fresh_urls.extend(valid_urls)

            # Save city results
            city_filename = f"fresh_urls_{city_info['name'].replace(' ', '_')}_{city_info['state'].replace(' ', '_')}.json"
            with open(city_filename, 'w') as f:
                json.dump(valid_urls, f, indent=2)
            print(f"💾 Saved {len(valid_urls)} URLs to {city_filename}")

        # Save all fresh URLs
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        all_urls_filename = f"fresh_all_urls_{timestamp}.json"
        with open(all_urls_filename, 'w') as f:
            json.dump(all_fresh_urls, f, indent=2)

        # Print final statistics
        print(f"\n🎯 FRESH URL EXTRACTION COMPLETE")
        print(f"=" * 60)
        print(f"✅ Total fresh URLs: {len(all_fresh_urls)}")
        print(f"🏙️ Cities processed: {self.stats['cities_processed']}")
        print(f"📄 Search pages scraped: {self.stats['search_pages_scraped']}")
        print(f"🌐 API requests made: {self.stats['api_requests']}")
        print(f"💾 All URLs saved to: {all_urls_filename}")

        # URL breakdown by source
        source_breakdown = {}
        for url_data in all_fresh_urls:
            source = url_data['source']
            source_breakdown[source] = source_breakdown.get(source, 0) + 1

        print(f"\n📡 URLs by Source:")
        for source, count in source_breakdown.items():
            print(f"   {source}: {count} URLs")

        print(f"=" * 60)
        return all_fresh_urls

def main():
    """Main execution"""
    import argparse

    parser = argparse.ArgumentParser(description='Fresh URL Extractor')
    parser.add_argument('--max-pages', type=int, default=15, help='Maximum pages per city/source')

    args = parser.parse_args()

    # Initialize extractor
    extractor = FreshURLExtractor()

    try:
        # Extract fresh URLs
        fresh_urls = extractor.extract_all_fresh_urls()

        if fresh_urls:
            print(f"\n🎉 SUCCESS: {len(fresh_urls)} fresh URLs extracted")
            print(f"📁 Ready for Phase 2 processing")
        else:
            print(f"\n⚠️ No URLs extracted")

    except KeyboardInterrupt:
        print(f"\n🛑 Extraction interrupted by user")
    except Exception as e:
        print(f"\n❌ Extraction failed: {e}")

if __name__ == "__main__":
    main()
