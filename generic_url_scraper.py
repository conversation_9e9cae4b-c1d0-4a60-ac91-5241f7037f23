#!/usr/bin/env python3
"""
Generic URL Scraper: Extract all URLs from search pages for any state and city
Works with the existing web scraper infrastructure like state_scraper.py
"""

import sys
import os
import json
import time
import argparse
from typing import List, Dict, Optional
from datetime import datetime
import re
import logging

from web_scraper import WebScraper


class GenericURLScraper(WebScraper):
    def __init__(self, mistral_api_key: str = None, output_file: str = "extracted_urls.json"):
        """Initialize generic URL scraper"""
        super().__init__(mistral_api_key=mistral_api_key)
        self.output_file = output_file

    def get_cities_by_state(self, state_name: str) -> List[Dict[str, str]]:
        """Get all cities for a specific state"""
        all_cities = self.parse_url_list()

        # Filter cities by state (case-insensitive)
        state_cities = []
        for city in all_cities:
            if city['state'].lower() == state_name.lower():
                state_cities.append(city)

        return state_cities

    def get_city_combinations(self, state_name: str, city_name: str = None) -> List[Dict[str, str]]:
        """Get city-source combinations for specific state and optionally city"""
        state_cities = self.get_cities_by_state(state_name)

        if not state_cities:
            return []

        # Filter by city if specified
        if city_name:
            filtered_cities = []
            for city_info in state_cities:
                if city_info['city'].lower() == city_name.lower():
                    filtered_cities.append(city_info)
            return filtered_cities

        return state_cities

    def list_available_states(self) -> List[str]:
        """List all available states"""
        all_cities = self.parse_url_list()
        states = list(set(city['state'] for city in all_cities))
        return sorted(states)

    def list_cities_in_state(self, state_name: str) -> List[str]:
        """List all available cities in a specific state"""
        state_cities = self.get_cities_by_state(state_name)
        cities = list(set(city['city'] for city in state_cities))
        return sorted(cities)

    def extract_urls_from_city(self, city_info: Dict[str, str]) -> List[str]:
        """Extract all dedicated page URLs from all search pages for a city-source combination"""
        city_name = city_info['city']
        source = city_info['source']
        base_url = city_info['url']

        self.logger.info(f"Extracting URLs from {city_name} ({source})")

        all_dedicated_urls = []
        page_num = 1
        empty_page_count = 0

        while True:
            # Construct page URL
            page_url = re.sub(r'/\d+$', f'/{page_num}', base_url)
            self.logger.info(f"  Processing page {page_num}: {page_url}")

            # Get search page HTML
            search_html = self.execute_curl_request(page_url, self.search_curl_template)
            if not search_html:
                self.logger.warning(f"  Failed to get page {page_num}")
                empty_page_count += 1
            else:
                # Extract dedicated page URLs (filtered by age ≤30)
                page_dedicated_urls = self.extract_dedicated_urls(search_html)
                if not page_dedicated_urls:
                    empty_page_count += 1
                    self.logger.info(f"  No URLs found on page {page_num} (empty page {empty_page_count}/{self.max_consecutive_empty_pages})")
                else:
                    empty_page_count = 0
                    all_dedicated_urls.extend(page_dedicated_urls)
                    self.logger.info(f"  Found {len(page_dedicated_urls)} URLs on page {page_num}")

            # Check stopping conditions
            if page_num > self.min_pages_to_scrape and empty_page_count >= self.max_consecutive_empty_pages:
                self.logger.info(f"  Stopping after {self.max_consecutive_empty_pages} consecutive empty pages (scraped {page_num} pages)")
                break
            elif page_num <= self.min_pages_to_scrape:
                self.logger.info(f"  Continuing - need minimum {self.min_pages_to_scrape} pages (currently at {page_num})")

            time.sleep(self.request_delay)
            page_num += 1

            if page_num > self.max_pages_per_city:
                self.logger.info(f"  Reached maximum page limit ({self.max_pages_per_city})")
                break

        # Remove duplicates while preserving order
        unique_urls = list(dict.fromkeys(all_dedicated_urls))
        self.logger.info(f"  Completed {city_name} ({source}): {len(unique_urls)} unique URLs from {page_num-1} pages")

        return unique_urls

    def extract_all_urls(self, state_name: str, city_name: str = None, max_cities: int = None, force_refresh: bool = False) -> bool:
        """Extract all dedicated page URLs from search pages"""
        self.logger.info("=" * 60)
        self.logger.info("EXTRACTING ALL URLS FROM SEARCH PAGES")
        self.logger.info("=" * 60)

        # Generate output filename based on parameters
        if city_name:
            safe_state = state_name.lower().replace(' ', '_').replace('-', '_')
            safe_city = city_name.lower().replace(' ', '_').replace('-', '_')
            self.output_file = f"urls_{safe_state}_{safe_city}.json"
        else:
            safe_state = state_name.lower().replace(' ', '_').replace('-', '_')
            self.output_file = f"urls_{safe_state}_all.json"

        # Check if URLs file already exists
        if os.path.exists(self.output_file) and not force_refresh:
            self.logger.info(f"URLs file {self.output_file} already exists. Loading existing data...")
            try:
                with open(self.output_file, 'r') as f:
                    existing_data = json.load(f)
                self.logger.info(f"Loaded {len(existing_data)} existing URL collections")
                return True
            except Exception as e:
                self.logger.warning(f"Failed to load existing URLs file: {e}")

        # Get city combinations
        combinations = self.get_city_combinations(state_name, city_name)

        if not combinations:
            if city_name:
                self.logger.error(f"No combinations found for {city_name} in {state_name}")
                self.logger.info(f"Available cities in {state_name}:")
                for city in self.list_cities_in_state(state_name):
                    self.logger.info(f"  - {city}")
            else:
                self.logger.error(f"No cities found for state: {state_name}")
                self.logger.info("Available states:")
                for state in self.list_available_states():
                    self.logger.info(f"  - {state}")
            return False

        # Limit combinations if specified
        if max_cities:
            combinations = combinations[:max_cities]
            self.logger.info(f"Limited to first {max_cities} city-source combinations for testing")

        self.logger.info(f"Found {len(combinations)} city-source combinations to process")

        # Group by city name to show what will be processed
        cities_by_name = {}
        for combo in combinations:
            city_name_key = combo['city']
            if city_name_key not in cities_by_name:
                cities_by_name[city_name_key] = []
            cities_by_name[city_name_key].append(combo)

        self.logger.info(f"Will process {len(cities_by_name)} cities from available sources:")
        for city_name_key in sorted(cities_by_name.keys()):
            sources = [combo['source'] for combo in cities_by_name[city_name_key]]
            self.logger.info(f"  - {city_name_key} (sources: {', '.join(sources)})")

        all_urls_data = {}

        for i, combo in enumerate(combinations):
            city_name_key = combo['city']
            source = combo['source']
            state = combo['state']
            combo_key = f"{state}_{city_name_key}_{source}"

            self.logger.info(f"Processing {i+1}/{len(combinations)}: {city_name_key} ({state}) from {source}")

            try:
                # Extract URLs from all search pages for this combination
                unique_urls = self.extract_urls_from_city(combo)

                # Store URLs data
                all_urls_data[combo_key] = {
                    'state': state,
                    'city': city_name_key,
                    'source': source,
                    'base_url': combo['url'],
                    'total_urls': len(unique_urls),
                    'urls': unique_urls,
                    'extracted_at': datetime.now().isoformat()
                }

                # Save progress after each combination
                try:
                    with open(self.output_file, 'w') as f:
                        json.dump(all_urls_data, f, indent=2)
                    self.logger.info(f"  Saved progress to {self.output_file}")
                except Exception as e:
                    self.logger.error(f"  Failed to save progress: {e}")

                # Rate limiting between combinations
                time.sleep(self.request_delay)

            except Exception as e:
                self.logger.error(f"Error processing {city_name_key} from {source}: {e}")
                continue

        # Final summary
        total_urls = sum(data['total_urls'] for data in all_urls_data.values())

        self.logger.info("=" * 60)
        self.logger.info("URL EXTRACTION COMPLETED - SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"Total combinations processed: {len(all_urls_data)}")
        self.logger.info(f"Total dedicated URLs extracted: {total_urls}")

        for combo_key, data in all_urls_data.items():
            self.logger.info(f"  {data['city']} ({data['state']}, {data['source']}): {data['total_urls']} URLs")

        self.logger.info(f"All URLs saved to: {self.output_file}")
        return True

    def get_url_stats(self, urls_file: str = None) -> Optional[Dict]:
        """Get statistics about extracted URLs"""
        file_to_check = urls_file or self.output_file

        if not os.path.exists(file_to_check):
            self.logger.warning(f"URLs file {file_to_check} not found")
            return None

        try:
            with open(file_to_check, 'r') as f:
                data = json.load(f)

            stats = {
                'total_combinations': len(data),
                'total_urls': sum(combo['total_urls'] for combo in data.values()),
                'by_state': {},
                'by_city': {},
                'by_source': {}
            }

            for combo_key, combo_data in data.items():
                state = combo_data['state']
                city = combo_data['city']
                source = combo_data['source']

                # By state
                if state not in stats['by_state']:
                    stats['by_state'][state] = {'urls': 0, 'cities': set()}
                stats['by_state'][state]['urls'] += combo_data['total_urls']
                stats['by_state'][state]['cities'].add(city)

                # By city
                city_key = f"{city} ({state})"
                if city_key not in stats['by_city']:
                    stats['by_city'][city_key] = {'urls': 0, 'sources': []}
                stats['by_city'][city_key]['urls'] += combo_data['total_urls']
                stats['by_city'][city_key]['sources'].append(source)

                # By source
                if source not in stats['by_source']:
                    stats['by_source'][source] = {'urls': 0, 'combinations': 0}
                stats['by_source'][source]['urls'] += combo_data['total_urls']
                stats['by_source'][source]['combinations'] += 1

            # Convert sets to counts for JSON serialization
            for state_data in stats['by_state'].values():
                state_data['cities'] = len(state_data['cities'])

            return stats

        except Exception as e:
            self.logger.error(f"Failed to get URL stats: {e}")
            return None

    def export_urls_list(self, output_file: str = "urls_list.txt", urls_file: str = None) -> bool:
        """Export all URLs to a simple text file (one URL per line)"""
        file_to_read = urls_file or self.output_file

        if not os.path.exists(file_to_read):
            self.logger.warning(f"URLs file {file_to_read} not found")
            return False

        try:
            with open(file_to_read, 'r') as f:
                data = json.load(f)

            all_urls = []
            for combo_data in data.values():
                all_urls.extend(combo_data['urls'])

            # Remove duplicates while preserving order
            unique_urls = list(dict.fromkeys(all_urls))

            with open(output_file, 'w') as f:
                for url in unique_urls:
                    f.write(url + '\n')

            self.logger.info(f"Exported {len(unique_urls)} unique URLs to {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to export URLs list: {e}")
            return False


def main():
    """Main entry point for generic URL scraper"""
    parser = argparse.ArgumentParser(description='Generic URL Scraper for any State and City')
    parser.add_argument('state', nargs='?', help='State name to scrape (e.g., "Alabama", "New York")')
    parser.add_argument('--city', help='Specific city to scrape (optional - if not provided, scrapes all cities in state)')
    parser.add_argument('--output', help='Output JSON file for extracted URLs (auto-generated if not provided)')
    parser.add_argument('--list-states', action='store_true', help='List all available states')
    parser.add_argument('--list-cities', help='List all available cities in specified state')
    parser.add_argument('--max-cities', type=int, help='Maximum number of city-source combinations to process (for testing)')
    parser.add_argument('--force-refresh', action='store_true', help='Force refresh even if output file exists')
    parser.add_argument('--stats', help='Show statistics about extracted URLs from specified file')
    parser.add_argument('--export-list', help='Export URLs to simple text file (one URL per line)')
    parser.add_argument('--delay', type=float, default=0.5, help='Delay between requests in seconds (default: 0.5)')
    parser.add_argument('--mistral-key', help='Mistral AI API key (optional - defaults to built-in key)')

    args = parser.parse_args()

    # Get Mistral API key
    mistral_key = args.mistral_key or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"

    # Create scraper
    output_file = args.output or "extracted_urls.json"
    scraper = GenericURLScraper(mistral_api_key=mistral_key, output_file=output_file)
    scraper.request_delay = args.delay

    # List states if requested
    if args.list_states:
        print("Available states:")
        for state in scraper.list_available_states():
            print(f"  - {state}")
        return 0

    # List cities in state if requested
    if args.list_cities:
        print(f"Available cities in {args.list_cities}:")
        cities = scraper.list_cities_in_state(args.list_cities)
        if cities:
            for city in cities:
                print(f"  - {city}")
        else:
            print(f"No cities found for state: {args.list_cities}")
            print("\nAvailable states:")
            for state in scraper.list_available_states():
                print(f"  - {state}")
        return 0

    # Show stats if requested
    if args.stats:
        print("Getting URL statistics...")
        stats = scraper.get_url_stats(args.stats)
        if stats:
            print("\nURL Statistics:")
            print("-" * 30)
            print(f"Total combinations: {stats['total_combinations']}")
            print(f"Total URLs: {stats['total_urls']}")

            print("\nBy State:")
            for state, data in stats['by_state'].items():
                print(f"  {state}: {data['urls']} URLs from {data['cities']} cities")

            print("\nBy City:")
            for city, data in stats['by_city'].items():
                sources_str = ', '.join(data['sources'])
                print(f"  {city}: {data['urls']} URLs (sources: {sources_str})")

            print("\nBy Source:")
            for source, data in stats['by_source'].items():
                print(f"  {source}: {data['urls']} URLs from {data['combinations']} combinations")
        else:
            print("No statistics available (URLs file not found or corrupted)")
        return 0

    # Export URLs list if requested
    if args.export_list:
        print(f"Exporting URLs to {args.export_list}...")
        urls_file = args.output if args.output else None
        success = scraper.export_urls_list(args.export_list, urls_file)
        if success:
            print("URLs exported successfully!")
        else:
            print("Failed to export URLs")
        return 0

    # Check if state provided
    if not args.state:
        print("Error: Please provide a state name or use --list-states to see available states")
        print("\nUsage examples:")
        print("  python generic_url_scraper.py Alabama")
        print("  python generic_url_scraper.py 'New York' --city 'New York City'")
        print("  python generic_url_scraper.py --list-states")
        print("  python generic_url_scraper.py --list-cities Alabama")
        print("  python generic_url_scraper.py Alabama --max-cities 5  # Test with 5 combinations")
        return 1

    print("Generic URL Scraper")
    print("=" * 50)
    print(f"Target state: {args.state}")
    if args.city:
        print(f"Target city: {args.city}")
    else:
        print("Target: All cities in state")
    print(f"Request delay: {args.delay}s")
    print()

    start_time = time.time()

    # Extract URLs
    print("Starting URL extraction...")
    success = scraper.extract_all_urls(
        state_name=args.state,
        city_name=args.city,
        max_cities=args.max_cities,
        force_refresh=args.force_refresh
    )

    end_time = time.time()
    processing_time = end_time - start_time

    if success:
        # Get final stats
        stats = scraper.get_url_stats()

        print()
        print("=" * 50)
        print("URL EXTRACTION COMPLETED!")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Results saved to: {scraper.output_file}")

        if stats:
            print(f"Total URLs extracted: {stats['total_urls']}")
            print(f"Total combinations processed: {stats['total_combinations']}")

        print("=" * 50)
        return 0
    else:
        print("URL extraction failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
