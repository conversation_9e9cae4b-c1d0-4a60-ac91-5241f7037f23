#!/usr/bin/env python3
"""
Synchronous Playwright Scraper - Avoids async context issues
Handles JavaScript rendering and anti-bot protection properly
"""

import json
import os
import time
import argparse
from datetime import datetime
from typing import List, Dict, Optional
import pandas as pd
import re
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright
import random

class SyncPlaywrightScraper:
    def __init__(self, headless: bool = True):
        self.headless = headless
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results = []
        self.processed_count = 0

        # Rate limiting
        self.min_delay = 2.0  # Minimum delay between requests
        self.max_delay = 5.0  # Maximum delay between requests

    def extract_data_from_html(self, html: str, url: str) -> Dict:
        """Extract profile data from HTML using proper selectors"""

        data = {
            'url': url,
            'name': '',
            'age': '',
            'phone': '',
            'location': '',
            'description': '',
            'website_type': '',
            'extraction_success': False,
            'scraped_at': datetime.now().isoformat(),
            'content_length': len(html)
        }

        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Check for anti-bot protection
            protection_indicators = [
                "Just a moment",
                "Enable JavaScript and cookies",
                "Checking your browser",
                "Please wait while we verify",
                "Ray ID:",
                "Cloudflare"
            ]

            if any(phrase in html for phrase in protection_indicators):
                print(f"⚠️  Anti-bot protection detected for {url}")
                data['error'] = 'Anti-bot protection detected'
                return data

            # Detect website type
            domain = url.lower()
            if 'aypapi' in domain:
                data['website_type'] = 'aypapi'
            elif 'aaok' in domain:
                data['website_type'] = 'aaok'
            else:
                data['website_type'] = 'unknown'

            # Extract name from title
            name_elem = soup.select_one('.viewposttitle')
            if name_elem:
                data['name'] = name_elem.get_text(strip=True)

            # Extract age
            age_elem = soup.select_one('.postTitleAge')
            if age_elem:
                age_text = age_elem.get_text(strip=True)
                if age_text.isdigit():
                    data['age'] = age_text

            # Extract phone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                data['phone'] = phone_elem.get_text(strip=True)

            # Extract location/description from other elements
            body_elem = soup.select_one('.viewpostbody')
            if body_elem:
                data['description'] = body_elem.get_text(strip=True)[:500]  # Limit description length

            # Look for title in page
            title = soup.title
            if title:
                data['page_title'] = title.get_text(strip=True)

            # Check if we got meaningful data
            if data['name'] or data['age'] or data['phone']:
                data['extraction_success'] = True
                print(f"✅ Extracted: Name='{data['name']}', Age={data['age']}, Phone='{data['phone']}'")
            else:
                print(f"❌ No meaningful data extracted")

        except Exception as e:
            print(f"❌ Error extracting data from {url}: {e}")
            data['error'] = str(e)

        return data

    def fetch_page(self, page, url: str) -> Optional[str]:
        """Fetch a single page with proper error handling"""

        max_retries = 3

        for attempt in range(max_retries):
            try:
                print(f"📡 Fetching {url} (attempt {attempt + 1}/{max_retries})")

                # Navigate to page
                response = page.goto(url, wait_until='domcontentloaded', timeout=30000)

                if not response or response.status != 200:
                    print(f"❌ Bad response: {response.status if response else 'No response'}")
                    if attempt < max_retries - 1:
                        time.sleep(5)  # Wait before retry
                        continue
                    return None

                # Wait for content to load
                time.sleep(3)

                # Get content
                html = page.content()

                if len(html) < 1000:
                    print(f"⚠️  Content too short: {len(html)} chars")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue

                # Check for anti-bot protection and wait if needed
                if "Just a moment" in html or "Enable JavaScript and cookies" in html:
                    print(f"⚠️  Anti-bot protection detected, waiting 10 seconds...")
                    time.sleep(10)  # Wait 10 seconds
                    html = page.content()

                print(f"✅ Fetched {len(html)} characters")
                return html

            except Exception as e:
                print(f"❌ Attempt {attempt + 1} failed: {str(e)[:100]}")
                if attempt < max_retries - 1:
                    time.sleep(5 + attempt * 2)  # Increasing delay
                    continue

        print(f"❌ Failed to fetch {url} after {max_retries} attempts")
        return None

    def process_urls(self, urls: List[str], max_urls: Optional[int] = None) -> List[Dict]:
        """Process URLs sequentially to avoid rate limiting"""

        if max_urls:
            urls = urls[:max_urls]

        results = []

        print(f"🚀 Starting to process {len(urls)} URLs")

        with sync_playwright() as p:
            # Launch browser
            browser = p.chromium.launch(
                headless=self.headless,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-web-security',
                    '--disable-dev-shm-usage',
                    '--disable-gpu'
                ]
            )

            try:
                # Create context
                context = browser.new_context(
                    viewport={'width': 1920, 'height': 1080},
                    user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    extra_http_headers={
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1'
                    }
                )

                # Create page
                page = context.new_page()

                # Process URLs one by one
                for i, url in enumerate(urls):
                    print(f"\n📋 Processing {i+1}/{len(urls)}: {url}")

                    # Rate limiting delay
                    if i > 0:
                        delay = self.min_delay + random.uniform(0, 2)  # Random delay 2-4 seconds
                        print(f"⏱️  Waiting {delay:.1f}s...")
                        time.sleep(delay)

                    # Fetch page
                    html = self.fetch_page(page, url)

                    if html:
                        # Extract data
                        data = self.extract_data_from_html(html, url)
                        results.append(data)
                    else:
                        # Add failed entry
                        failed_data = {
                            'url': url,
                            'extraction_success': False,
                            'error': 'Failed to fetch page',
                            'scraped_at': datetime.now().isoformat()
                        }
                        results.append(failed_data)

                    # Save intermediate results every 25 URLs
                    if (i + 1) % 25 == 0:
                        self.save_results(results, f"checkpoint_{i+1}")
                        print(f"💾 Saved checkpoint at {i+1} URLs")

            finally:
                browser.close()

        print(f"\n🏁 Completed processing {len(urls)} URLs")
        return results

    def save_results(self, results: List[Dict], suffix: str = "final"):
        """Save results to Excel file"""

        if not results:
            print("⚠️  No results to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"sync_playwright_{len(results)}_urls_{timestamp}_{suffix}.xlsx"

        try:
            df = pd.DataFrame(results)

            # Reorder columns
            column_order = [
                'url', 'name', 'age', 'phone', 'location', 'description',
                'website_type', 'extraction_success', 'page_title', 'content_length', 'scraped_at', 'error'
            ]

            # Only include columns that exist
            existing_columns = [col for col in column_order if col in df.columns]
            df = df[existing_columns]

            # Create multiple sheets
            with pd.ExcelWriter(filename) as writer:
                # All data
                df.to_excel(writer, sheet_name='All_Data', index=False)

                # Successful extractions only
                successful_df = df[df['extraction_success'] == True]
                if not successful_df.empty:
                    successful_df.to_excel(writer, sheet_name='Successful', index=False)

                # Failed extractions
                failed_df = df[df['extraction_success'] == False]
                if not failed_df.empty:
                    failed_df.to_excel(writer, sheet_name='Failed', index=False)

            # Calculate statistics
            successful = len(df[df['extraction_success'] == True])
            failed = len(df[df['extraction_success'] == False])
            success_rate = (successful / len(df)) * 100 if len(df) > 0 else 0

            print(f"💾 Saved {len(df)} records to {filename}")
            print(f"📊 Results: {successful} successful, {failed} failed ({success_rate:.1f}% success rate)")

            # Show sample of successful extractions
            if not successful_df.empty:
                print(f"✅ Sample successful extractions:")
                for i, row in successful_df.head(5).iterrows():
                    print(f"   • Name: '{row.get('name', 'N/A')}' | Age: {row.get('age', 'N/A')} | Phone: '{row.get('phone', 'N/A')}'")

            # Show failed reasons
            if not failed_df.empty:
                error_counts = failed_df['error'].value_counts()
                print(f"❌ Failure reasons:")
                for error, count in error_counts.items():
                    print(f"   • {error}: {count} URLs")

        except Exception as e:
            print(f"❌ Error saving results: {e}")

def main():
    parser = argparse.ArgumentParser(description="Synchronous Playwright Scraper for NYC URLs")
    parser.add_argument('--max-urls', type=int, help='Maximum URLs to process')
    parser.add_argument('--visible', action='store_true', help='Run browser in visible mode')

    args = parser.parse_args()

    scraper = SyncPlaywrightScraper(headless=not args.visible)

    # Load URLs
    if not os.path.exists(scraper.urls_file):
        print(f"❌ URLs file not found: {scraper.urls_file}")
        return False

    print(f"📂 Loading URLs from {scraper.urls_file}")
    with open(scraper.urls_file, 'r') as f:
        url_data = json.load(f)

    # Extract all URLs
    all_urls = []
    for borough_source, data in url_data.items():
        if 'urls' in data and data['urls']:
            all_urls.extend(data['urls'])
            print(f"📍 Loaded {len(data['urls'])} URLs from {borough_source}")

    print(f"📊 Total URLs loaded: {len(all_urls)}")

    if args.max_urls:
        print(f"🧪 Testing mode: limiting to {args.max_urls} URLs")

    # Process URLs
    try:
        start_time = time.time()
        results = scraper.process_urls(all_urls, args.max_urls)

        # Save final results
        scraper.save_results(results, "final")

        end_time = time.time()
        processing_time = (end_time - start_time) / 60

        print(f"\n🎉 Scraping completed successfully!")
        print(f"⏰ Total processing time: {processing_time:.1f} minutes")
        return True

    except KeyboardInterrupt:
        print(f"\n⚠️  Scraping interrupted by user")
        print(f"💾 Saving partial results...")
        scraper.save_results(scraper.results, "interrupted")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    result = main()
    exit(0 if result else 1)
