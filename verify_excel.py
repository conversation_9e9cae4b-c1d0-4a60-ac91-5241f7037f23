#!/usr/bin/env python3
"""
Verify the Excel output format
"""

import pandas as pd
import sys

def verify_excel_output(filename="quick_test_output.xlsx"):
    """Verify the Excel file format and content"""
    try:
        # Read the Excel file
        df = pd.read_excel(filename)
        
        print(f"Excel File Verification: {filename}")
        print("=" * 50)
        
        # Basic info
        print(f"Total records: {len(df)}")
        print(f"Total columns: {len(df.columns)}")
        print()
        
        # Column names
        print("Columns:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        print()
        
        # Sample data
        print("Sample Records:")
        for i, row in df.head(3).iterrows():
            print(f"\nRecord {i+1}:")
            for col in df.columns:
                value = row[col]
                if pd.isna(value):
                    value = "[EMPTY]"
                elif isinstance(value, str) and len(value) > 60:
                    value = value[:60] + "..."
                print(f"  {col}: {value}")
        
        # Data quality check
        print("\nData Quality Check:")
        print(f"  Records with phone numbers: {df['phone'].notna().sum()}")
        print(f"  Records with titles: {df['title'].notna().sum()}")
        print(f"  Records with descriptions: {df['description'].notna().sum()}")
        print(f"  Records with ages: {df['age'].notna().sum()}")
        
        # Unique values
        print(f"\nUnique cities: {df['city'].nunique()}")
        print(f"Unique states: {df['state'].nunique()}")
        
        return True
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return False

if __name__ == "__main__":
    filename = sys.argv[1] if len(sys.argv) > 1 else "quick_test_output.xlsx"
    verify_excel_output(filename)
