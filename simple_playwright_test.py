#!/usr/bin/env python3
"""
Simple Playwright test - tests a single URL to verify functionality
"""

import asyncio
import json
import os
from datetime import datetime
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async

async def test_single_url():
    """Test Playwright with a single URL from the dataset"""

    # Load one URL from the dataset
    urls_file = "parallel_nyc_all_urls_deduplicated.json"
    test_url = None

    if os.path.exists(urls_file):
        with open(urls_file, 'r') as f:
            url_data = json.load(f)

        # Extract first URL from the nested structure
        for borough_source, data in url_data.items():
            if 'urls' in data and data['urls']:
                test_url = data['urls'][0]
                print(f"Testing with URL from {borough_source}: {test_url}")
                break

    if not test_url:
        print("❌ No test URL available")
        return False

    print(f"\n🚀 Testing Playwright with: {test_url}")

    async with async_playwright() as p:
        # Launch browser
        browser = await p.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-extensions'
            ]
        )

        # Create context
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )

        # Create page with stealth
        page = await context.new_page()
        await stealth_async(page)

        try:
            print("📡 Fetching page content...")

            # Navigate to page
            response = await page.goto(test_url, wait_until='domcontentloaded', timeout=30000)

            if response:
                print(f"✅ Response status: {response.status}")

                # Wait for content to load
                await page.wait_for_timeout(3000)

                # Get content
                content = await page.content()
                print(f"📄 Content length: {len(content)} characters")

                # Check for anti-bot protection
                if "Just a moment" in content or "Enable JavaScript and cookies" in content:
                    print("⚠️  Anti-bot protection detected, waiting longer...")
                    await page.wait_for_timeout(5000)
                    content = await page.content()
                    print(f"📄 Updated content length: {len(content)} characters")

                # Check if we got meaningful content
                if len(content) > 1000:
                    print("✅ Got substantial content")

                    # Look for profile indicators
                    indicators = ['profile', 'age', 'escort', 'phone', 'name']
                    found_indicators = [ind for ind in indicators if ind.lower() in content.lower()]

                    if found_indicators:
                        print(f"✅ Found profile indicators: {found_indicators}")

                        # Try to extract basic info
                        from bs4 import BeautifulSoup
                        soup = BeautifulSoup(content, 'html.parser')

                        # Look for age
                        age_elem = soup.find('div', class_='titleAge')
                        if age_elem:
                            print(f"📊 Found age: {age_elem.get_text(strip=True)}")

                        # Look for title/name
                        title_elem = soup.find('h1')
                        if title_elem:
                            print(f"👤 Found title: {title_elem.get_text(strip=True)[:50]}...")

                        print("🎉 Test SUCCESSFUL - Playwright can extract data!")
                        return True
                    else:
                        print("⚠️  Content doesn't look like a profile page")
                        print(f"Sample content: {content[:200]}...")
                        return False
                else:
                    print("❌ Content too short, likely blocked")
                    print(f"Content sample: {content[:200]}")
                    return False
            else:
                print("❌ No response received")
                return False

        except Exception as e:
            print(f"❌ Error during page fetch: {e}")
            return False

        finally:
            await browser.close()

async def main():
    print("🧪 Simple Playwright Test")
    print("=" * 50)

    try:
        success = await test_single_url()
        if success:
            print("\n✅ Playwright test completed successfully!")
            print("The enhanced scraper should work better than curl-based approach.")
        else:
            print("\n❌ Playwright test failed.")
            print("There may be issues with anti-bot protection or the URL.")

        return success

    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
