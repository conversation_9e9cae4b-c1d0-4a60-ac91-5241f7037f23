#!/usr/bin/env python3
"""
Simple test script for the new city deduplication scraper
Demonstrates usage with your specific API key
"""

import sys
import os
from new_city_deduplication_scraper import NewCityDeduplicationScraper


def test_small_city():
    """Test with a smaller city for quick results"""
    print("🧪 Testing New City Deduplication Scraper")
    print("=" * 50)

    # Create scraper with your API key
    scraper = NewCityDeduplicationScraper(
        scraperapi_key="********************************"
    )

    # Test with a smaller city first (adjust these as needed)
    state_name = "Alabama"
    city_name = "Auburn"  # Smaller city for faster testing

    print(f"Testing: {city_name}, {state_name}")
    print(f"API Key: {scraper.scraperapi_key[:20]}...")
    print()

    # Set conservative parameters for testing
    scraper.phone_extraction_batch_size = 10  # Smaller batches
    scraper.phone_extraction_workers = 2      # Fewer workers
    scraper.min_pages_to_scrape = 10          # Fewer pages for testing
    scraper.max_pages_per_city = 20           # Limit pages

    try:
        success = scraper.scrape_city(
            state_name=state_name,
            city_name=city_name,
            include_no_phone=True,
            max_workers=2  # Conservative worker count
        )

        if success:
            print("\n✅ TEST SUCCESSFUL!")
            print(f"Results saved to: {scraper.results_file}")
            print(f"Phone database: {scraper.phone_db_file}")
            print(f"URLs data: {scraper.urls_file}")
            return True
        else:
            print("\n❌ TEST FAILED!")
            return False

    except Exception as e:
        print(f"\n❌ TEST ERROR: {e}")
        return False


def quick_demo():
    """Quick demo of the scraper capabilities"""
    print("📋 NEW CITY SCRAPER DEMO")
    print("=" * 30)
    print("This scraper will:")
    print("1. 📥 Extract URLs from search pages")
    print("2. 📱 Extract phone numbers and deduplicate")
    print("3. 🚀 Scrape unique profiles only")
    print("4. 💾 Save results to Excel file")
    print()
    print("Key features:")
    print("• No existing files needed - works from scratch")
    print("• Phone number deduplication (30-70% reduction)")
    print("• Uses your API key: ********************************")
    print("• Age filtering (≤30 years old only)")
    print("• Comprehensive logging and progress tracking")
    print()


def show_usage_examples():
    """Show usage examples"""
    print("🔧 USAGE EXAMPLES")
    print("=" * 30)
    print("Basic usage:")
    print('python new_city_deduplication_scraper.py "Alabama" "Birmingham"')
    print()
    print("With custom settings:")
    print('python new_city_deduplication_scraper.py "Alabama" "Birmingham" \\')
    print('    --max-workers 5 \\')
    print('    --phone-batch-size 20 \\')
    print('    --phone-workers 5')
    print()
    print("Clean and start fresh:")
    print('python new_city_deduplication_scraper.py "Alabama" "Birmingham" --clean')
    print()
    print("Exclude URLs with no phone numbers:")
    print('python new_city_deduplication_scraper.py "Alabama" "Birmingham" --exclude-no-phone')
    print()


def main():
    """Main function"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "--demo":
            quick_demo()
        elif sys.argv[1] == "--examples":
            show_usage_examples()
        elif sys.argv[1] == "--test":
            return 0 if test_small_city() else 1
        else:
            print("Unknown option. Use --demo, --examples, or --test")
            return 1
    else:
        print("🚀 New City Deduplication Scraper Test")
        print()
        print("Options:")
        print("  --demo      Show what the scraper does")
        print("  --examples  Show usage examples")
        print("  --test      Run a quick test")
        print()
        print("Example usage:")
        print('python test_new_city.py --test')

    return 0


if __name__ == "__main__":
    sys.exit(main())
