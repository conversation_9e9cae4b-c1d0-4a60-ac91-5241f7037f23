#!/usr/bin/env python3
"""
Runner script for processing JSON files with the comprehensive data extractor

Usage Examples:
    # Process a single JSON file
    python run_extractor.py --file fresh_urls_Baltimore_Maryland.json

    # Process all JSON files matching a pattern
    python run_extractor.py --pattern "fresh_urls_*.json"

    # Process with custom settings
    python run_extractor.py --file urls.json --delay 2.0 --workers 3 --limit 100

    # Process specific files
    python run_extractor.py --files "file1.json,file2.json,file3.json"
"""

import os
import glob
import argparse
import logging
from datetime import datetime
from comprehensive_data_extractor import ProfileDataExtractor, load_urls_from_json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def find_json_files(pattern=None):
    """Find JSON files matching pattern or containing 'urls'"""
    if pattern:
        files = glob.glob(pattern)
    else:
        # Find all JSON files that likely contain URLs
        files = []
        for file_pattern in ['*urls*.json', '*fresh*.json', '*phase*.json', '*batch*.json']:
            files.extend(glob.glob(file_pattern))

        # Remove duplicates and sort
        files = sorted(list(set(files)))

    # Filter out files that are too small (likely empty)
    valid_files = []
    for file in files:
        try:
            if os.path.getsize(file) > 100:  # At least 100 bytes
                valid_files.append(file)
        except:
            continue

    return valid_files

def get_output_filename(input_file, suffix="extraction_results"):
    """Generate output filename based on input file"""
    base = os.path.splitext(input_file)[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{base}_{suffix}_{timestamp}.xlsx"

def process_single_file(json_file, args):
    """Process a single JSON file"""
    logger.info(f"Processing: {json_file}")

    try:
        # Load URLs
        urls_data = load_urls_from_json(json_file)

        if not urls_data:
            logger.warning(f"No URLs found in {json_file}")
            return None

        if args.limit:
            urls_data = urls_data[:args.limit]
            logger.info(f"Limited to first {args.limit} URLs")

        # Create output filename
        output_file = args.output or get_output_filename(json_file)

        # Initialize extractor
        extractor = ProfileDataExtractor(
            delay=args.delay,
            max_workers=args.workers
        )

        # Process URLs
        results = extractor.process_urls(urls_data)

        # Save results
        extractor.save_to_excel(results, output_file)

        # Print summary
        successful = sum(1 for r in results if r['status'] == 'success')
        failed = len(results) - successful

        logger.info(f"Results for {json_file}:")
        logger.info(f"  Successful: {successful}")
        logger.info(f"  Failed: {failed}")
        logger.info(f"  Total: {len(results)}")
        logger.info(f"  Output: {output_file}")

        return {
            'file': json_file,
            'output': output_file,
            'total': len(results),
            'successful': successful,
            'failed': failed
        }

    except Exception as e:
        logger.error(f"Error processing {json_file}: {e}")
        return None

def process_multiple_files(json_files, args):
    """Process multiple JSON files"""
    results_summary = []

    for json_file in json_files:
        result = process_single_file(json_file, args)
        if result:
            results_summary.append(result)

        # Add delay between files if processing multiple
        if len(json_files) > 1:
            import time
            time.sleep(1)

    return results_summary

def list_available_files():
    """List available JSON files"""
    files = find_json_files()

    if not files:
        print("No JSON files found with URL patterns")
        return

    print("Available JSON files:")
    for i, file in enumerate(files, 1):
        try:
            size = os.path.getsize(file)
            size_mb = size / (1024 * 1024)
            print(f"  {i:2d}. {file:<50} ({size_mb:.2f} MB)")
        except:
            print(f"  {i:2d}. {file}")

def main():
    parser = argparse.ArgumentParser(
        description='Run comprehensive data extractor on JSON files',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    # Input options
    input_group = parser.add_mutually_exclusive_group()
    input_group.add_argument('--file', help='Single JSON file to process')
    input_group.add_argument('--files', help='Comma-separated list of JSON files')
    input_group.add_argument('--pattern', help='Glob pattern for JSON files (e.g., "fresh_urls_*.json")')
    input_group.add_argument('--all', action='store_true', help='Process all URL JSON files found')
    input_group.add_argument('--list', action='store_true', help='List available JSON files and exit')

    # Processing options
    parser.add_argument('--output', help='Output Excel file (auto-generated if not specified)')
    parser.add_argument('--delay', type=float, default=1.0, help='Delay between requests (seconds, default: 1.0)')
    parser.add_argument('--workers', type=int, default=5, help='Number of worker threads (default: 5)')
    parser.add_argument('--limit', type=int, help='Limit number of URLs to process (for testing)')

    # Other options
    parser.add_argument('--dry-run', action='store_true', help='Show what would be processed without actually processing')

    args = parser.parse_args()

    # Handle list option
    if args.list:
        list_available_files()
        return

    # Determine which files to process
    json_files = []

    if args.file:
        if os.path.exists(args.file):
            json_files = [args.file]
        else:
            logger.error(f"File not found: {args.file}")
            return

    elif args.files:
        file_list = [f.strip() for f in args.files.split(',')]
        for f in file_list:
            if os.path.exists(f):
                json_files.append(f)
            else:
                logger.warning(f"File not found: {f}")

    elif args.pattern:
        json_files = find_json_files(args.pattern)
        if not json_files:
            logger.error(f"No files found matching pattern: {args.pattern}")
            return

    elif args.all:
        json_files = find_json_files()
        if not json_files:
            logger.error("No JSON files found")
            return

    else:
        # Interactive mode - show available files
        list_available_files()
        files = find_json_files()

        if not files:
            return

        try:
            choice = input(f"\nEnter file number (1-{len(files)}) or 'all' for all files: ").strip()

            if choice.lower() == 'all':
                json_files = files
            else:
                idx = int(choice) - 1
                if 0 <= idx < len(files):
                    json_files = [files[idx]]
                else:
                    logger.error("Invalid selection")
                    return

        except (ValueError, KeyboardInterrupt):
            logger.info("Operation cancelled")
            return

    if not json_files:
        logger.error("No files to process")
        return

    # Show what will be processed
    print(f"\nFiles to process ({len(json_files)}):")
    for f in json_files:
        print(f"  - {f}")

    if args.dry_run:
        print("\n[DRY RUN] Would process the above files")
        return

    # Confirm if processing multiple large files
    if len(json_files) > 1:
        total_size = sum(os.path.getsize(f) for f in json_files if os.path.exists(f))
        total_mb = total_size / (1024 * 1024)

        if total_mb > 10:  # More than 10MB total
            response = input(f"\nTotal data size: {total_mb:.1f} MB. Continue? (y/N): ")
            if response.lower() not in ['y', 'yes']:
                logger.info("Operation cancelled")
                return

    # Process the files
    print(f"\nStarting extraction with {args.workers} workers, {args.delay}s delay...")
    results_summary = process_multiple_files(json_files, args)

    # Final summary
    if results_summary:
        print(f"\n=== PROCESSING COMPLETE ===")
        total_processed = sum(r['total'] for r in results_summary)
        total_successful = sum(r['successful'] for r in results_summary)
        total_failed = sum(r['failed'] for r in results_summary)

        print(f"Files processed: {len(results_summary)}")
        print(f"Total URLs processed: {total_processed}")
        print(f"Total successful: {total_successful}")
        print(f"Total failed: {total_failed}")
        print(f"Success rate: {total_successful/total_processed*100:.1f}%")

        print(f"\nOutput files:")
        for result in results_summary:
            print(f"  {result['file']} -> {result['output']}")

    else:
        print("No files were successfully processed")

if __name__ == '__main__':
    main()
