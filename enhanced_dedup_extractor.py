#!/usr/bin/env python3
"""
Enhanced Data Extractor with Progress Saving and Phone Deduplication

This script processes fresh_all_urls_20250818_234554.json with the following features:
- Saves progress after every 500 URLs
- Skips URLs with phone numbers that have already been scraped
- Resumes from last checkpoint if interrupted
- Extracts: Phone Number, Name, Location, City, Social Media, Age, Raw Text

Usage:
    python enhanced_dedup_extractor.py
"""

import json
import requests
import pandas as pd
import re
import time
import logging
import os
from datetime import datetime
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any, Set
import concurrent.futures
from threading import Lock

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_dedup_extractor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedDedupExtractor:
    """Enhanced extractor with progress saving and phone deduplication"""

    def __init__(self, delay: float = 1.0, max_workers: int = 5, checkpoint_interval: int = 500):
        self.delay = delay
        self.max_workers = max_workers
        self.checkpoint_interval = checkpoint_interval
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.results = []
        self.processed_count = 0
        self.total_count = 0
        self.lock = Lock()
        self.seen_phones: Set[str] = set()
        self.start_time = datetime.now()
        self.progress_file = None
        self.checkpoint_file = None

    def normalize_phone(self, phone: str) -> str:
        """Normalize phone number for comparison"""
        if not phone:
            return ""
        # Remove all non-digits
        digits_only = re.sub(r'\D', '', phone)
        # Return last 10 digits if longer than 10
        if len(digits_only) > 10:
            return digits_only[-10:]
        return digits_only

    def is_phone_duplicate(self, phone: str) -> bool:
        """Check if phone number has already been processed"""
        if not phone:
            return False
        normalized = self.normalize_phone(phone)
        if normalized in self.seen_phones:
            return True
        self.seen_phones.add(normalized)
        return False

    def extract_phone_number(self, soup: BeautifulSoup, html_content: str) -> Optional[str]:
        """Extract phone number from various possible locations"""
        # Method 1: Look for tel: links
        tel_link = soup.find('a', href=re.compile(r'tel:'))
        if tel_link:
            phone = tel_link.get_text(strip=True)
            if phone and re.search(r'\d{3}[-.]?\d{3}[-.]?\d{4}', phone):
                return phone

        # Method 2: Look in viewposttelephone class
        phone_div = soup.find('div', class_='viewposttelephone')
        if phone_div:
            phone_text = phone_div.get_text(strip=True)
            phone_match = re.search(r'(\d{3}[-.]?\d{3}[-.]?\d{4})', phone_text)
            if phone_match:
                return phone_match.group(1)

        # Method 3: Look for phone patterns in the entire content
        phone_patterns = [
            r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})',
            r'\((\d{3})\)[-.\s]?(\d{3})[-.\s]?(\d{4})',
            r'(\d{10})'
        ]

        for pattern in phone_patterns:
            matches = re.findall(pattern, html_content)
            if matches:
                if isinstance(matches[0], tuple):
                    return ''.join(matches[0])
                return matches[0]

        return None

    def extract_name(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract name from the profile"""
        # Method 1: Look for Nym field
        nym_span = soup.find('span', string=re.compile(r'Nym:'))
        if nym_span and nym_span.next_sibling:
            name = str(nym_span.next_sibling).strip()
            if name and name != 'None':
                return name

        # Method 2: Look in viewpostname class
        name_div = soup.find('div', class_='viewpostname')
        if name_div:
            text = name_div.get_text()
            nym_match = re.search(r'Nym:\s*(.+?)(?:\s|$)', text)
            if nym_match:
                return nym_match.group(1).strip()

        # Method 3: Look in post title
        title_div = soup.find('div', class_='viewposttitle')
        if title_div:
            title_text = title_div.get_text(strip=True)
            # Extract name from title patterns
            name_match = re.search(r'([A-Za-z]+)', title_text)
            if name_match:
                return name_match.group(1)

        return None

    def extract_location(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract location from the profile"""
        # Method 1: Look for Location field
        location_spans = soup.find_all('span', class_='postContentBold')
        for span in location_spans:
            if 'Location:' in span.get_text():
                next_text = span.next_sibling
                if next_text:
                    return str(next_text).strip()

        # Method 2: Look in location list items
        location_lis = soup.find_all('li')
        for li in location_lis:
            li_text = li.get_text()
            if 'Location:' in li_text:
                location_match = re.search(r'Location:\s*(.+)', li_text)
                if location_match:
                    return location_match.group(1).strip()

        return None

    def extract_age(self, soup: BeautifulSoup) -> Optional[int]:
        """Extract age from the profile"""
        # Method 1: Look for Age field in spans
        age_spans = soup.find_all('span', class_='postContentBold')
        for span in age_spans:
            if 'Age:' in span.get_text():
                next_text = span.next_sibling
                if next_text:
                    age_match = re.search(r'(\d+)', str(next_text))
                    if age_match:
                        return int(age_match.group(1))

        # Method 2: Look in age list items
        age_lis = soup.find_all('li')
        for li in age_lis:
            li_text = li.get_text()
            if 'Age:' in li_text:
                age_match = re.search(r'Age:\s*(\d+)', li_text)
                if age_match:
                    return int(age_match.group(1))

        # Method 3: Look in post title age span
        age_span = soup.find('span', class_='postTitleAge')
        if age_span:
            age_match = re.search(r'(\d+)', age_span.get_text())
            if age_match:
                return int(age_match.group(1))

        return None

    def extract_social_media(self, soup: BeautifulSoup, html_content: str) -> Optional[str]:
        """Extract social media information"""
        social_platforms = ['Instagram', 'Onlyfans', 'Twitter', 'Facebook', 'Snapchat', 'TikTok', 'OnlyFans']
        social_info = []

        # Look for social media patterns in the text
        for platform in social_platforms:
            # Pattern: Platform: username or Platform username
            pattern = rf'{platform}[:\s]+([A-Za-z0-9_\.]+)'
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 2:  # Valid username
                    social_info.append(f"{platform}: {match}")

        return '; '.join(social_info) if social_info else None

    def extract_raw_text(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract the main description text"""
        # Look for viewpostbody class
        body_div = soup.find('div', class_='viewpostbody')
        if body_div:
            text = body_div.get_text(separator=' ', strip=True)
            text = re.sub(r'\s+', ' ', text)
            return text

        # Fallback: look for main content areas
        content_selectors = [
            'div.post-content',
            'div.content',
            'div.description',
            'div.post-body'
        ]

        for selector in content_selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text(separator=' ', strip=True)
                text = re.sub(r'\s+', ' ', text)
                return text

        return None

    def extract_profile_data(self, url: str, city: str = None, state: str = None) -> Dict[str, Any]:
        """Extract all profile data from a single URL"""
        result = {
            'url': url,
            'phone_number': None,
            'name': None,
            'location': None,
            'city': city,
            'state': state,
            'social_media': None,
            'age': None,
            'raw_text': None,
            'status': 'failed',
            'error': None,
            'extracted_at': datetime.now().isoformat(),
            'duplicate_phone': False
        }

        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')
            html_content = response.text

            # Extract phone number first
            phone = self.extract_phone_number(soup, html_content)

            # Check for duplicate phone
            if phone and self.is_phone_duplicate(phone):
                result['phone_number'] = phone
                result['duplicate_phone'] = True
                result['status'] = 'skipped_duplicate'
                result['error'] = 'Duplicate phone number'
                logger.info(f"Skipped duplicate phone {phone} for {url}")
                return result

            # Extract all other fields
            result['phone_number'] = phone
            result['name'] = self.extract_name(soup)
            result['location'] = self.extract_location(soup)
            result['age'] = self.extract_age(soup)
            result['social_media'] = self.extract_social_media(soup, html_content)
            result['raw_text'] = self.extract_raw_text(soup)

            result['status'] = 'success'
            logger.info(f"Successfully extracted data from {url} - Phone: {phone}")

        except requests.exceptions.RequestException as e:
            result['error'] = f"Request error: {str(e)}"
            logger.error(f"Request error for {url}: {e}")
        except Exception as e:
            result['error'] = f"Parsing error: {str(e)}"
            logger.error(f"Parsing error for {url}: {e}")

        return result

    def save_checkpoint(self, results: List[Dict], checkpoint_num: int):
        """Save checkpoint with current results"""
        checkpoint_file = f"checkpoint_{checkpoint_num}_urls_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        df = pd.DataFrame(results)
        column_order = [
            'url', 'phone_number', 'name', 'location', 'city', 'state',
            'age', 'social_media', 'raw_text', 'status', 'error', 'duplicate_phone', 'extracted_at'
        ]
        available_columns = [col for col in column_order if col in df.columns]
        df = df[available_columns]

        with pd.ExcelWriter(checkpoint_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Profile_Data', index=False)

        # Save progress state
        progress_data = {
            'checkpoint_num': checkpoint_num,
            'processed_count': len(results),
            'seen_phones': list(self.seen_phones),
            'timestamp': datetime.now().isoformat(),
            'checkpoint_file': checkpoint_file
        }

        progress_file = f"progress_state_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(progress_file, 'w') as f:
            json.dump(progress_data, f, indent=2)

        logger.info(f"Checkpoint saved: {checkpoint_file} ({len(results)} URLs processed)")
        logger.info(f"Unique phones found: {len(self.seen_phones)}")
        return checkpoint_file

    def load_checkpoint(self):
        """Load the most recent checkpoint if available"""
        progress_files = [f for f in os.listdir('.') if f.startswith('progress_state_') and f.endswith('.json')]

        if not progress_files:
            logger.info("No checkpoint found, starting fresh")
            return None, []

        # Get the most recent progress file
        latest_progress = sorted(progress_files)[-1]

        try:
            with open(latest_progress, 'r') as f:
                progress_data = json.load(f)

            checkpoint_file = progress_data.get('checkpoint_file')
            if checkpoint_file and os.path.exists(checkpoint_file):
                # Load previous results
                df = pd.read_excel(checkpoint_file)
                results = df.to_dict('records')

                # Restore seen phones
                self.seen_phones = set(progress_data.get('seen_phones', []))

                logger.info(f"Loaded checkpoint: {checkpoint_file}")
                logger.info(f"Resuming from {len(results)} processed URLs")
                logger.info(f"Loaded {len(self.seen_phones)} unique phones")

                return progress_data.get('processed_count', 0), results

        except Exception as e:
            logger.error(f"Error loading checkpoint: {e}")

        return None, []

    def process_urls(self, urls_data: List[Dict]) -> List[Dict]:
        """Process all URLs with checkpointing and deduplication"""
        self.total_count = len(urls_data)

        # Try to load checkpoint
        start_index, self.results = self.load_checkpoint()

        if start_index:
            # Resume from checkpoint
            urls_data = urls_data[start_index:]
            self.processed_count = start_index
            logger.info(f"Resuming from URL {start_index + 1}")
        else:
            self.processed_count = 0
            self.results = []

        remaining_urls = len(urls_data)
        logger.info(f"Processing {remaining_urls} URLs (total: {self.total_count})")

        # Process URLs in batches for checkpointing
        for batch_start in range(0, remaining_urls, self.checkpoint_interval):
            batch_end = min(batch_start + self.checkpoint_interval, remaining_urls)
            batch_urls = urls_data[batch_start:batch_end]

            logger.info(f"Processing batch {batch_start + 1} to {batch_end} of {remaining_urls}")

            # Process batch with threading
            batch_results = self.process_batch_with_threading(batch_urls)
            self.results.extend(batch_results)

            # Save checkpoint
            total_processed = self.processed_count + len(batch_results)
            checkpoint_file = self.save_checkpoint(self.results, total_processed)

            # Update processed count
            self.processed_count = total_processed

            # Log progress
            successful = sum(1 for r in self.results if r['status'] == 'success')
            skipped = sum(1 for r in self.results if r['status'] == 'skipped_duplicate')
            failed = len(self.results) - successful - skipped

            logger.info(f"Progress: {self.processed_count}/{self.total_count} URLs")
            logger.info(f"  Successful: {successful}")
            logger.info(f"  Skipped (duplicates): {skipped}")
            logger.info(f"  Failed: {failed}")
            logger.info(f"  Unique phones: {len(self.seen_phones)}")

        return self.results

    def process_batch_with_threading(self, batch_urls: List[Dict]) -> List[Dict]:
        """Process a batch of URLs with threading"""
        batch_results = []

        # Split into smaller sub-batches for workers
        batch_size = max(1, len(batch_urls) // self.max_workers)
        sub_batches = [batch_urls[i:i + batch_size] for i in range(0, len(batch_urls), batch_size)]

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_batch = {
                executor.submit(self.process_sub_batch, sub_batch): sub_batch
                for sub_batch in sub_batches
            }

            for future in concurrent.futures.as_completed(future_to_batch):
                try:
                    sub_batch_results = future.result()
                    batch_results.extend(sub_batch_results)
                except Exception as e:
                    logger.error(f"Sub-batch processing error: {e}")

        return batch_results

    def process_sub_batch(self, urls_batch: List[Dict]) -> List[Dict]:
        """Process a sub-batch of URLs"""
        sub_batch_results = []

        for url_data in urls_batch:
            url = url_data.get('url')
            city = url_data.get('city')
            state = url_data.get('state')

            result = self.extract_profile_data(url, city, state)
            sub_batch_results.append(result)

            time.sleep(self.delay)

        return sub_batch_results

    def save_final_results(self, results: List[Dict]):
        """Save final results to Excel file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"fresh_all_urls_FINAL_extraction_{timestamp}.xlsx"

        df = pd.DataFrame(results)

        # Reorder columns
        column_order = [
            'url', 'phone_number', 'name', 'location', 'city', 'state',
            'age', 'social_media', 'raw_text', 'status', 'error', 'duplicate_phone', 'extracted_at'
        ]

        available_columns = [col for col in column_order if col in df.columns]
        df = df[available_columns]

        # Create summary sheet
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Profile_Data', index=False)

            # Create summary
            summary_data = {
                'Metric': [
                    'Total URLs Processed',
                    'Successful Extractions',
                    'Skipped (Duplicate Phone)',
                    'Failed Extractions',
                    'Unique Phone Numbers',
                    'Success Rate (%)',
                    'Processing Start Time',
                    'Processing End Time',
                    'Total Processing Time'
                ],
                'Value': [
                    len(results),
                    sum(1 for r in results if r['status'] == 'success'),
                    sum(1 for r in results if r['status'] == 'skipped_duplicate'),
                    sum(1 for r in results if r['status'] == 'failed'),
                    len(self.seen_phones),
                    f"{sum(1 for r in results if r['status'] == 'success') / len(results) * 100:.2f}",
                    self.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    str(datetime.now() - self.start_time)
                ]
            }

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

        logger.info(f"Final results saved to: {output_file}")
        return output_file

def load_fresh_urls():
    """Load URLs from the specific JSON file"""
    json_file = "fresh_all_urls_20250818_234554.json"

    if not os.path.exists(json_file):
        logger.error(f"File not found: {json_file}")
        return []

    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        urls_list = []
        if isinstance(data, list):
            urls_list = data
        elif isinstance(data, dict):
            # Handle nested structure
            for key, value in data.items():
                if isinstance(value, dict) and 'urls' in value:
                    urls = value['urls']
                    city = value.get('city')
                    state = value.get('state')

                    for url in urls:
                        if isinstance(url, str):
                            urls_list.append({
                                'url': url,
                                'city': city,
                                'state': state
                            })
                        elif isinstance(url, dict):
                            url_data = url.copy()
                            if not url_data.get('city'):
                                url_data['city'] = city
                            if not url_data.get('state'):
                                url_data['state'] = state
                            urls_list.append(url_data)

        logger.info(f"Loaded {len(urls_list)} URLs from {json_file}")
        return urls_list

    except Exception as e:
        logger.error(f"Error loading {json_file}: {e}")
        return []

def main():
    """Main function to run the enhanced extractor"""
    logger.info("=== Enhanced Dedup Extractor Started ===")
    logger.info("Features:")
    logger.info("- Saves progress every 500 URLs")
    logger.info("- Skips duplicate phone numbers")
    logger.info("- Resumes from checkpoints")
    logger.info("")

    # Load URLs
    urls_data = load_fresh_urls()
    if not urls_data:
        logger.error("No URLs to process")
        return

    # Initialize extractor
    extractor = EnhancedDedupExtractor(
        delay=1.0,  # 1 second delay between requests
        max_workers=5,  # 5 concurrent workers
        checkpoint_interval=500  # Save every 500 URLs
    )

    # Process URLs
    logger.info("Starting URL processing...")
    results = extractor.process_urls(urls_data)

    # Save final results
    output_file = extractor.save_final_results(results)

    # Final summary
    successful = sum(1 for r in results if r['status'] == 'success')
    skipped = sum(1 for r in results if r['status'] == 'skipped_duplicate')
    failed = len(results) - successful - skipped

    logger.info("=== PROCESSING COMPLETE ===")
    logger.info(f"Total URLs processed: {len(results)}")
    logger.info(f"Successful extractions: {successful}")
    logger.info(f"Skipped (duplicate phones): {skipped}")
    logger.info(f"Failed extractions: {failed}")
    logger.info(f"Unique phone numbers found: {len(extractor.seen_phones)}")
    logger.info(f"Success rate: {successful/len(results)*100:.2f}%")
    logger.info(f"Output file: {output_file}")
    logger.info(f"Total processing time: {datetime.now() - extractor.start_time}")

if __name__ == '__main__':
    main()
