#!/usr/bin/env python3
"""
Web Scraping System for Escort Alligator
Scrapes search pages and dedicated pages to extract comprehensive data
"""

import re
import os
import sys
import json
import time
import logging
import subprocess
import pandas as pd
from typing import Dict, List, Tuple, Optional
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
from datetime import datetime
import requests
from pathlib import Path
try:
    from mistralai import Mistral
    MISTRAL_AVAILABLE = True
except ImportError:
    MISTRAL_AVAILABLE = False
    Mistral = None

class WebScraper:
    def __init__(self, base_dir: str = ".", mistral_api_key: str = None):
        """Initialize the web scraper with configuration"""
        self.base_dir = Path(base_dir)
        self.setup_logging()

        # Load reference cURL commands
        self.search_curl_template = self.load_curl_command("search page_1.html")
        self.dedicated_curl_template = self.load_curl_command("dedicated page.html")

        # Initialize Mistral client
        self.mistral_client = None
        if mistral_api_key and MISTRAL_AVAILABLE:
            try:
                self.mistral_client = Mistral(api_key=mistral_api_key)
                self.logger.info("Mistral client initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Mistral client: {e}")
        elif mistral_api_key and not MISTRAL_AVAILABLE:
            self.logger.warning("Mistral AI requested but not available. Install with: pip install mistralai")

        # Data storage
        self.scraped_data = []
        self.processed_cities = set()

        # Rate limiting
        self.request_delay = 2  # seconds between requests

        # Main URL sources (no escortalligator)
        self.url_sources = ['aaok', 'aypapi']

        # Multi-page scraping settings
        self.max_pages_per_city = 100  # Safety limit to prevent infinite loops
        self.min_pages_to_scrape = 25  # Minimum pages to attempt before stopping
        self.max_consecutive_empty_pages = 5  # Stop after 5 consecutive empty pages (increased from 3)

        # Resume functionality
        self.progress_file = None
        self.resume_enabled = True
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('scraper.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_curl_command(self, filename: str) -> str:
        """Load cURL command from reference file"""
        try:
            file_path = self.base_dir / filename
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract cURL command (spans multiple lines with backslashes)
            lines = content.split('\n')
            curl_lines = []
            in_curl_command = False

            for line in lines:
                line = line.strip()

                # Start of curl command
                if line.startswith('curl '):
                    in_curl_command = True
                    curl_lines.append(line.rstrip('\\').strip())
                # Continuation of curl command
                elif in_curl_command and (line.startswith('-H ') or line.startswith('-b ') or line.startswith('-')):
                    curl_lines.append(line.rstrip('\\').strip())
                # End of curl command (empty line or non-curl line)
                elif in_curl_command and (not line or not line.startswith('-')):
                    break

            # Join all parts with spaces
            full_command = ' '.join(curl_lines)
            return full_command

        except Exception as e:
            self.logger.error(f"Error loading cURL command from {filename}: {e}")
            return ""
    
    def parse_url_list(self) -> List[Dict[str, str]]:
        """Parse the URL list file to extract cities and their URLs"""
        try:
            url_list_path = self.base_dir / "url_list.md"
            with open(url_list_path, 'r', encoding='utf-8') as f:
                content = f.read()

            cities = []
            current_state = ""

            for line in content.split('\n'):
                line = line.strip()
                if not line:
                    continue

                # Check if this is a state header (no URL)
                if '-https://' not in line and line and not line.startswith('http'):
                    current_state = line
                    continue

                # Parse city-URL pairs
                if '-https://' in line:
                    parts = line.split('-https://', 1)
                    if len(parts) == 2:
                        city_name = parts[0].strip()
                        original_url = 'https://' + parts[1].strip()

                        # Extract the path structure from original URL
                        # e.g., from https://escortalligator.com.listcrawler.eu/brief/escorts/usa/alabama/auburn/1
                        # extract /brief/escorts/usa/alabama/auburn/1
                        url_parts = original_url.split('.com.listcrawler.eu')
                        if len(url_parts) == 2:
                            path_part = url_parts[1]

                            # Generate URLs for each main source
                            for source in self.url_sources:
                                new_url = f"https://{source}.com.listcrawler.eu{path_part}"

                                cities.append({
                                    'state': current_state,
                                    'city': city_name,
                                    'url': new_url,
                                    'source': source
                                })

            self.logger.info(f"Parsed {len(cities)} city-source combinations from URL list")
            return cities

        except Exception as e:
            self.logger.error(f"Error parsing URL list: {e}")
            return []

    def save_progress(self, progress_data: Dict, filename: str = None):
        """Save current progress to JSON file"""
        try:
            if not filename:
                filename = self.progress_file or "scraper_progress.json"

            progress_data.update({
                'timestamp': datetime.now().isoformat(),
                'processed_cities': list(self.processed_cities),
                'total_records': len(self.scraped_data)
            })

            with open(filename, 'w') as f:
                json.dump(progress_data, f, indent=2)

            self.logger.info(f"Progress saved to {filename}")

        except Exception as e:
            self.logger.error(f"Error saving progress: {e}")

    def load_progress(self, filename: str = None) -> Dict:
        """Load progress from JSON file"""
        try:
            if not filename:
                filename = self.progress_file or "scraper_progress.json"

            if not os.path.exists(filename):
                self.logger.info(f"No progress file found: {filename}")
                return {}

            with open(filename, 'r') as f:
                progress_data = json.load(f)

            # Restore processed cities
            if 'processed_cities' in progress_data:
                self.processed_cities = set(progress_data['processed_cities'])
                self.logger.info(f"Loaded {len(self.processed_cities)} processed cities from {filename}")

            return progress_data

        except Exception as e:
            self.logger.error(f"Error loading progress: {e}")
            return {}

    def save_data_checkpoint(self, checkpoint_name: str = None):
        """Save current scraped data to Excel checkpoint"""
        try:
            if not checkpoint_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                checkpoint_name = f"checkpoint_{timestamp}.xlsx"

            if self.scraped_data:
                # Create temporary scraper instance to use save_to_excel
                temp_data = self.scraped_data.copy()
                self.save_to_excel(checkpoint_name)
                self.logger.info(f"Data checkpoint saved to {checkpoint_name} ({len(self.scraped_data)} records)")
            else:
                self.logger.info("No data to save in checkpoint")

        except Exception as e:
            self.logger.error(f"Error saving data checkpoint: {e}")
    
    def execute_curl_request(self, url: str, curl_template: str) -> Optional[str]:
        """Execute cURL request and return HTML content"""
        try:
            # Extract the original URL from the curl template
            import re
            url_match = re.search(r"curl '([^']+)'", curl_template)
            if not url_match:
                self.logger.error(f"Could not find URL in curl template")
                return None

            original_url = url_match.group(1)

            # Replace the original URL with the new URL
            curl_command = curl_template.replace(original_url, url)

            # Execute curl command
            result = subprocess.run(
                curl_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                return result.stdout
            else:
                self.logger.error(f"cURL failed for {url}: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            self.logger.error(f"cURL timeout for {url}")
            return None
        except Exception as e:
            self.logger.error(f"Error executing cURL for {url}: {e}")
            return None
    
    def extract_dedicated_urls(self, html_content: str) -> List[str]:
        """Extract dedicated page URLs from search page HTML, filtering by age ≤30"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Find all links matching the dedicated page pattern for aaok and aypapi
            dedicated_urls = []
            pattern = re.compile(r'https://(aaok|aypapi)\.com\.listcrawler\.eu/post/escorts/usa/[^/]+/[^/]+/\d+')

            # Find all age elements first
            age_elements = soup.find_all('div', class_='titleAge')
            valid_ages = set()

            for age_elem in age_elements:
                try:
                    age_text = age_elem.get_text(strip=True)
                    age = int(age_text)
                    if age <= 30:
                        valid_ages.add(age_text)
                except (ValueError, AttributeError):
                    continue

            self.logger.info(f"Found {len(valid_ages)} profiles with age ≤30: {sorted(valid_ages)}")

            # Now find links associated with valid ages
            for link in soup.find_all('a', href=True):
                href = link['href']
                if pattern.match(href):
                    # Check if this link is associated with a valid age
                    # Look for age in the same container or nearby elements
                    container = link.find_parent(['div', 'article', 'section'])
                    if container:
                        age_elem = container.find('div', class_='titleAge')
                        if age_elem:
                            try:
                                age_text = age_elem.get_text(strip=True)
                                age = int(age_text)
                                if age <= 30:
                                    dedicated_urls.append(href)
                            except (ValueError, AttributeError):
                                continue

            # Remove duplicates while preserving order
            seen = set()
            unique_urls = []
            for url in dedicated_urls:
                if url not in seen:
                    seen.add(url)
                    unique_urls.append(url)

            self.logger.info(f"Found {len(unique_urls)} dedicated page URLs for age ≤30")
            return unique_urls

        except Exception as e:
            self.logger.error(f"Error extracting dedicated URLs: {e}")
            return []
    
    def check_gender_filter(self, soup: BeautifulSoup) -> bool:
        """Check if the profile is for a woman"""
        try:
            # Look for the "I am" section
            iam_elem = soup.find('div', class_='i-am')
            if iam_elem:
                value_elem = iam_elem.find('span', class_='iamisee__value')
                if value_elem:
                    gender_text = value_elem.get_text(strip=True).lower()
                    return 'woman' in gender_text
            return False
        except Exception as e:
            self.logger.debug(f"Error checking gender filter: {e}")
            return False

    def extract_with_mistral(self, html_content: str, page_type: str = "dedicated") -> Dict[str, str]:
        """Use Mistral AI to extract structured data from HTML"""
        if not self.mistral_client:
            return {}

        try:
            # Clean HTML and extract text content
            soup = BeautifulSoup(html_content, 'html.parser')
            text_content = soup.get_text(separator=' ', strip=True)

            # Limit text length for API
            if len(text_content) > 8000:
                text_content = text_content[:8000] + "..."

            if page_type == "dedicated":
                prompt = f"""
                Extract the following information from this escort profile page content:

                1. Title/Headline
                2. Name (if mentioned)
                3. Age (number only)
                4. Phone number
                5. Description/Services offered
                6. Location mentioned
                7. Posted date/time
                8. Social media links/handles (Instagram, Twitter, Snapchat, OnlyFans, etc.)
                9. Email address
                10. Website/personal links

                Content:
                {text_content}

                Return ONLY a JSON object with these exact keys: title, name, age, phone, description, location, posted_date, social_media, email, website
                If any field is not found, use null.
                """
            else:  # search page
                prompt = f"""
                Extract any social media information, contact details, or additional profile information from this search page content:

                Look for:
                1. Social media handles/links (Instagram, Twitter, Snapchat, OnlyFans, etc.)
                2. Email addresses
                3. Website links
                4. Additional contact information
                5. Profile descriptions or bio information

                Content:
                {text_content}

                Return ONLY a JSON object with these exact keys: social_media, email, website, additional_info
                If any field is not found, use null.
                """

            response = self.mistral_client.chat.complete(
                model="mistral-large-latest",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=500
            )

            result_text = response.choices[0].message.content.strip()

            # Try to parse JSON response
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                # If not valid JSON, try to extract JSON from the response
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                return {}

        except Exception as e:
            self.logger.debug(f"Mistral extraction failed: {e}")
            return {}

    def extract_dedicated_page_data(self, html_content: str, url: str) -> Optional[Dict]:
        """Extract data from dedicated page HTML with gender filtering and Mistral AI"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # First check gender filter - only process women
            if not self.check_gender_filter(soup):
                self.logger.debug(f"Skipping non-woman profile: {url}")
                return None

            data = {
                'url': url,
                'scraped_at': datetime.now().isoformat()
            }

            # Try Mistral AI extraction first
            mistral_data = self.extract_with_mistral(html_content, "dedicated")
            if mistral_data:
                data.update(mistral_data)
                self.logger.debug("Used Mistral AI for data extraction")

            # Fallback to traditional extraction for missing fields
            if not data.get('title'):
                title_elem = soup.find('div', class_='viewposttitle')
                if title_elem:
                    data['title'] = title_elem.get_text(strip=True)

            if not data.get('age'):
                age_elem = soup.find('span', class_='postTitleAge')
                if age_elem:
                    data['age'] = age_elem.get_text(strip=True)

            if not data.get('name'):
                name_elem = soup.find('div', class_='viewpostname')
                if name_elem:
                    name_text = name_elem.get_text(strip=True)
                    if 'Nym:' in name_text:
                        data['name'] = name_text.split('Nym:')[-1].strip()
                    else:
                        data['name'] = name_text

            if not data.get('phone'):
                phone_elem = soup.find('div', class_='viewposttelephone')
                if phone_elem:
                    phone_link = phone_elem.find('a')
                    if phone_link and 'href' in phone_link.attrs:
                        data['phone'] = phone_link['href'].replace('tel:', '')

                # Also try hidden inputs
                if not data.get('phone'):
                    phone_inputs = soup.find_all('input', {'name': ['relatedPhoneNumber', 'phone']})
                    for inp in phone_inputs:
                        if inp.get('value'):
                            data['phone'] = inp['value']
                            break

            if not data.get('description'):
                body_elem = soup.find('div', class_='viewpostbody')
                if body_elem:
                    data['description'] = body_elem.get_text(strip=True)

            if not data.get('posted_date'):
                date_elem = soup.find('div', class_='viewpostdateEscort')
                if date_elem:
                    data['posted_date'] = date_elem.get_text(strip=True)

            # Extract post ID from URL
            post_id_match = re.search(r'/(\d+)$', url)
            if post_id_match:
                data['post_id'] = post_id_match.group(1)

            return data

        except Exception as e:
            self.logger.error(f"Error extracting data from dedicated page {url}: {e}")
            return None

    def scrape_city_pages(self, city_info: Dict[str, str]) -> List[Dict]:
        """Scrape multiple pages for a single city from a specific source"""
        city_name = city_info['city']
        state_name = city_info['state']
        base_url = city_info['url']
        source = city_info['source']

        self.logger.info(f"Starting multi-page scrape for {city_name}, {state_name} from {source}")

        all_dedicated_urls = []

        # Step 1: Scrape all available pages for this city-source combination
        page_num = 1
        max_empty_pages = 3  # Stop after 3 consecutive empty pages
        empty_page_count = 0

        while True:
            # Generate URL for this page (replace the page number at the end)
            page_url = re.sub(r'/\d+$', f'/{page_num}', base_url)

            self.logger.info(f"Scraping page {page_num} for {city_name} ({source}): {page_url}")

            # Get search page HTML
            search_html = self.execute_curl_request(page_url, self.search_curl_template)
            if not search_html:
                self.logger.warning(f"Failed to get page {page_num} for {city_name} from {source}")
                continue

            # Extract search page info using Mistral AI
            search_page_info = self.extract_with_mistral(search_html, "search")
            if search_page_info and page_num == 1:  # Only log for first page to avoid spam
                self.logger.info(f"Search page info extracted for {city_name} ({source}): {search_page_info}")

            # Extract dedicated page URLs (filtered by age ≤30)
            page_dedicated_urls = self.extract_dedicated_urls(search_html)
            if not page_dedicated_urls:
                empty_page_count += 1
                self.logger.info(f"No URLs found on page {page_num} for {city_name} from {source} (empty page {empty_page_count}/{self.max_consecutive_empty_pages})")

                # Only stop if we've scraped minimum pages AND hit consecutive empty page limit
                # Note: page_num is the current page being processed, so we check page_num > min_pages_to_scrape
                if page_num > self.min_pages_to_scrape and empty_page_count >= self.max_consecutive_empty_pages:
                    self.logger.info(f"Stopping after {self.max_consecutive_empty_pages} consecutive empty pages (scraped {page_num} pages, minimum {self.min_pages_to_scrape} reached)")
                    break
                elif page_num <= self.min_pages_to_scrape:
                    self.logger.info(f"Continuing despite empty page - need to reach minimum {self.min_pages_to_scrape} pages (currently at {page_num})")
            else:
                empty_page_count = 0  # Reset counter when we find content
                all_dedicated_urls.extend(page_dedicated_urls)
                self.logger.info(f"Found {len(page_dedicated_urls)} URLs on page {page_num}")

            # Rate limiting between pages
            time.sleep(self.request_delay)
            page_num += 1

            # Safety check to prevent infinite loops
            if page_num > self.max_pages_per_city:
                self.logger.info(f"Reached maximum page limit ({self.max_pages_per_city}) for {city_name} from {source}")
                break

        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in all_dedicated_urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)

        self.logger.info(f"Total unique URLs found for {city_name} ({source}): {len(unique_urls)}")

        if not unique_urls:
            return []

        # Step 2: Scrape each dedicated page (filtered by gender = woman)
        city_data = []
        for i, dedicated_url in enumerate(unique_urls):
            self.logger.info(f"Scraping dedicated page {i+1}/{len(unique_urls)} for {city_name} ({source})")

            # Rate limiting
            if i > 0:
                time.sleep(self.request_delay)

            # Get dedicated page HTML
            dedicated_html = self.execute_curl_request(dedicated_url, self.dedicated_curl_template)
            if not dedicated_html:
                self.logger.error(f"Failed to get dedicated page: {dedicated_url}")
                continue

            # Extract data (includes gender filtering)
            page_data = self.extract_dedicated_page_data(dedicated_html, dedicated_url)
            if page_data:
                # Add city, state, and source information
                page_data['city'] = city_name
                page_data['state'] = state_name
                page_data['source'] = source
                page_data['search_url'] = base_url
                city_data.append(page_data)

        self.logger.info(f"Completed scraping {city_name} from {source}: {len(city_data)} women ≤30 years")
        return city_data

    def save_to_excel(self, output_file: str = "scraped_data.xlsx"):
        """Save scraped data to Excel file organized by city"""
        try:
            if not self.scraped_data:
                self.logger.warning("No data to save")
                return

            # Create DataFrame
            df = pd.DataFrame(self.scraped_data)

            # Reorder columns for better readability
            column_order = [
                'state', 'city', 'source', 'title', 'name', 'age', 'phone',
                'description', 'social_media', 'email', 'website', 'posted_date',
                'post_id', 'url', 'search_url', 'scraped_at'
            ]

            # Only include columns that exist in the data
            existing_columns = [col for col in column_order if col in df.columns]
            df = df[existing_columns]

            # Sort by state, then city, then source, then scraped_at
            df = df.sort_values(['state', 'city', 'source', 'scraped_at'])

            # Save to Excel with multiple sheets if needed
            output_path = self.base_dir / output_file

            # If data is large, create separate sheets by state
            if len(df) > 1000000:  # Excel row limit consideration
                with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                    for state in df['state'].unique():
                        state_df = df[df['state'] == state]
                        sheet_name = state[:31]  # Excel sheet name limit
                        state_df.to_excel(writer, sheet_name=sheet_name, index=False)
            else:
                df.to_excel(output_path, index=False)

            self.logger.info(f"Data saved to {output_path}")
            self.logger.info(f"Total records: {len(df)}")
            self.logger.info(f"Cities processed: {df['city'].nunique()}")
            self.logger.info(f"States processed: {df['state'].nunique()}")

        except Exception as e:
            self.logger.error(f"Error saving to Excel: {e}")

    def run_full_scrape(self, max_cities: Optional[int] = None, resume: bool = True):
        """Run the complete scraping workflow with resume capability"""
        self.logger.info("Starting full scrape workflow")

        # Set up progress file
        self.progress_file = "full_scrape_progress.json"

        # Load previous progress if resuming
        if resume:
            progress_data = self.load_progress()
            if progress_data:
                self.logger.info(f"Resuming from previous session (last saved: {progress_data.get('timestamp', 'unknown')})")

        # Parse URL list
        cities = self.parse_url_list()
        if not cities:
            self.logger.error("No cities found in URL list")
            return

        # Limit cities if specified (for testing)
        if max_cities:
            cities = cities[:max_cities]
            self.logger.info(f"Limited to first {max_cities} cities for testing")

        # Process each city
        total_cities = len(cities)
        processed_count = 0

        for i, city_info in enumerate(cities):
            city_name = city_info['city']
            state_name = city_info['state']
            source = city_info['source']

            self.logger.info(f"Processing combination {i+1}/{total_cities}: {city_name}, {state_name} from {source}")

            # Skip if already processed (for resume functionality)
            city_key = f"{state_name}_{city_name}_{source}"
            if city_key in self.processed_cities:
                self.logger.info(f"Skipping already processed: {city_name} ({source})")
                continue

            try:
                # Scrape city data (multi-page)
                city_data = self.scrape_city_pages(city_info)

                # Add to main data collection
                self.scraped_data.extend(city_data)

                # Mark as processed
                self.processed_cities.add(city_key)
                processed_count += 1

                # Save progress after every city-source combination
                progress_data = {
                    'current_city': city_name,
                    'current_state': state_name,
                    'current_source': source,
                    'progress_index': i + 1,
                    'total_cities': total_cities,
                    'processed_count': processed_count
                }
                self.save_progress(progress_data)

                # Save data checkpoint after every city
                if processed_count % 1 == 0:  # Save after every city-source combination
                    checkpoint_name = f"checkpoint_{state_name}_{city_name}_{source}.xlsx"
                    self.save_data_checkpoint(checkpoint_name)

                # Rate limiting between city-source combinations
                time.sleep(self.request_delay)

            except Exception as e:
                self.logger.error(f"Error processing {city_name} from {source}: {e}")
                # Save progress even on error
                progress_data = {
                    'current_city': city_name,
                    'current_state': state_name,
                    'current_source': source,
                    'progress_index': i + 1,
                    'total_cities': total_cities,
                    'processed_count': processed_count,
                    'last_error': str(e)
                }
                self.save_progress(progress_data)
                continue

        # Final save
        self.save_to_excel("final_scraped_data.xlsx")
        self.logger.info("Full scrape workflow completed")

        # Clean up progress file on successful completion
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                self.logger.info("Progress file cleaned up after successful completion")
        except Exception as e:
            self.logger.warning(f"Could not clean up progress file: {e}")


def main():
    """Main entry point"""
    import argparse

    parser = argparse.ArgumentParser(description='Enhanced Web Scraper with AI, Filtering, and Resume')
    parser.add_argument('--max-cities', type=int, help='Maximum number of city-source combinations to process (for testing)')
    parser.add_argument('--output', default='scraped_data.xlsx', help='Output Excel file name')
    parser.add_argument('--delay', type=float, default=2.0, help='Delay between requests in seconds')
    parser.add_argument('--mistral-key', help='Mistral AI API key for enhanced text extraction')
    parser.add_argument('--no-resume', action='store_true', help='Start fresh without resuming from previous session')
    parser.add_argument('--clean-progress', action='store_true', help='Clean up progress files and start fresh')

    args = parser.parse_args()

    # Get Mistral API key from environment if not provided, with your key as default
    mistral_key = args.mistral_key or os.getenv('MISTRAL_API_KEY') or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"

    # Create scraper instance
    scraper = WebScraper(mistral_api_key=mistral_key)
    scraper.request_delay = args.delay

    # Clean progress files if requested
    if args.clean_progress:
        progress_files = ["full_scrape_progress.json", "scraper_progress.json"]
        for pf in progress_files:
            if os.path.exists(pf):
                os.remove(pf)
                print(f"Cleaned up progress file: {pf}")
        print("Progress files cleaned. Starting fresh.")

    # Run scraping with resume capability
    resume = not args.no_resume
    scraper.run_full_scrape(max_cities=args.max_cities, resume=resume)

    # Save final results
    if args.output != 'scraped_data.xlsx':
        scraper.save_to_excel(args.output)


if __name__ == "__main__":
    main()
