#!/usr/bin/env python3
"""
Targeted City Scraper - Using exact URLs provided by user
Scrapes the specific cities with correct URL patterns:
- South Jersey, New Jersey
- Philadelphia, Pennsylvania
- Pittsburgh, Pennsylvania
- Wilmington, Delaware
- Dover, Delaware
- Baltimore, Maryland
- Annapolis, Maryland
"""

import requests
import re
import time
import json
from typing import List, Dict, Set, Optional
from bs4 import BeautifulSoup
from datetime import datetime
from urllib.parse import urljoin, urlparse, quote
import pandas as pd
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
import random

# Fixed Mistral AI import
try:
    from mistralai.client import MistralClient
    from mistralai.models.chat_completion import ChatMessage
    MISTRAL_AVAILABLE = True
except ImportError:
    MISTRAL_AVAILABLE = False
    MistralClient = None
    ChatMessage = None

class TargetedCityScraper:
    def __init__(self, scrapingdog_api_keys: List[str] = None,
                 mistral_api_key: str = "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso",
                 max_urls_to_process: int = 200):
        """Initialize targeted city scraper with ScrapingDog API keys"""

        # Default ScrapingDog API keys if none provided
        if scrapingdog_api_keys is None:
            scrapingdog_api_keys = [
                "68a390dbc2920968e9acce34",
                # Add more API keys here if you have them
            ]

        self.scrapingdog_api_keys = scrapingdog_api_keys
        self.current_key_index = 0
        self.current_key_requests = 0
        self.max_requests_per_key = 900
        self.max_urls_to_process = max_urls_to_process
        self.mistral_api_key = mistral_api_key
        self.scrapingdog_base_url = "https://api.scrapingdog.com/scrape"

        # Initialize Mistral client
        self.mistral_client = None
        if mistral_api_key and MISTRAL_AVAILABLE:
            try:
                self.mistral_client = MistralClient(api_key=mistral_api_key)
                print("✅ Mistral AI client initialized")
            except Exception as e:
                print(f"⚠️ Mistral client failed: {e}")

        # Exact target city URLs provided by user
        self.target_cities = [
            {
                "name": "South Jersey",
                "state": "New Jersey",
                "base_url": "https://aaok.com.listcrawler.eu/brief/escorts/usa/newjersey/southjersey",
                "source": "aaok"
            },
            {
                "name": "Philadelphia",
                "state": "Pennsylvania",
                "base_url": "https://aaok.com.listcrawler.eu/brief/escorts/usa/pennsylvania/philadelphia",
                "source": "aaok"
            },
            {
                "name": "Pittsburgh",
                "state": "Pennsylvania",
                "base_url": "https://aaok.com.listcrawler.eu/brief/escorts/usa/pennsylvania/pittsburgh",
                "source": "aaok"
            },
            {
                "name": "Wilmington",
                "state": "Delaware",
                "base_url": "https://aaok.com.listcrawler.eu/brief/escorts/usa/delaware/wilmingtonde",
                "source": "aaok"
            },
            {
                "name": "Dover",
                "state": "Delaware",
                "base_url": "https://aaok.com.listcrawler.eu/brief/escorts/usa/delaware/doverde",
                "source": "aaok"
            },
            {
                "name": "Baltimore",
                "state": "Maryland",
                "base_url": "https://aaok.com.listcrawler.eu/brief/escorts/usa/maryland/baltimore",
                "source": "aaok"
            },
            {
                "name": "Annapolis",
                "state": "Maryland",
                "base_url": "https://aaok.com.listcrawler.eu/brief/escorts/usa/maryland/annapolis",
                "source": "aaok"
            }
        ]

        # Phone deduplication
        self.known_phones: Set[str] = set()
        self.phone_to_data: Dict[str, Dict] = {}
        self.phone_lock = threading.Lock()

        # Statistics
        self.stats = {
            'search_pages_scraped': 0,
            'total_urls_found': 0,
            'profiles_scraped': 0,
            'duplicates_skipped': 0,
            'failed_extractions': 0,
            'api_requests': 0
        }

        # Results storage
        self.all_profile_urls = []
        self.scraped_profiles = []
        self.results_lock = threading.Lock()

        print(f"🎯 Targeted City Scraper initialized with ScrapingDog")
        print(f"🔑 Available API keys: {len(self.scrapingdog_api_keys)}")
        print(f"📊 Max requests per key: {self.max_requests_per_key}")
        print(f"🎯 Max URLs to process: {self.max_urls_to_process}")

        # Initialize user agents for browser simulation
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]

    def detect_abuse_page(self, html: str) -> bool:
        """Detect if we got an abuse/bot check page instead of real content"""
        if not html:
            return False

        abuse_indicators = [
            "report abuse",
            "abuse check",
            "bot detection",
            "cloudflare",
            "security check",
            "captcha",
            "please wait",
            "checking your browser",
            "ray id",
            "access denied",
            "blocked",
            "suspicious activity",
            "× report abuse",
            "content/photos/phone scam/fake",
            "must authenticate your email"
        ]

        html_lower = html.lower()
        for indicator in abuse_indicators:
            if indicator in html_lower:
                return True

        # Check if we're getting the generic listing page instead of profile details
        if ("escort south jersey | listing" in html_lower and
            "report abuse" in html_lower and
            len(html.strip()) < 2000):
            return True

        return False

    def fetch_with_scrapingdog(self, url: str, render_js: bool = False) -> Optional[str]:
        """Fetch URL using ScrapingDog with API key rotation and anti-bot measures"""
        try:
            # Check if we need to switch to next API key
            if self.current_key_requests >= self.max_requests_per_key:
                self.current_key_index += 1
                if self.current_key_index >= len(self.scrapingdog_api_keys):
                    print("❌ All ScrapingDog API keys exhausted!")
                    return None

                print(f"🔄 Switching to API key {self.current_key_index + 1}/{len(self.scrapingdog_api_keys)}")
                self.current_key_requests = 0

                # Save progress when switching API keys
                print(f"💾 Saving progress before switching API keys...")
                self.save_progress()

            # Get current API key
            current_api_key = self.scrapingdog_api_keys[self.current_key_index]

            # Random delay between requests (1-3 seconds)
            time.sleep(random.uniform(1, 3))

            # Prepare ScrapingDog request with better browser simulation
            params = {
                'api_key': current_api_key,
                'url': url,
                'dynamic': 'true' if render_js else 'false',
                'premium': 'true',  # Use premium residential proxies
                'country': 'US',    # Use US proxies
            }

            # Add random user agent
            headers = {
                'User-Agent': random.choice(self.user_agents),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            response = requests.get(self.scrapingdog_base_url, params=params, headers=headers, timeout=60)
            self.stats['api_requests'] += 1
            self.current_key_requests += 1

            if response.status_code == 200:
                html_content = response.text

                # Check if we got an abuse page
                if self.detect_abuse_page(html_content):
                    print(f"⚠️ Abuse/bot check detected for {url}, retrying with different settings...")

                    # Wait longer and try again with different params
                    time.sleep(random.uniform(5, 10))

                    # Try with different settings
                    retry_params = {
                        'api_key': current_api_key,
                        'url': url,
                        'dynamic': 'true',
                        'premium': 'true',
                        'country': 'US',
                        'render_wait': '3000',  # Wait 3 seconds for page load
                    }

                    retry_headers = {
                        'User-Agent': random.choice(self.user_agents),
                        'Referer': 'https://google.com/',
                    }

                    retry_response = requests.get(self.scrapingdog_base_url, params=retry_params, headers=retry_headers, timeout=90)
                    self.stats['api_requests'] += 1
                    self.current_key_requests += 1

                    if retry_response.status_code == 200:
                        retry_html = retry_response.text
                        if not self.detect_abuse_page(retry_html):
                            print(f"✅ Retry successful for {url}")
                            return retry_html
                        else:
                            print(f"❌ Still getting abuse page after retry for {url}")
                            return None
                    else:
                        print(f"⚠️ Retry failed with status {retry_response.status_code}")
                        return None
                else:
                    print(f"✅ Request {self.current_key_requests}/{self.max_requests_per_key} (API Key {self.current_key_index + 1})")
                    return html_content
            else:
                print(f"⚠️ ScrapingDog failed for {url}: Status {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ Error fetching {url}: {e}")
            return None

    def clean_phone_number(self, phone_str: str) -> Optional[str]:
        """Clean phone number for deduplication"""
        if not phone_str or pd.isna(phone_str):
            return None

        phone_clean = re.sub(r'[^\d]', '', str(phone_str))

        if len(phone_clean) < 10:
            return None

        if len(phone_clean) == 11 and phone_clean.startswith('1'):
            phone_clean = phone_clean[1:]
        elif len(phone_clean) > 11:
            return None

        return phone_clean

    def is_phone_duplicate(self, phone: str) -> bool:
        """Check if phone is already scraped"""
        cleaned = self.clean_phone_number(phone)
        if not cleaned:
            return False

        with self.phone_lock:
            return cleaned in self.known_phones

    def add_phone_to_database(self, phone: str, data: Dict):
        """Add phone to deduplication database"""
        cleaned = self.clean_phone_number(phone)
        if cleaned:
            with self.phone_lock:
                if cleaned not in self.known_phones:
                    self.known_phones.add(cleaned)
                    self.phone_to_data[cleaned] = data

    def extract_profile_urls_from_search(self, html: str, city_info: Dict) -> List[Dict]:
        """Extract profile URLs from search page with age filtering"""
        if not html:
            return []

        try:
            soup = BeautifulSoup(html, 'html.parser')
            profile_urls = []

            # Find all profile links - look for multiple patterns
            # Pattern 1: Links with class 'listtitle'
            listtitle_links = soup.find_all('a', class_='listtitle', href=True)

            # Pattern 2: All links containing 'post/escorts'
            all_links = soup.find_all('a', href=True)
            escort_links = [link for link in all_links if 'post/escorts' in link.get('href', '')]

            # Combine all potential links
            candidate_links = listtitle_links + escort_links

            for link in candidate_links:
                href = link['href']

                # Only process escort post links
                if 'post/escorts' not in href:
                    continue

                # Build full URL
                if href.startswith('http'):
                    full_url = href
                elif href.startswith('/'):
                    full_url = f"https://aaok.com.listcrawler.eu{href}"
                else:
                    full_url = urljoin(city_info['base_url'], href)

                # Extract age information from context
                age_found = False
                age_value = None

                # Search in link text and surrounding elements
                context_text = link.get_text() + ' '
                if link.parent:
                    context_text += link.parent.get_text()

                # Look for age patterns
                age_matches = re.findall(r'(?:Age[:\s]*|^|\s)(\d{2})(?:\s|$)', context_text, re.IGNORECASE)
                if age_matches:
                    try:
                        age_value = int(age_matches[0])
                        if 18 <= age_value <= 30:  # Age filter ≤30
                            age_found = True
                    except ValueError:
                        pass

                # Include if age ≤30 or age not found (will filter later)
                if not age_found or (age_value and age_value <= 30):
                    profile_urls.append({
                        'url': full_url,
                        'city': city_info['name'],
                        'state': city_info['state'],
                        'source': city_info['source'],
                        'estimated_age': age_value
                    })

            # Remove duplicates
            seen_urls = set()
            unique_urls = []
            for url_data in profile_urls:
                if url_data['url'] not in seen_urls:
                    seen_urls.add(url_data['url'])
                    unique_urls.append(url_data)

            return unique_urls

        except Exception as e:
            print(f"❌ Error extracting URLs from search page: {e}")
            return []

    def scrape_search_pages_for_city(self, city_info: Dict, max_pages: int = 25) -> List[Dict]:
        """Phase 1: Scrape all search pages for a specific city"""
        city_name = city_info['name']
        state_name = city_info['state']
        base_url = city_info['base_url']

        print(f"\n🏙️ Phase 1: Scraping search pages for {city_name}, {state_name}")
        print(f"   📡 Base URL: {base_url}")

        all_urls = []
        page = 1
        consecutive_empty = 0

        while page <= max_pages and consecutive_empty < 3:
            search_url = f"{base_url}/{page}"
            print(f"      📄 Page {page}: {search_url}")

            html = self.fetch_with_scrapingdog(search_url)
            if not html:
                consecutive_empty += 1
                page += 1
                continue

            # Extract profile URLs from this search page
            page_urls = self.extract_profile_urls_from_search(html, city_info)

            if not page_urls:
                consecutive_empty += 1
                print(f"         ❌ No URLs found on page {page}")
            else:
                consecutive_empty = 0
                all_urls.extend(page_urls)
                print(f"         ✅ Found {len(page_urls)} URLs on page {page}")

            self.stats['search_pages_scraped'] += 1
            page += 1
            time.sleep(1)  # Rate limiting

        print(f"🎯 {city_name} total: {len(all_urls)} profile URLs")
        self.stats['total_urls_found'] += len(all_urls)
        return all_urls

    def extract_profile_data_with_mistral(self, html: str, url_data: Dict) -> Optional[Dict]:
        """Phase 2: Extract profile data using Mistral AI"""
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Clean HTML
            for script in soup(["script", "style"]):
                script.decompose()

            text_content = soup.get_text()
            lines = (line.strip() for line in text_content.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text_content = ' '.join(chunk for chunk in chunks if chunk)

            # Basic regex extraction as fallback
            phone_match = re.search(r'(\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4})', text_content)
            age_match = re.search(r'(?:Age[:\s]*|I\'m\s|I\sam\s)(\d{2})', text_content, re.IGNORECASE)
            name_match = re.search(r'(?:I\'m\s|My name is\s|Call me\s)([A-Za-z]+)', text_content, re.IGNORECASE)

            # Try Mistral AI for enhanced extraction
            if self.mistral_client:
                try:
                    prompt = f"""
Extract data from this escort profile. Return ONLY a JSON object with these fields:
- "name": person's name (string or null)
- "age": age in years (integer or null)
- "phone": phone number (string or null)
- "description": brief description (string or null)
- "social_media": social media handles as object
- "email": email address (string or null)
- "is_female": true if female profile, false otherwise

Only include profiles for women aged 30 or under. Return null for all fields if male or over 30.

Profile text: {text_content[:2000]}
"""

                    messages = [ChatMessage(role="user", content=prompt)]
                    response = self.mistral_client.chat(
                        model="mistral-large-latest",
                        messages=messages,
                        temperature=0.1,
                        max_tokens=500
                    )

                    mistral_text = response.choices[0].message.content.strip()
                    json_match = re.search(r'\{.*\}', mistral_text, re.DOTALL)

                    if json_match:
                        mistral_data = json.loads(json_match.group())

                        if mistral_data.get('is_female') and mistral_data.get('phone'):
                            phone = mistral_data.get('phone')

                            # Check for duplicates
                            if self.is_phone_duplicate(phone):
                                self.stats['duplicates_skipped'] += 1
                                return None

                            result = {
                                'name': mistral_data.get('name', ''),
                                'age': mistral_data.get('age'),
                                'phone': phone,
                                'description': mistral_data.get('description', ''),
                                'social_media': json.dumps(mistral_data.get('social_media', {})),
                                'email': mistral_data.get('email', ''),
                                'city': url_data['city'],
                                'state': url_data['state'],
                                'source': url_data['source'],
                                'url': url_data['url'],
                                'post_id': self.extract_post_id(url_data['url']),
                                'scraped_at': datetime.now().isoformat()
                            }

                            self.add_phone_to_database(phone, result)
                            return result

                except Exception as e:
                    print(f"⚠️ Mistral AI failed for {url_data['url']}: {e}")

            # Fallback to regex
            if phone_match and age_match:
                phone = phone_match.group(1)
                age = int(age_match.group(1))

                if age > 30:
                    return None

                if self.is_phone_duplicate(phone):
                    self.stats['duplicates_skipped'] += 1
                    return None

                result = {
                    'name': name_match.group(1) if name_match else '',
                    'age': age,
                    'phone': phone,
                    'description': text_content[:200],
                    'social_media': '{}',
                    'email': '',
                    'city': url_data['city'],
                    'state': url_data['state'],
                    'source': url_data['source'],
                    'url': url_data['url'],
                    'post_id': self.extract_post_id(url_data['url']),
                    'scraped_at': datetime.now().isoformat()
                }

                self.add_phone_to_database(phone, result)
                return result

            return None

        except Exception as e:
            print(f"❌ Error extracting profile from {url_data['url']}: {e}")
            return None

    def extract_post_id(self, url: str) -> str:
        """Extract post ID from URL"""
        match = re.search(r'/(\d+)/?$', url)
        return match.group(1) if match else ''

    def scrape_profile_url(self, url_data: Dict) -> Optional[Dict]:
        """Phase 2: Scrape individual profile URL"""
        url = url_data['url']

        try:
            html = self.fetch_with_scrapingdog(url, render_js=True)
            if not html:
                self.stats['failed_extractions'] += 1
                return None

            profile_data = self.extract_profile_data_with_mistral(html, url_data)
            if profile_data:
                self.stats['profiles_scraped'] += 1
                return profile_data
            else:
                self.stats['failed_extractions'] += 1
                return None

        except Exception as e:
            print(f"❌ Error scraping {url}: {e}")
            self.stats['failed_extractions'] += 1
            return None

    def phase1_collect_all_urls(self) -> List[Dict]:
        """Phase 1: Collect all profile URLs from all target cities"""
        print("🚀 PHASE 1: Collecting all search page URLs from target cities")
        print("=" * 60)

        all_urls = []

        for city_info in self.target_cities:
            city_urls = self.scrape_search_pages_for_city(city_info)
            all_urls.extend(city_urls)

            # Save intermediate results
            city_filename = f"targeted_urls_{city_info['name'].replace(' ', '_')}_{city_info['state'].replace(' ', '_')}.json"
            with open(city_filename, 'w') as f:
                json.dump(city_urls, f, indent=2)
            print(f"💾 Saved {len(city_urls)} URLs to {city_filename}")

        # Save all URLs
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        all_urls_filename = f"targeted_all_urls_{timestamp}.json"
        with open(all_urls_filename, 'w') as f:
            json.dump(all_urls, f, indent=2)

        print(f"\n🎯 PHASE 1 COMPLETE")
        print(f"✅ Total URLs collected: {len(all_urls)}")
        print(f"💾 All URLs saved to: {all_urls_filename}")
        print(f"📊 Search pages scraped: {self.stats['search_pages_scraped']}")
        print(f"🌐 API requests made: {self.stats['api_requests']}")

        return all_urls

    def save_progress(self, force_new_file=False):
        """Save current progress to file"""
        progress_data = {
            'scraped_profiles': self.scraped_profiles,
            'stats': self.stats,
            'current_key_index': self.current_key_index,
            'current_key_requests': self.current_key_requests,
            'processed_urls': len(self.scraped_profiles)
        }

        if force_new_file or not hasattr(self, 'current_progress_file'):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.current_progress_file = f"scraping_progress_{timestamp}.json"

        progress_file = self.current_progress_file

        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)

        print(f"💾 Progress saved to {progress_file} ({len(self.scraped_profiles)} profiles)")

    def load_progress(self, progress_file: str) -> int:
        """Load progress from file and return starting index"""
        if not os.path.exists(progress_file):
            print(f"❌ Progress file {progress_file} not found")
            return 0

        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)

            self.scraped_profiles = progress_data.get('scraped_profiles', [])
            self.stats = progress_data.get('stats', self.stats)
            self.current_key_index = progress_data.get('current_key_index', 0)
            self.current_key_requests = progress_data.get('current_key_requests', 0)

            processed_count = progress_data.get('processed_urls', 0)
            print(f"📂 Progress loaded: {processed_count} URLs already processed")
            print(f"🔄 Resuming with API key {self.current_key_index + 1} ({self.current_key_requests}/{self.max_requests_per_key} requests)")

            return processed_count

        except Exception as e:
            print(f"❌ Error loading progress: {e}")
            return 0

    def phase2_scrape_all_profiles(self, profile_urls: List[Dict], max_workers: int = 4, resume_file: str = None) -> List[Dict]:
        """Phase 2: Scrape all individual profile URLs with progress saving and resume"""

        # Limit the number of URLs to process
        urls_to_process = profile_urls[:self.max_urls_to_process]

        print(f"\n🚀 PHASE 2: Scraping {len(urls_to_process)} individual profiles (limited to {self.max_urls_to_process})")
        print("=" * 60)

        # Load progress if resume file provided
        start_index = 0
        if resume_file:
            start_index = self.load_progress(resume_file)

        results = list(self.scraped_profiles)  # Start with existing results
        remaining_urls = urls_to_process[start_index:]

        print(f"📊 Starting from URL {start_index + 1}/{len(urls_to_process)}")
        print(f"🔄 Remaining URLs to process: {len(remaining_urls)}")
        print(f"👥 Using {max_workers} workers for parallel processing")

        completed = start_index

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_url = {executor.submit(self.scrape_profile_url, url_data): url_data for url_data in remaining_urls}

            for future in as_completed(future_to_url):
                # Check if all API keys are exhausted
                if self.current_key_index >= len(self.scrapingdog_api_keys):
                    print("❌ All ScrapingDog API keys exhausted! Saving progress and stopping.")
                    break

                url_data = future_to_url[future]
                result = future.result()

                if result:
                    with self.results_lock:
                        results.append(result)
                        self.scraped_profiles.append(result)

                completed += 1

                # Progress reporting every 10 URLs
                if completed % 10 == 0:
                    print(f"📊 Progress: {completed}/{len(urls_to_process)} URLs processed")
                    print(f"   ✅ Valid profiles: {len(results)}")
                    print(f"   🔄 Duplicates: {self.stats['duplicates_skipped']}")
                    print(f"   ❌ Failed: {self.stats['failed_extractions']}")
                    print(f"   🎯 API Key {self.current_key_index + 1}: {self.current_key_requests}/{self.max_requests_per_key} requests")

                # Check if we've processed the target number of URLs
                if completed >= self.max_urls_to_process:
                    print(f"🎯 Reached target of {self.max_urls_to_process} URLs! Stopping and saving progress.")
                    break

                # Random delay between batches
                time.sleep(random.uniform(0.5, 2.0))

        # Final save with new filename
        print(f"💾 Saving final progress...")
        self.save_progress(force_new_file=True)

        print(f"\n🎯 PHASE 2 COMPLETE")
        print(f"✅ Valid profiles scraped: {len(results)}")
        print(f"🔄 Duplicates skipped: {self.stats['duplicates_skipped']}")
        print(f"❌ Failed extractions: {self.stats['failed_extractions']}")
        print(f"🎯 Final API key usage: API Key {self.current_key_index + 1}: {self.current_key_requests}/{self.max_requests_per_key}")
        print(f"📊 Processed {completed}/{len(urls_to_process)} URLs (limit: {self.max_urls_to_process})")

        return results

    def save_results_to_excel(self, results: List[Dict], filename: str):
        """Save results to Excel with proper formatting"""
        if not results:
            print("⚠️ No results to save")
            return

        df = pd.DataFrame(results)

        # Column order
        column_order = ['state', 'city', 'source', 'name', 'age', 'phone', 'description',
                       'social_media', 'email', 'url', 'post_id', 'scraped_at']

        df = df.reindex(columns=[col for col in column_order if col in df.columns] +
                               [col for col in df.columns if col not in column_order])

        df.to_excel(filename, index=False)
        print(f"💾 Results saved to {filename}")

        # Print statistics
        print(f"\n📊 FINAL STATISTICS")
        print(f"=" * 40)
        print(f"🏙️ Cities processed: {df['city'].nunique()}")
        print(f"🗺️ States processed: {df['state'].nunique()}")
        print(f"📡 Sources used: {', '.join(df['source'].unique())}")
        print(f"📱 Unique phone numbers: {len(self.known_phones)}")
        if 'age' in df.columns and len(df) > 0:
            print(f"👥 Age range: {df['age'].min()}-{df['age'].max()}")
        print(f"🌐 Total API requests: {self.stats['api_requests']}")

        # City breakdown
        print(f"\n🏙️ Profiles by City:")
        city_counts = df.groupby(['state', 'city']).size().sort_values(ascending=False)
        for (state, city), count in city_counts.items():
            print(f"   {city}, {state}: {count}")

    def run_complete_targeted_scraping(self, max_workers: int = 3):
        """Run complete two-phase targeted scraping process"""
        try:
            # Phase 1: Collect all URLs
            all_urls = self.phase1_collect_all_urls()

            if not all_urls:
                print("❌ No URLs collected in Phase 1")
                return

            # Phase 2: Scrape all profiles
            results = self.phase2_scrape_all_profiles(all_urls, max_workers)

            if results:
                # Save results
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"targeted_scraping_results_{len(results)}_profiles_{timestamp}.xlsx"
                self.save_results_to_excel(results, filename)

                print(f"\n🎉 TARGETED SCRAPING COMPLETE!")
                print(f"📋 {len(results)} profiles scraped from {len(self.target_cities)} cities")
                print(f"💾 Results saved to: {filename}")
            else:
                print("⚠️ No valid profiles found")

        except KeyboardInterrupt:
            print("\n🛑 Scraping interrupted by user")
            if self.scraped_profiles:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"targeted_interrupted_{len(self.scraped_profiles)}_profiles_{timestamp}.xlsx"
                self.save_results_to_excel(self.scraped_profiles, filename)

        except Exception as e:
            print(f"❌ Scraping failed: {e}")
            if self.scraped_profiles:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"targeted_error_{len(self.scraped_profiles)}_profiles_{timestamp}.xlsx"
                self.save_results_to_excel(self.scraped_profiles, filename)

def main():
    """Main execution"""
    import argparse

    parser = argparse.ArgumentParser(description='Targeted City Scraper')
    parser.add_argument('--phase1-only', action='store_true', help='Only run Phase 1 (collect URLs)')
    parser.add_argument('--phase2-only', help='Only run Phase 2 with existing URLs file')
    parser.add_argument('--workers', type=int, default=3, help='Number of worker threads for Phase 2')

    args = parser.parse_args()

    scraper = TargetedCityScraper()

    if args.phase1_only:
        # Only collect URLs
        all_urls = scraper.phase1_collect_all_urls()
        print(f"✅ Phase 1 complete. {len(all_urls)} URLs collected.")

    elif args.phase2_only:
        # Only scrape profiles from existing URLs file
        try:
            with open(args.phase2_only, 'r') as f:
                all_urls = json.load(f)
            print(f"📁 Loaded {len(all_urls)} URLs from {args.phase2_only}")

            results = scraper.phase2_scrape_all_profiles(all_urls, args.workers)

            if results:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"targeted_phase2_results_{len(results)}_profiles_{timestamp}.xlsx"
                scraper.save_results_to_excel(results, filename)

        except FileNotFoundError:
            print(f"❌ URLs file not found: {args.phase2_only}")
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON in file: {args.phase2_only}")

    else:
        # Run full two-phase process
        scraper.run_complete_targeted_scraping(args.workers)

if __name__ == "__main__":
    main()
