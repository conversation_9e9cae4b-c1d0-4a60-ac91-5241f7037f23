#!/usr/bin/env python3
"""
Runner script for Phase2 batch extraction

This script processes all phase2 batch files from phase2_batch_01_500_urls.json
to phase2_batch_18_315_urls.json with the following features:
- Processes all 18 phase2 batch files in sequence
- Saves progress after each batch file
- Skips URLs with duplicate phone numbers across all batches
- Resumes from last completed batch if interrupted
- Extracts: Phone Number, Name, Location, City, Social Media, Age, Raw Text

Usage:
    python run_phase2_extraction.py
"""

import os
import sys
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_batch_files():
    """Check if all phase2 batch files exist"""
    missing_files = []
    found_files = []

    for i in range(1, 19):  # batch_01 to batch_18
        if i == 18:
            filename = f"phase2_batch_{i:02d}_315_urls.json"
        else:
            filename = f"phase2_batch_{i:02d}_500_urls.json"

        if os.path.exists(filename):
            found_files.append(filename)
        else:
            missing_files.append(filename)

    return found_files, missing_files

def show_file_info(found_files):
    """Show information about the batch files"""
    total_size = 0
    total_urls = 0

    logger.info(f"Found {len(found_files)} phase2 batch files:")

    for filename in found_files:
        try:
            size_mb = os.path.getsize(filename) / (1024 * 1024)
            total_size += size_mb

            # Estimate URLs based on filename
            if "315_urls" in filename:
                estimated_urls = 315
            else:
                estimated_urls = 500

            total_urls += estimated_urls
            logger.info(f"  {filename:<35} ({size_mb:.2f} MB, ~{estimated_urls} URLs)")

        except Exception as e:
            logger.error(f"Error reading {filename}: {e}")

    logger.info(f"\nTotal estimated URLs: {total_urls:,}")
    logger.info(f"Total file size: {total_size:.2f} MB")

    # Estimate processing time (2 seconds per URL average)
    estimated_hours = total_urls * 2 / 3600
    logger.info(f"Estimated processing time: {estimated_hours:.1f} hours")

    return total_urls

def main():
    """Main runner function"""
    print("=" * 80)
    print("PHASE2 BATCH FILES EXTRACTION")
    print("=" * 80)
    print("Processing: phase2_batch_01_500_urls.json to phase2_batch_18_315_urls.json")
    print("")

    # Check batch files
    found_files, missing_files = check_batch_files()

    if missing_files:
        logger.warning(f"Missing batch files: {len(missing_files)}")
        for missing in missing_files:
            logger.warning(f"  - {missing}")
        print("")

    if not found_files:
        logger.error("No phase2 batch files found!")
        return

    # Show file information
    total_urls = show_file_info(found_files)

    print("")
    print("EXTRACTION FEATURES:")
    print("✓ Processes all phase2 batch files in sequence")
    print("✓ Saves results after each batch file")
    print("✓ Skips duplicate phone numbers across all batches")
    print("✓ Resumes from last completed batch if interrupted")
    print("✓ Extracts: Phone, Name, Age, Location, City, Social Media, Raw Text")
    print("")

    logger.info("Starting Phase2 batch extraction automatically...")
    logger.info("You can safely interrupt (Ctrl+C) and resume later")
    print("")

    try:
        # Import and run the extractor
        from phase2_batch_extractor import Phase2BatchExtractor

        # Initialize extractor
        extractor = Phase2BatchExtractor(
            delay=1.0,  # 1 second delay between requests
            max_workers=5  # 5 concurrent workers
        )

        # Process all batches
        extractor.process_all_batches()

        print("")
        print("=" * 80)
        print("PHASE2 EXTRACTION COMPLETE!")
        print("=" * 80)

        logger.info("All phase2 batch files have been processed successfully!")
        logger.info("Check the individual batch result files and the final combined file.")

    except KeyboardInterrupt:
        print("\n")
        logger.info("Extraction interrupted by user")
        logger.info("Progress has been saved - you can resume by running this script again")
        print("")
        print("To resume extraction, simply run:")
        print("python run_phase2_extraction.py")

    except ImportError:
        logger.error("Could not import phase2_batch_extractor module")
        logger.error("Make sure phase2_batch_extractor.py is in the same directory")

    except Exception as e:
        logger.error(f"Extraction error: {e}")
        logger.error("Check the phase2_batch_extractor.log file for detailed error information")
        print("")
        print("If you encounter persistent errors, you can:")
        print("1. Check your internet connection")
        print("2. Reduce the number of workers (edit max_workers in phase2_batch_extractor.py)")
        print("3. Increase the delay between requests (edit delay in phase2_batch_extractor.py)")

if __name__ == '__main__':
    main()
