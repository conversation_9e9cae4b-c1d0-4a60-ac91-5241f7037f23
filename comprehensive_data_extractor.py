#!/usr/bin/env python3
"""
Comprehensive Data Extractor for Escort Profile URLs

This script extracts the following data fields from profile pages:
- Phone Number
- Name
- Location
- City
- Social Media
- Age
- Raw Text (full description)

Usage:
    python comprehensive_data_extractor.py --json_file urls.json --output output.xlsx
"""

import json
import requests
import pandas as pd
import re
import time
import logging
from datetime import datetime
from bs4 import BeautifulSoup
import argparse
from urllib.parse import urljoin
import os
from typing import Dict, List, Optional, Any
import concurrent.futures
from threading import Lock

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_extractor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProfileDataExtractor:
    """Extract profile data from escort websites"""

    def __init__(self, delay: float = 1.0, max_workers: int = 5):
        self.delay = delay
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.results = []
        self.processed_count = 0
        self.total_count = 0
        self.lock = Lock()

    def extract_phone_number(self, soup: BeautifulSoup, html_content: str) -> Optional[str]:
        """Extract phone number from various possible locations"""
        # Method 1: Look for tel: links
        tel_link = soup.find('a', href=re.compile(r'tel:'))
        if tel_link:
            phone = tel_link.get_text(strip=True)
            if phone and re.search(r'\d{3}-\d{3}-\d{4}', phone):
                return phone

        # Method 2: Look in viewposttelephone class
        phone_div = soup.find('div', class_='viewposttelephone')
        if phone_div:
            phone_text = phone_div.get_text(strip=True)
            phone_match = re.search(r'(\d{3}[-.]?\d{3}[-.]?\d{4})', phone_text)
            if phone_match:
                return phone_match.group(1)

        # Method 3: Look for phone patterns in the entire content
        phone_patterns = [
            r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})',
            r'\((\d{3})\)[-.\s]?(\d{3})[-.\s]?(\d{4})',
            r'(\d{10})'
        ]

        for pattern in phone_patterns:
            matches = re.findall(pattern, html_content)
            if matches:
                if isinstance(matches[0], tuple):
                    return ''.join(matches[0])
                return matches[0]

        return None

    def extract_name(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract name from the profile"""
        # Method 1: Look for Nym field
        nym_span = soup.find('span', string=re.compile(r'Nym:'))
        if nym_span and nym_span.next_sibling:
            name = str(nym_span.next_sibling).strip()
            if name and name != 'None':
                return name

        # Method 2: Look in viewpostname class
        name_div = soup.find('div', class_='viewpostname')
        if name_div:
            # Look for text after "Nym:"
            text = name_div.get_text()
            nym_match = re.search(r'Nym:\s*(.+?)(?:\s|$)', text)
            if nym_match:
                return nym_match.group(1).strip()

        # Method 3: Look in post title
        title_div = soup.find('div', class_='viewposttitle')
        if title_div:
            title_text = title_div.get_text(strip=True)
            # Extract name from title patterns like "✨Make A Wish✨ - 28"
            name_match = re.search(r'([A-Za-z]+)', title_text)
            if name_match:
                return name_match.group(1)

        return None

    def extract_location(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract location from the profile"""
        # Method 1: Look for Location field
        location_spans = soup.find_all('span', class_='postContentBold')
        for span in location_spans:
            if 'Location:' in span.get_text():
                next_text = span.next_sibling
                if next_text:
                    return str(next_text).strip()

        # Method 2: Look in location list items
        location_lis = soup.find_all('li')
        for li in location_lis:
            li_text = li.get_text()
            if 'Location:' in li_text:
                location_match = re.search(r'Location:\s*(.+)', li_text)
                if location_match:
                    return location_match.group(1).strip()

        return None

    def extract_age(self, soup: BeautifulSoup) -> Optional[int]:
        """Extract age from the profile"""
        # Method 1: Look for Age field in spans
        age_spans = soup.find_all('span', class_='postContentBold')
        for span in age_spans:
            if 'Age:' in span.get_text():
                next_text = span.next_sibling
                if next_text:
                    age_match = re.search(r'(\d+)', str(next_text))
                    if age_match:
                        return int(age_match.group(1))

        # Method 2: Look in age list items
        age_lis = soup.find_all('li')
        for li in age_lis:
            li_text = li.get_text()
            if 'Age:' in li_text:
                age_match = re.search(r'Age:\s*(\d+)', li_text)
                if age_match:
                    return int(age_match.group(1))

        # Method 3: Look in post title age span
        age_span = soup.find('span', class_='postTitleAge')
        if age_span:
            age_match = re.search(r'(\d+)', age_span.get_text())
            if age_match:
                return int(age_match.group(1))

        return None

    def extract_social_media(self, soup: BeautifulSoup, html_content: str) -> Optional[str]:
        """Extract social media information"""
        social_platforms = ['Instagram', 'Onlyfans', 'Twitter', 'Facebook', 'Snapchat', 'TikTok']
        social_info = []

        # Look for social media patterns in the text
        for platform in social_platforms:
            # Pattern: Platform: username or Platform username
            pattern = rf'{platform}[:\s]+([A-Za-z0-9_\.]+)'
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 2:  # Valid username
                    social_info.append(f"{platform}: {match}")

        return '; '.join(social_info) if social_info else None

    def extract_raw_text(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract the main description text"""
        # Look for viewpostbody class
        body_div = soup.find('div', class_='viewpostbody')
        if body_div:
            # Clean up the text
            text = body_div.get_text(separator=' ', strip=True)
            # Remove excessive whitespace
            text = re.sub(r'\s+', ' ', text)
            return text

        # Fallback: look for main content areas
        content_selectors = [
            'div.post-content',
            'div.content',
            'div.description',
            'div.post-body'
        ]

        for selector in content_selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text(separator=' ', strip=True)
                text = re.sub(r'\s+', ' ', text)
                return text

        return None

    def extract_profile_data(self, url: str, city: str = None, state: str = None) -> Dict[str, Any]:
        """Extract all profile data from a single URL"""
        result = {
            'url': url,
            'phone_number': None,
            'name': None,
            'location': None,
            'city': city,
            'state': state,
            'social_media': None,
            'age': None,
            'raw_text': None,
            'status': 'failed',
            'error': None,
            'extracted_at': datetime.now().isoformat()
        }

        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')
            html_content = response.text

            # Extract all fields
            result['phone_number'] = self.extract_phone_number(soup, html_content)
            result['name'] = self.extract_name(soup)
            result['location'] = self.extract_location(soup)
            result['age'] = self.extract_age(soup)
            result['social_media'] = self.extract_social_media(soup, html_content)
            result['raw_text'] = self.extract_raw_text(soup)

            result['status'] = 'success'
            logger.info(f"Successfully extracted data from {url}")

        except requests.exceptions.RequestException as e:
            result['error'] = f"Request error: {str(e)}"
            logger.error(f"Request error for {url}: {e}")
        except Exception as e:
            result['error'] = f"Parsing error: {str(e)}"
            logger.error(f"Parsing error for {url}: {e}")

        return result

    def process_url_batch(self, urls_batch: List[Dict]) -> List[Dict]:
        """Process a batch of URLs"""
        batch_results = []

        for url_data in urls_batch:
            url = url_data.get('url')
            city = url_data.get('city')
            state = url_data.get('state')

            result = self.extract_profile_data(url, city, state)
            batch_results.append(result)

            with self.lock:
                self.processed_count += 1
                if self.processed_count % 10 == 0:
                    logger.info(f"Processed {self.processed_count}/{self.total_count} URLs")

            time.sleep(self.delay)

        return batch_results

    def process_urls(self, urls_data: List[Dict]) -> List[Dict]:
        """Process all URLs with threading"""
        self.total_count = len(urls_data)
        self.processed_count = 0
        self.results = []

        logger.info(f"Starting to process {self.total_count} URLs with {self.max_workers} workers")

        # Split URLs into batches for threading
        batch_size = max(1, len(urls_data) // self.max_workers)
        batches = [urls_data[i:i + batch_size] for i in range(0, len(urls_data), batch_size)]

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_batch = {
                executor.submit(self.process_url_batch, batch): batch
                for batch in batches
            }

            for future in concurrent.futures.as_completed(future_to_batch):
                try:
                    batch_results = future.result()
                    self.results.extend(batch_results)
                except Exception as e:
                    logger.error(f"Batch processing error: {e}")

        logger.info(f"Completed processing {len(self.results)} URLs")
        return self.results

    def save_to_excel(self, results: List[Dict], output_file: str):
        """Save results to Excel file"""
        df = pd.DataFrame(results)

        # Reorder columns for better readability
        column_order = [
            'url', 'phone_number', 'name', 'location', 'city', 'state',
            'age', 'social_media', 'raw_text', 'status', 'error', 'extracted_at'
        ]

        # Only include columns that exist
        available_columns = [col for col in column_order if col in df.columns]
        df = df[available_columns]

        # Save to Excel with formatting
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Profile_Data', index=False)

            # Get the worksheet
            worksheet = writer.sheets['Profile_Data']

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        logger.info(f"Results saved to {output_file}")

def load_urls_from_json(json_file: str) -> List[Dict]:
    """Load URLs from JSON file"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    urls_list = []

    # Handle different JSON structures
    if isinstance(data, list):
        # Direct list of URL objects
        urls_list = data
    elif isinstance(data, dict):
        # Dictionary with nested URL lists
        for key, value in data.items():
            if isinstance(value, dict) and 'urls' in value:
                # Structure like {"city_state_source": {"urls": [...], ...}}
                urls = value['urls']
                city = value.get('city')
                state = value.get('state')

                for url in urls:
                    if isinstance(url, str):
                        urls_list.append({
                            'url': url,
                            'city': city,
                            'state': state
                        })
                    elif isinstance(url, dict):
                        url_data = url.copy()
                        if not url_data.get('city'):
                            url_data['city'] = city
                        if not url_data.get('state'):
                            url_data['state'] = state
                        urls_list.append(url_data)
            elif isinstance(value, list):
                # Direct list under a key
                for url_data in value:
                    if isinstance(url_data, dict):
                        urls_list.append(url_data)

    logger.info(f"Loaded {len(urls_list)} URLs from {json_file}")
    return urls_list

def main():
    parser = argparse.ArgumentParser(description='Extract comprehensive data from escort profile URLs')
    parser.add_argument('--json_file', required=True, help='JSON file containing URLs')
    parser.add_argument('--output', required=True, help='Output Excel file')
    parser.add_argument('--delay', type=float, default=1.0, help='Delay between requests (seconds)')
    parser.add_argument('--max_workers', type=int, default=5, help='Maximum number of worker threads')
    parser.add_argument('--limit', type=int, help='Limit number of URLs to process (for testing)')

    args = parser.parse_args()

    # Load URLs
    urls_data = load_urls_from_json(args.json_file)

    if args.limit:
        urls_data = urls_data[:args.limit]
        logger.info(f"Limited to first {args.limit} URLs")

    # Initialize extractor
    extractor = ProfileDataExtractor(delay=args.delay, max_workers=args.max_workers)

    # Process URLs
    results = extractor.process_urls(urls_data)

    # Save results
    extractor.save_to_excel(results, args.output)

    # Print summary
    successful = sum(1 for r in results if r['status'] == 'success')
    failed = len(results) - successful

    logger.info(f"Processing complete:")
    logger.info(f"  Successful: {successful}")
    logger.info(f"  Failed: {failed}")
    logger.info(f"  Total: {len(results)}")

    # Show sample of successful extractions
    successful_results = [r for r in results if r['status'] == 'success'][:3]
    if successful_results:
        logger.info("\nSample successful extractions:")
        for i, result in enumerate(successful_results, 1):
            logger.info(f"  {i}. Name: {result.get('name', 'N/A')}, "
                       f"Phone: {result.get('phone_number', 'N/A')}, "
                       f"Age: {result.get('age', 'N/A')}, "
                       f"Location: {result.get('location', 'N/A')}")

if __name__ == '__main__':
    main()
