#!/usr/bin/env python3
"""
Complete Two-Phase Escort Profile Scraper

This script combines Phase 1 (URL extraction from search pages) and Phase 2 (data extraction from profiles)
into a single integrated workflow.

PHASE 1: Extract URLs from search pages
- Scrapes search pages from aaok and aypapi domains
- Extracts profile URLs for specified cities
- Filters by age (≤30) when possible
- Saves intermediate URL files

PHASE 2: Extract profile data from URLs
- Phone numbers (formatted as 1xxxxxxxxxx)
- Names, ages, locations, social media
- Full profile text descriptions
- Duplicate phone detection
- Progress saving with resume capability

Usage:
    python complete_two_phase_scraper.py
    python complete_two_phase_scraper.py --cities "Baltimore,Philadelphia" --max-pages 10
    python complete_two_phase_scraper.py --phase2-only  # Skip Phase 1, use existing URLs
"""

import json
import requests
import pandas as pd
import re
import time
import logging
import os
from datetime import datetime
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any, Set, Tuple
import concurrent.futures
from threading import Lock
from urllib.parse import urljoin, urlparse
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_two_phase_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompleteTwoPhaseScraper:
    """Complete two-phase scraper with URL extraction and data extraction"""

    def __init__(self, delay: float = 1.0, max_workers: int = 5, checkpoint_interval: int = 500):
        # Phase 1 settings (no ScraperAPI needed)
        self.sources = ['aaok', 'aypapi']  # Only use aaok and aypapi

        # Phase 2 settings
        self.delay = delay
        self.max_workers = max_workers
        self.checkpoint_interval = checkpoint_interval

        # Session setup with realistic headers for direct requests
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'no-cache',
        })

        # Data tracking
        self.results = []
        self.processed_count = 0
        self.total_count = 0
        self.lock = Lock()
        self.seen_phones: Set[str] = set()
        self.start_time = datetime.now()
        self.progress_file = None
        self.checkpoint_file = None

        # Default target cities with Ohio cities added
        self.target_cities = [
            {"name": "Columbus", "state": "Ohio", "url_name": "columbus"},
            {"name": "Cleveland", "state": "Ohio", "url_name": "cleveland"},
            {"name": "Dayton", "state": "Ohio", "url_name": "dayton"},
            {"name": "Toledo", "state": "Ohio", "url_name": "toledo"},
            {"name": "Cincinnati", "state": "Ohio", "url_name": "cincinnati"},
            {"name": "South New Jersey", "state": "New Jersey", "url_name": "south%20new%20jersey"},
            {"name": "Philadelphia", "state": "Pennsylvania", "url_name": "philadelphia"},
            {"name": "Pittsburgh", "state": "Pennsylvania", "url_name": "pittsburgh"},
            {"name": "Wilmington", "state": "Delaware", "url_name": "wilmington"},
            {"name": "Dover", "state": "Delaware", "url_name": "dover"},
            {"name": "Baltimore", "state": "Maryland", "url_name": "baltimore"},
            {"name": "Annapolis", "state": "Maryland", "url_name": "annapolis"}
        ]

        # Statistics
        self.phase1_stats = {
            'search_pages_scraped': 0,
            'urls_extracted': 0,
            'requests_made': 0,
            'cities_processed': 0
        }

    # =====================================================================
    # PHASE 1: URL EXTRACTION FROM SEARCH PAGES
    # =====================================================================

    def fetch_page_content(self, url: str, retries: int = 3) -> Optional[str]:
        """Fetch page content directly with retries and error handling"""
        for attempt in range(retries):
            try:
                logger.debug(f"Fetching: {url} (attempt {attempt + 1})")
                response = self.session.get(url, timeout=30)
                self.phase1_stats['requests_made'] += 1

                if response.status_code == 200:
                    return response.text
                elif response.status_code == 403:
                    logger.warning(f"403 Forbidden for {url} - trying with different headers")
                    # Try with simplified headers
                    simple_headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                    response = self.session.get(url, headers=simple_headers, timeout=30)
                    if response.status_code == 200:
                        return response.text
                else:
                    logger.warning(f"HTTP {response.status_code} for {url}")

            except requests.exceptions.RequestException as e:
                logger.warning(f"Request failed for {url}: {e}")
                if attempt < retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        return None

    def extract_age_from_context(self, link_element) -> Optional[int]:
        """Extract age information from link context"""
        try:
            # Check the link text itself
            link_text = link_element.get_text(strip=True)
            age_match = re.search(r'\b(\d{2})\b', link_text)
            if age_match:
                age = int(age_match.group(1))
                if 18 <= age <= 99:  # Reasonable age range
                    return age

            # Check parent elements for age info
            parent = link_element.parent
            for _ in range(3):  # Check up to 3 parent levels
                if parent is None:
                    break

                # Look for age in class names like 'titleAge'
                age_divs = parent.find_all('div', class_=re.compile(r'.*[Aa]ge.*'))
                for age_div in age_divs:
                    age_text = age_div.get_text(strip=True)
                    age_match = re.search(r'\b(\d{2})\b', age_text)
                    if age_match:
                        age = int(age_match.group(1))
                        if 18 <= age <= 99:
                            return age

                # Check all text in parent for age patterns
                parent_text = parent.get_text()
                age_patterns = [
                    r'Age[:\s]*(\d{2})',
                    r'(\d{2})\s*years?\s*old',
                    r'I\'m\s*(\d{2})',
                    r'Age\s*(\d{2})',
                    r'\b(\d{2})\b'  # Just two digits
                ]

                for pattern in age_patterns:
                    age_match = re.search(pattern, parent_text, re.IGNORECASE)
                    if age_match:
                        age = int(age_match.group(1))
                        if 18 <= age <= 99:
                            return age

                parent = parent.parent

            return None

        except Exception:
            return None

    def extract_profile_urls_from_html(self, html: str, source: str, base_url: str) -> List[Dict]:
        """Extract profile URLs from search page HTML"""
        if not html:
            return []

        try:
            soup = BeautifulSoup(html, 'html.parser')
            profile_urls = []

            # Look for multiple patterns
            listtitle_links = soup.find_all('a', class_='listtitle')
            all_links = soup.find_all('a', href=True)
            candidate_links = listtitle_links + all_links

            for link in candidate_links:
                href = link.get('href', '')

                # Only process links that contain 'post/escorts'
                if 'post/escorts' not in href:
                    continue

                # Skip escortalligator links (only aaok and aypapi)
                if 'escortalligator' in href:
                    continue

                # Ensure link belongs to current source
                if source not in href:
                    continue

                # Build full URL
                if href.startswith('http'):
                    full_url = href
                elif href.startswith('/'):
                    full_url = f"https://{source}.com.listcrawler.eu{href}"
                else:
                    full_url = urljoin(base_url, href)

                # Extract age information
                age_info = self.extract_age_from_context(link)

                # Only include if age ≤ 30 or age not found (will filter in Phase 2)
                if age_info is None or age_info <= 30:
                    profile_urls.append({
                        'url': full_url,
                        'estimated_age': age_info,
                        'source': source
                    })

            # Remove duplicates
            seen_urls = set()
            unique_urls = []
            for url_data in profile_urls:
                if url_data['url'] not in seen_urls:
                    seen_urls.add(url_data['url'])
                    unique_urls.append(url_data)

            return unique_urls

        except Exception as e:
            logger.error(f"Error extracting URLs from HTML: {e}")
            return []

    def scrape_city_urls(self, city_info: Dict, max_pages: int = 15) -> List[Dict]:
        """Scrape URLs from search pages for a city"""
        city_name = city_info['name']
        state_name = city_info['state']
        url_name = city_info['url_name']

        logger.info(f"Phase 1: Extracting URLs for {city_name}, {state_name}")

        all_urls = []

        for source in self.sources:
            logger.info(f"   Scraping {source.upper()} search pages...")

            # Build base URL
            state_url = state_name.lower().replace(' ', '%20')
            base_url = f"https://{source}.com.listcrawler.eu/brief/escorts/usa/{state_url}/{url_name}"

            page = 1
            consecutive_empty = 0

            while page <= max_pages and consecutive_empty < 3:
                search_url = f"{base_url}/{page}"
                logger.info(f"      Page {page}: {search_url}")

                # Fetch search page
                html = self.fetch_page_content(search_url)
                if not html:
                    consecutive_empty += 1
                    page += 1
                    continue

                # Extract URLs from this page
                page_urls = self.extract_profile_urls_from_html(html, source, base_url)

                if not page_urls:
                    consecutive_empty += 1
                    logger.info(f"         No URLs found on page {page}")
                else:
                    consecutive_empty = 0
                    # Add city/state info to each URL
                    for url_data in page_urls:
                        url_data.update({
                            'city': city_name,
                            'state': state_name,
                            'page': page
                        })

                    all_urls.extend(page_urls)
                    logger.info(f"         Found {len(page_urls)} URLs on page {page}")

                self.phase1_stats['search_pages_scraped'] += 1
                page += 1
                time.sleep(1)  # Rate limiting

            source_count = len([u for u in all_urls if u['source'] == source])
            logger.info(f"   {source.upper()}: {source_count} URLs")

        logger.info(f"{city_name} total: {len(all_urls)} URLs")
        self.phase1_stats['urls_extracted'] += len(all_urls)
        self.phase1_stats['cities_processed'] += 1

        return all_urls

    def validate_url(self, url: str) -> bool:
        """Quick validation of URL format"""
        try:
            parsed = urlparse(url)
            return (
                parsed.scheme in ['http', 'https'] and
                'listcrawler.eu' in parsed.netloc and
                'post/escorts' in parsed.path and
                ('aaok' in parsed.netloc or 'aypapi' in parsed.netloc)
            )
        except:
            return False

    def phase1_extract_urls(self, max_pages: int = 15, custom_cities: List[str] = None) -> str:
        """Phase 1: Extract URLs from search pages"""
        logger.info("=" * 80)
        logger.info("PHASE 1: URL EXTRACTION FROM SEARCH PAGES")
        logger.info("=" * 80)

        # Filter cities if custom list provided
        if custom_cities:
            filtered_cities = [city for city in self.target_cities
                             if city['name'] in custom_cities]
            if filtered_cities:
                self.target_cities = filtered_cities
            else:
                logger.warning("No matching cities found, using all default cities")

        all_urls = []

        for city_info in self.target_cities:
            city_urls = self.scrape_city_urls(city_info, max_pages)

            # Validate URLs
            valid_urls = [url_data for url_data in city_urls if self.validate_url(url_data['url'])]
            invalid_count = len(city_urls) - len(valid_urls)

            if invalid_count > 0:
                logger.info(f"   Filtered out {invalid_count} invalid URLs")

            all_urls.extend(valid_urls)

            # Save city results
            city_filename = f"phase1_urls_{city_info['name'].replace(' ', '_')}_{city_info['state'].replace(' ', '_')}.json"
            with open(city_filename, 'w') as f:
                json.dump(valid_urls, f, indent=2)
            logger.info(f"Saved {len(valid_urls)} URLs to {city_filename}")

        # Save all URLs
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        all_urls_filename = f"phase1_all_urls_{timestamp}.json"
        with open(all_urls_filename, 'w') as f:
            json.dump(all_urls, f, indent=2)

        # Print Phase 1 statistics
        logger.info("=" * 80)
        logger.info("PHASE 1 COMPLETE")
        logger.info("=" * 80)
        logger.info(f"Total URLs extracted: {len(all_urls)}")
        logger.info(f"Cities processed: {self.phase1_stats['cities_processed']}")
        logger.info(f"Search pages scraped: {self.phase1_stats['search_pages_scraped']}")
        logger.info(f"Direct requests made: {self.phase1_stats['requests_made']}")
        logger.info(f"URLs saved to: {all_urls_filename}")

        # URL breakdown by source
        source_breakdown = {}
        for url_data in all_urls:
            source = url_data['source']
            source_breakdown[source] = source_breakdown.get(source, 0) + 1

        logger.info("URLs by Source:")
        for source, count in source_breakdown.items():
            logger.info(f"   {source}: {count} URLs")

        return all_urls_filename

    # =====================================================================
    # PHASE 2: DATA EXTRACTION FROM PROFILE PAGES
    # =====================================================================

    def normalize_phone(self, phone: str) -> str:
        """Normalize phone number to 1xxxxxxxxxx format"""
        if not phone:
            return ""

        # Remove all non-digits
        digits_only = re.sub(r'\D', '', phone)

        # Take last 10 digits if more than 10
        if len(digits_only) > 10:
            digits_only = digits_only[-10:]

        # Add "1" prefix if exactly 10 digits
        if len(digits_only) == 10:
            return "1" + digits_only

        return digits_only

    def is_phone_duplicate(self, phone: str) -> bool:
        """Check if phone number has already been processed"""
        if not phone:
            return False
        normalized = self.normalize_phone(phone)
        if normalized in self.seen_phones:
            return True
        self.seen_phones.add(normalized)
        return False

    def extract_phone_number(self, soup: BeautifulSoup, html_content: str) -> Optional[str]:
        """Extract phone number from various possible locations"""
        # Method 1: Look for tel: links
        tel_link = soup.find('a', href=re.compile(r'tel:'))
        if tel_link:
            phone = tel_link.get_text(strip=True)
            if phone and re.search(r'\d{3}[-.]?\d{3}[-.]?\d{4}', phone):
                return phone

        # Method 2: Look in viewposttelephone class
        phone_div = soup.find('div', class_='viewposttelephone')
        if phone_div:
            phone_text = phone_div.get_text(strip=True)
            phone_match = re.search(r'(\d{3}[-.]?\d{3}[-.]?\d{4})', phone_text)
            if phone_match:
                return phone_match.group(1)

        # Method 3: Search entire HTML for phone patterns
        phone_patterns = [
            r'\b(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})\b',
            r'\b(\(\d{3}\)\s?\d{3}[-.\s]?\d{4})\b',
            r'\b(\+1[-.\s]?\d{3}[-.\s]?\d{3}[-.\s]?\d{4})\b'
        ]

        for pattern in phone_patterns:
            match = re.search(pattern, html_content)
            if match:
                return match.group(1)

        return None

    def extract_name(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract name from various possible locations"""
        # Method 1: Look for name in title or heading
        title_element = soup.find('h1') or soup.find('h2') or soup.find('h3')
        if title_element:
            title_text = title_element.get_text(strip=True)
            # Extract name patterns
            name_match = re.search(r'(?:I\'m|My name is|Call me)?\s*([A-Za-z]{2,20})(?:\s+[A-Za-z]{2,20})?', title_text)
            if name_match:
                return name_match.group(1).strip()

        # Method 2: Look in viewposttitle class
        title_div = soup.find('div', class_='viewposttitle')
        if title_div:
            title_text = title_div.get_text(strip=True)
            name_match = re.search(r'([A-Za-z]{2,20})', title_text)
            if name_match:
                return name_match.group(1)

        # Method 3: Look for first name in text content
        all_text = soup.get_text()
        name_patterns = [
            r'(?:I\'m|My name is|Call me|Hi(?:,)?\s+I\'m)\s+([A-Za-z]{2,20})',
            r'([A-Za-z]{2,20})(?:\s+here|\s+available|\s+ready)'
        ]

        for pattern in name_patterns:
            match = re.search(pattern, all_text, re.IGNORECASE)
            if match:
                return match.group(1)

        return None

    def extract_location(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract location information"""
        # Look for location in various elements
        location_indicators = ['location', 'area', 'city', 'place']

        for indicator in location_indicators:
            location_div = soup.find('div', class_=re.compile(f'.*{indicator}.*', re.IGNORECASE))
            if location_div:
                location_text = location_div.get_text(strip=True)
                if location_text and len(location_text) < 50:
                    return location_text

        # Look in URL path for location
        url_elem = soup.find('link', rel='canonical')
        if url_elem:
            url = url_elem.get('href', '')
            location_match = re.search(r'/([^/]+)/\d+$', url)
            if location_match:
                return location_match.group(1).replace('-', ' ').title()

        return None

    def extract_age(self, soup: BeautifulSoup) -> Optional[int]:
        """Extract age from profile"""
        all_text = soup.get_text()

        age_patterns = [
            r'Age[:\s]*(\d{2})',
            r'(\d{2})\s*years?\s*old',
            r'I\'m\s*(\d{2})',
            r'\b(\d{2})\b\s*(?:year|yr)s?\s*old'
        ]

        for pattern in age_patterns:
            match = re.search(pattern, all_text, re.IGNORECASE)
            if match:
                age = int(match.group(1))
                if 18 <= age <= 99:
                    return age

        return None

    def extract_social_media(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract social media information"""
        social_media = []
        all_text = soup.get_text()

        # Social media patterns
        patterns = {
            'Instagram': [r'(?:instagram|ig|insta)[:\s@]*([a-zA-Z0-9_.]{3,30})', r'@([a-zA-Z0-9_.]{3,30})'],
            'OnlyFans': [r'(?:onlyfans|of)[:\s@]*([a-zA-Z0-9_.]{3,30})'],
            'Twitter': [r'(?:twitter|tweet)[:\s@]*([a-zA-Z0-9_.]{3,30})'],
            'Snapchat': [r'(?:snapchat|snap)[:\s@]*([a-zA-Z0-9_.]{3,30})']
        }

        for platform, platform_patterns in patterns.items():
            for pattern in platform_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                for match in matches:
                    if match and len(match) >= 3:
                        social_media.append(f"{platform}: {match}")

        return "; ".join(social_media) if social_media else None

    def extract_raw_text(self, soup: BeautifulSoup) -> str:
        """Extract raw text content from profile"""
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()

        # Get text content
        text = soup.get_text()

        # Clean up text
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)

        return text[:2000] if text else ""  # Limit to 2000 characters

    def extract_profile_data(self, url_data: Dict) -> Dict:
        """Extract data from a single profile URL"""
        url = url_data['url']

        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')

            # Extract all data fields
            phone_number = self.extract_phone_number(soup, response.text)
            formatted_phone = self.normalize_phone(phone_number) if phone_number else None

            # Check for duplicate phone
            is_duplicate = False
            if formatted_phone:
                is_duplicate = self.is_phone_duplicate(formatted_phone)

            result = {
                'url': url,
                'phone_number': formatted_phone,
                'name': self.extract_name(soup),
                'location': self.extract_location(soup),
                'city': url_data.get('city', ''),
                'state': url_data.get('state', ''),
                'age': self.extract_age(soup),
                'social_media': self.extract_social_media(soup),
                'raw_text': self.extract_raw_text(soup),
                'status': 'skipped_duplicate' if is_duplicate else 'success',
                'error': None,
                'duplicate_phone': is_duplicate,
                'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            logger.info(f"✅ Successfully extracted: {url}")
            return result

        except Exception as e:
            logger.error(f"❌ Error extracting {url}: {e}")
            return {
                'url': url,
                'phone_number': None,
                'name': None,
                'location': None,
                'city': url_data.get('city', ''),
                'state': url_data.get('state', ''),
                'age': None,
                'social_media': None,
                'raw_text': None,
                'status': 'failed',
                'error': str(e),
                'duplicate_phone': False,
                'extracted_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

    def save_progress(self, processed_results: List[Dict], urls_processed: int, total_urls: int):
        """Save current progress"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save progress state
        progress_data = {
            'urls_processed': urls_processed,
            'total_urls': total_urls,
            'timestamp': timestamp,
            'seen_phones': list(self.seen_phones)
        }

        progress_filename = f"phase2_progress_state_{timestamp}.json"
        with open(progress_filename, 'w') as f:
            json.dump(progress_data, f, indent=2)

        # Save checkpoint data
        checkpoint_filename = f"phase2_checkpoint_{urls_processed}_urls_{timestamp}.xlsx"
        if processed_results:
            df = pd.DataFrame(processed_results)
            column_order = [
                'url', 'phone_number', 'name', 'location', 'city', 'state',
                'age', 'social_media', 'raw_text', 'status', 'error', 'duplicate_phone', 'extracted_at'
            ]
            df = df.reindex(columns=column_order, fill_value='')

            with pd.ExcelWriter(checkpoint_filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Profile_Data', index=False)

        logger.info(f"Progress saved: {urls_processed}/{total_urls} URLs processed")
        return progress_filename, checkpoint_filename

    def load_progress(self) -> Tuple[int, Set[str]]:
        """Load previous progress if available"""
        # Find most recent progress file
        progress_files = [f for f in os.listdir('.') if f.startswith('phase2_progress_state_')]

        if not progress_files:
            return 0, set()

        latest_file = max(progress_files)

        try:
            with open(latest_file, 'r') as f:
                progress_data = json.load(f)

            urls_processed = progress_data.get('urls_processed', 0)
            seen_phones = set(progress_data.get('seen_phones', []))

            logger.info(f"Resuming from checkpoint: {urls_processed} URLs already processed")
            return urls_processed, seen_phones

        except Exception as e:
            logger.error(f"Error loading progress: {e}")
            return 0, set()

    def save_final_results(self, results: List[Dict], filename_prefix: str = "phase2_final"):
        """Save final results to Excel file"""
        if not results:
            logger.warning("No results to save")
            return None

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"{filename_prefix}_results_{timestamp}.xlsx"

        df = pd.DataFrame(results)

        # Reorder columns
        column_order = [
            'url', 'phone_number', 'name', 'location', 'city', 'state',
            'age', 'social_media', 'raw_text', 'status', 'error', 'duplicate_phone', 'extracted_at'
        ]
        df = df.reindex(columns=column_order, fill_value='')

        # Create Excel with multiple sheets
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # Main data sheet
            df.to_excel(writer, sheet_name='All_Profile_Data', index=False)

            # Summary sheet
            summary_data = {
                'Metric': [
                    'Total URLs Processed',
                    'Successful Extractions',
                    'Failed Extractions',
                    'Duplicate Phone Numbers',
                    'Unique Phone Numbers Found',
                    'Processing Start Time',
                    'Processing End Time',
                    'Total Processing Time'
                ],
                'Value': [
                    len(results),
                    len([r for r in results if r['status'] == 'success']),
                    len([r for r in results if r['status'] == 'failed']),
                    len([r for r in results if r['duplicate_phone']]),
                    len([r for r in results if r['phone_number'] and not r['duplicate_phone']]),
                    self.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    str(datetime.now() - self.start_time)
                ]
            }

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

        logger.info(f"Final results saved to: {output_file}")
        return output_file

    def phase2_extract_data(self, json_file: str) -> str:
        """Phase 2: Extract data from profile URLs"""
        logger.info("=" * 80)
        logger.info("PHASE 2: DATA EXTRACTION FROM PROFILE PAGES")
        logger.info("=" * 80)

        # Load URLs
        if not os.path.exists(json_file):
            logger.error(f"JSON file not found: {json_file}")
            return None

        with open(json_file, 'r') as f:
            urls_data = json.load(f)

        if not urls_data:
            logger.error("No URLs found in JSON file")
            return None

        logger.info(f"Loaded {len(urls_data)} URLs from {json_file}")

        # Load previous progress
        start_index, self.seen_phones = self.load_progress()

        # Process URLs
        all_results = []

        for i, url_data in enumerate(urls_data):
            if i < start_index:
                continue  # Skip already processed URLs

            result = self.extract_profile_data(url_data)
            all_results.append(result)

            # Save progress periodically
            if (i + 1) % self.checkpoint_interval == 0:
                self.save_progress(all_results, i + 1, len(urls_data))

            # Rate limiting
            time.sleep(self.delay)

        # Save final results
        output_file = self.save_final_results(all_results, "complete_phase2_final")

        # Print Phase 2 statistics
        logger.info("=" * 80)
        logger.info("PHASE 2 COMPLETE")
        logger.info("=" * 80)
        logger.info(f"Total URLs processed: {len(all_results)}")
        logger.info(f"Successful extractions: {len([r for r in all_results if r['status'] == 'success'])}")
        logger.info(f"Failed extractions: {len([r for r in all_results if r['status'] == 'failed'])}")
        logger.info(f"Duplicate phones: {len([r for r in all_results if r['duplicate_phone']])}")
        logger.info(f"Unique phones found: {len([r for r in all_results if r['phone_number'] and not r['duplicate_phone']])}")
        logger.info(f"Results saved to: {output_file}")

        return output_file

    def run_complete_workflow(self, max_pages: int = 15, custom_cities: List[str] = None,
                            phase2_only: bool = False, existing_json: str = None):
        """Run the complete two-phase workflow"""
        logger.info("🚀 STARTING COMPLETE TWO-PHASE SCRAPER")
        logger.info("=" * 80)

        urls_file = None

        # Phase 1: Extract URLs (unless skipped)
        if not phase2_only:
            urls_file = self.phase1_extract_urls(max_pages, custom_cities)
            if not urls_file:
                logger.error("Phase 1 failed - cannot proceed to Phase 2")
                return None
        else:
            # Use existing JSON file
            if existing_json and os.path.exists(existing_json):
                urls_file = existing_json
                logger.info(f"Skipping Phase 1 - Using existing file: {existing_json}")
            else:
                # Look for most recent phase1 file
                phase1_files = [f for f in os.listdir('.') if f.startswith('phase1_all_urls_')]
                if phase1_files:
                    urls_file = max(phase1_files)  # Most recent
                    logger.info(f"Using most recent Phase 1 file: {urls_file}")
                else:
                    logger.error("No existing URL file found for Phase 2")
                    return None

        # Phase 2: Extract data from URLs
        if urls_file:
            results_file = self.phase2_extract_data(urls_file)

            if results_file:
                logger.info("🎉 COMPLETE WORKFLOW FINISHED SUCCESSFULLY!")
                logger.info(f"📁 Final results: {results_file}")
                return results_file
            else:
                logger.error("Phase 2 failed")
                return None

        logger.error("Workflow failed")
        return None


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Complete Two-Phase Escort Profile Scraper')
    parser.add_argument('--max-pages', type=int, default=15,
                       help='Maximum pages to scrape per city/source (default: 15)')
    parser.add_argument('--cities', type=str,
                       help='Comma-separated list of cities to scrape (default: all)')
    parser.add_argument('--phase2-only', action='store_true',
                       help='Skip Phase 1, use existing URL file for Phase 2 only')
    parser.add_argument('--existing-json', type=str,
                       help='Specific JSON file to use for Phase 2 (when --phase2-only is used)')
    parser.add_argument('--delay', type=float, default=1.0,
                       help='Delay between requests in seconds (default: 1.0)')
    parser.add_argument('--workers', type=int, default=5,
                       help='Number of concurrent workers for Phase 2 (default: 5)')
    parser.add_argument('--checkpoint-interval', type=int, default=500,
                       help='Save progress every N URLs (default: 500)')

    args = parser.parse_args()

    # Parse cities if provided
    custom_cities = None
    if args.cities:
        custom_cities = [city.strip() for city in args.cities.split(',')]
        logger.info(f"Custom cities specified: {custom_cities}")

    # Initialize scraper (no ScraperAPI key needed)
    scraper = CompleteTwoPhaseScraper(
        delay=args.delay,
        max_workers=args.workers,
        checkpoint_interval=args.checkpoint_interval
    )

    try:
        # Run complete workflow
        result = scraper.run_complete_workflow(
            max_pages=args.max_pages,
            custom_cities=custom_cities,
            phase2_only=args.phase2_only,
            existing_json=args.existing_json
        )

        if result:
            logger.info(f"\n🎉 SCRAPING COMPLETED SUCCESSFULLY!")
            logger.info(f"📋 Final results saved to: {result}")
        else:
            logger.error(f"\n❌ SCRAPING FAILED")

    except KeyboardInterrupt:
        logger.info(f"\n🛑 Scraping interrupted by user")
    except Exception as e:
        logger.error(f"\n❌ Scraping failed with error: {e}")


if __name__ == "__main__":
    main()
