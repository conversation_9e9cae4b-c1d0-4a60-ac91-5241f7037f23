#!/usr/bin/env python3
"""
Test Anti-Bot Detection and Improvements
"""

import requests
import random
import time
from targeted_city_scraper import TargetedCityScraper

def test_direct_scrapingdog():
    """Test direct ScrapingDog request with anti-bot measures"""
    print("🧪 Testing Direct ScrapingDog Request")
    print("=" * 50)

    api_key = "68a38c83d2e5dbc2ff118e5c"
    test_url = "https://aaok.com.listcrawler.eu/post/escorts/usa/newjersey/southjersey/191793963"

    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    ]

    params = {
        'api_key': api_key,
        'url': test_url,
        'dynamic': 'true',
        'premium': 'true',
        'country': 'US',
        'render_wait': '3000',
    }

    headers = {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': 'https://google.com/',
    }

    print(f"📡 Making request to: {test_url}")
    print(f"🔑 Using API key: ...{api_key[-8:]}")

    try:
        response = requests.get("https://api.scrapingdog.com/scrape", params=params, headers=headers, timeout=60)

        print(f"📊 Status Code: {response.status_code}")
        print(f"📏 Response Length: {len(response.text)} characters")

        if response.status_code == 200:
            html = response.text

            # Check for abuse indicators
            abuse_indicators = [
                "report abuse",
                "× report abuse",
                "content/photos/phone scam/fake",
                "must authenticate your email",
                "escort south jersey | listing"
            ]

            found_abuse = []
            for indicator in abuse_indicators:
                if indicator.lower() in html.lower():
                    found_abuse.append(indicator)

            if found_abuse:
                print("❌ ABUSE PAGE DETECTED!")
                print(f"   Found indicators: {found_abuse}")
                print(f"   First 500 chars: {html[:500]}...")
            else:
                print("✅ REAL CONTENT DETECTED!")

                # Look for actual profile content
                profile_indicators = [
                    "description",
                    "contact",
                    "location",
                    "services",
                    "rates"
                ]

                found_content = []
                for indicator in profile_indicators:
                    if indicator.lower() in html.lower():
                        found_content.append(indicator)

                print(f"   Profile content found: {found_content}")
                print(f"   First 500 chars: {html[:500]}...")

        else:
            print(f"❌ Request failed: {response.text[:300]}...")

    except Exception as e:
        print(f"❌ Exception: {e}")

def test_scraper_antibot():
    """Test the improved scraper with anti-bot detection"""
    print("\n🧪 Testing Improved Scraper")
    print("=" * 50)

    try:
        scraper = TargetedCityScraper(
            scrapingdog_api_keys=["68a38c83d2e5dbc2ff118e5c"],
            max_urls_to_process=3  # Test with just 3 URLs
        )

        # Test URLs
        test_urls = [
            {
                "url": "https://aaok.com.listcrawler.eu/post/escorts/usa/newjersey/southjersey/191793963",
                "city": "South Jersey",
                "state": "New Jersey",
                "source": "aaok"
            },
            {
                "url": "https://aaok.com.listcrawler.eu/post/escorts/usa/newjersey/southjersey/191776195",
                "city": "South Jersey",
                "state": "New Jersey",
                "source": "aaok"
            },
            {
                "url": "https://aaok.com.listcrawler.eu/post/escorts/usa/newjersey/southjersey/191780166",
                "city": "South Jersey",
                "state": "New Jersey",
                "source": "aaok"
            }
        ]

        print(f"🎯 Testing {len(test_urls)} URLs")

        results = []
        for i, url_data in enumerate(test_urls, 1):
            print(f"\n📋 Testing URL {i}/{len(test_urls)}")
            print(f"   URL: {url_data['url']}")

            result = scraper.scrape_profile_url(url_data)

            if result:
                results.append(result)
                print(f"   ✅ Success: {result.get('name', 'No name')} - {result.get('phone', 'No phone')}")

                # Check if description looks like abuse page
                desc = result.get('description', '')
                if 'report abuse' in desc.lower() and len(desc) < 200:
                    print(f"   ⚠️ Still getting abuse page content")
                else:
                    print(f"   ✅ Real profile content detected")
            else:
                print(f"   ❌ Failed to scrape")

            # Small delay between tests
            time.sleep(2)

        print(f"\n📊 FINAL TEST RESULTS")
        print(f"   ✅ Successful scrapes: {len(results)}")
        print(f"   ❌ Failed scrapes: {len(test_urls) - len(results)}")

        # Show sample results
        if results:
            print(f"\n📄 SAMPLE RESULT:")
            sample = results[0]
            for key, value in sample.items():
                print(f"   {key}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")

    except Exception as e:
        print(f"❌ Scraper test failed: {e}")

def main():
    """Main test function"""
    print("🔍 Anti-Bot Detection Test Suite")
    print("=" * 60)

    # Test 1: Direct ScrapingDog request
    test_direct_scrapingdog()

    # Wait between tests
    print("\n⏳ Waiting 5 seconds before next test...")
    time.sleep(5)

    # Test 2: Improved scraper
    test_scraper_antibot()

    print(f"\n✅ All tests completed!")

if __name__ == "__main__":
    main()
