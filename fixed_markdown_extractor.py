#!/usr/bin/env python3
"""
Fixed markdown extractor that targets actual profile content
"""

import json
import re
from mistralai import Mistra<PERSON>
from nyc_boroughs_scraper import NYCBoro<PERSON>sScraper
from bs4 import BeautifulSoup

def extract_profile_markdown(html_content: str, url: str) -> str:
    """Extract markdown focusing on actual profile content areas"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Target specific profile content areas based on the HTML structure
        profile_data = {}
        
        # Extract title and age
        title_elem = soup.find('div', class_='viewposttitle')
        if title_elem:
            # Get title text
            title_text = title_elem.get_text().strip()
            profile_data['title'] = title_text
            
            # Extract age from title
            age_elem = title_elem.find('span', class_='postTitleAge')
            if age_elem:
                profile_data['age'] = age_elem.get_text().strip()
        
        # Extract name
        name_elem = soup.find('div', class_='viewpostname')
        if name_elem:
            name_text = name_elem.get_text().strip()
            # Remove "Nym:" prefix if present
            name_text = re.sub(r'^Nym:\s*', '', name_text)
            profile_data['name'] = name_text
        
        # Extract gender
        gender_elem = soup.find('div', class_='i-am')
        if gender_elem:
            gender_value = gender_elem.find('span', class_='iamisee__value')
            if gender_value:
                profile_data['gender'] = gender_value.get_text().strip()
        
        # Extract description/body
        body_elem = soup.find('div', class_='viewpostbody')
        if body_elem:
            profile_data['description'] = body_elem.get_text().strip()
        
        # Extract phone number
        phone_elem = soup.find('a', href=re.compile(r'^tel:'))
        if phone_elem:
            phone_href = phone_elem.get('href', '')
            phone_number = phone_href.replace('tel:', '')
            profile_data['phone'] = phone_number
        
        # Extract any social media links or additional info
        # Look for common social media patterns in the text
        all_text = soup.get_text()
        social_media_patterns = [
            r'instagram[:\s]*[@]?[\w\.]+',
            r'snapchat[:\s]*[@]?[\w\.]+',
            r'twitter[:\s]*[@]?[\w\.]+',
            r'onlyfans[:\s]*[@]?[\w\.]+',
            r'@[\w\.]+',
            r'snap[:\s]*[@]?[\w\.]+',
            r'ig[:\s]*[@]?[\w\.]+',
        ]
        
        social_media_found = []
        for pattern in social_media_patterns:
            matches = re.findall(pattern, all_text, re.IGNORECASE)
            social_media_found.extend(matches)
        
        if social_media_found:
            profile_data['social_media'] = ', '.join(set(social_media_found))
        
        # Extract post ID from URL
        post_id_match = re.search(r'/(\d+)$', url)
        if post_id_match:
            profile_data['post_id'] = post_id_match.group(1)
        
        # Create structured markdown with extracted data
        markdown_content = f"""# Profile: {profile_data.get('title', 'Unknown')}
**URL:** {url}
**Post ID:** {profile_data.get('post_id', 'Unknown')}

## Profile Information:
**Name:** {profile_data.get('name', 'Not specified')}
**Age:** {profile_data.get('age', 'Not specified')}
**Gender:** {profile_data.get('gender', 'Not specified')}
**Phone:** {profile_data.get('phone', 'Not specified')}

## Description:
{profile_data.get('description', 'No description available')}

## Social Media:
{profile_data.get('social_media', 'None found')}

---
"""
        
        return markdown_content
        
    except Exception as e:
        print(f"Error extracting profile data: {e}")
        return f"""# Profile Page
**URL:** {url}

## Content:
[Failed to extract profile content]

---
"""

def test_fixed_extraction():
    """Test the fixed profile extraction"""
    
    # Use confirmed working URL
    test_url = "https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/manhattanny/191403342"
    
    print("Testing Fixed Profile Extraction")
    print("=" * 50)
    print(f"Test URL: {test_url}")
    
    # Get HTML
    scraper = NYCBoroughsScraper("TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G")
    html = scraper.execute_curl_request(test_url, scraper.dedicated_curl_template)
    
    if not html:
        print("❌ Failed to get HTML")
        return
    
    print(f"✓ Downloaded {len(html)} characters of HTML")
    
    # Extract with fixed method
    markdown = extract_profile_markdown(html, test_url)
    
    print(f"✓ Extracted profile markdown ({len(markdown)} characters)")
    print("\nExtracted Profile Data:")
    print("=" * 50)
    print(markdown)
    print("=" * 50)
    
    # Test with Mistral
    print("\nTesting with Mistral...")
    
    client = Mistral(api_key="TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G")
    
    prompt = f"""
    You are extracting data from 1 escort profile page. Extract the following information:

    1. title: Profile title or headline
    2. name: Person's name
    3. age: Age (must be ≤30, skip if >30)
    4. phone: Phone number
    5. description: Profile description
    6. social_media: Social media handles/links (Instagram, Twitter, Snapchat, OnlyFans, etc.)
    7. email: Email address
    8. website: Website links
    9. posted_date: When the post was created
    10. post_id: Unique post identifier

    IMPORTANT: Only include profiles where age ≤30 AND gender is woman. Skip any profiles with age >30 or not women.

    Page content:

    {markdown}

    
    Return a JSON array with one object per valid page (only for women with age ≤30). Each object should have:
    {{
        "page_index": 0,
        "url": "{test_url}",
        "title": "<title>",
        "name": "<name>", 
        "age": "<age>",
        "phone": "<phone>",
        "description": "<description>",
        "social_media": "<social_media>",
        "email": "<email>",
        "website": "<website>",
        "posted_date": "<posted_date>",
        "post_id": "<post_id>"
    }}
    
    If any field is not found, use null. Only return valid JSON array.
    """
    
    try:
        response = client.chat.complete(
            model="mistral-large-latest",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=1500
        )
        
        result_text = response.choices[0].message.content.strip()
        
        print("=" * 60)
        print("MISTRAL RESPONSE:")
        print("=" * 60)
        print(result_text)
        print("=" * 60)
        
        # Try to parse
        try:
            # Try direct parsing first
            parsed_json = json.loads(result_text)
            print("✓ DIRECT JSON PARSING SUCCESSFUL!")
            print(f"Found {len(parsed_json)} profiles")
            
        except json.JSONDecodeError:
            # Try fallback parsing
            json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
            if json_match:
                try:
                    parsed_json = json.loads(json_match.group())
                    print("✓ FALLBACK JSON PARSING SUCCESSFUL!")
                    print(f"Found {len(parsed_json)} profiles")
                    
                except json.JSONDecodeError as e2:
                    print("✗ BOTH PARSING METHODS FAILED!")
                    print(f"Error: {e2}")
                    return
            else:
                print("✗ NO JSON FOUND IN RESPONSE!")
                return
        
        # Show results
        if isinstance(parsed_json, list) and len(parsed_json) > 0:
            print("\n🎯 SUCCESS! Found profile data:")
            for profile in parsed_json:
                print(f"  - Name: {profile.get('name', 'N/A')}")
                print(f"  - Age: {profile.get('age', 'N/A')}")
                print(f"  - Phone: {profile.get('phone', 'N/A')}")
                print(f"  - Social Media: {profile.get('social_media', 'N/A')}")
                print(f"  - Description: {profile.get('description', 'N/A')[:100]}...")
            
            print("\n🎉 PROBLEM SOLVED! The fixed extraction works!")
        else:
            print("❌ Still getting empty results")
        
    except Exception as e:
        print(f"❌ API call failed: {e}")

if __name__ == "__main__":
    test_fixed_extraction()
