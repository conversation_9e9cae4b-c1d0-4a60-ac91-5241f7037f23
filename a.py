import pandas as pd
import requests
import json
import time
from typing import List, Dict
import os

class SocialMediaExtractor:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.mistral.ai/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # Social media platforms to detect
        self.platforms = [
            "Instagram", "X", "Twitter", "OnlyFans", "OF",
            "Snapchat", "Video Cam", "Media", "TikTok",
            "YouTube", "Facebook", "LinkedIn"
        ]

    def create_prompt(self, descriptions: List[str]) -> str:
        """Create a prompt for the Mistral AI model to extract social media information."""
        prompt = f"""
You are a social media content analyzer. For each description provided, identify if it mentions any social media platforms or related activities.

Look for mentions of: Instagram, X (formerly Twitter), Twitter, OnlyFans, OF, Snapchat, Video Cam, Media, TikTok, YouTube, Facebook, LinkedIn, webcam, streaming, content creation, social media influencer, cam girl, cam boy, live streaming, video chat, etc.

IMPORTANT: For each description, return ONLY the social media platforms found (comma-separated) or "None" if no social media is mentioned.

You must return your response as a valid JSON array with exactly one result per description, in the same order as provided.

Descriptions to analyze:
{json.dumps(descriptions, indent=2)}

Return ONLY a JSON array like this example:
["Instagram, TikTok", "OnlyFans", "None", "Twitter, YouTube"]

Do not include any other text, explanations, or markdown formatting. Just the JSON array.
"""
        return prompt

    def call_mistral_api(self, prompt: str) -> str:
        """Make API call to Mistral AI."""
        payload = {
            "model": "mistral-large-latest",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 1000
        }

        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                print(f"API Error: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            return None

    def parse_api_response(self, response: str, batch_size: int) -> List[str]:
        """Parse the API response and extract social media information."""
        try:
            print(f"Raw API response: {response}")

            # Clean the response
            response = response.strip()

            # Try to parse as JSON first
            if response.startswith('[') and response.endswith(']'):
                results = json.loads(response)
                print(f"Parsed JSON results: {results}")
                return results
            else:
                # Try to extract JSON from markdown code block
                if '```json' in response:
                    json_start = response.find('```json') + 7
                    json_end = response.find('```', json_start)
                    json_str = response[json_start:json_end].strip()
                    results = json.loads(json_str)
                    print(f"Extracted JSON from markdown: {results}")
                    return results

                # If not JSON, try to extract from text lines
                lines = response.strip().split('\n')
                results = []
                for line in lines:
                    line = line.strip().strip('"').strip("'").strip(',')
                    if line and not line.startswith('[') and not line.startswith(']') and line != '```json' and line != '```':
                        results.append(line)

                print(f"Extracted from lines: {results}")

                # Ensure we have the right number of results
                while len(results) < batch_size:
                    results.append("None")

                return results[:batch_size]

        except Exception as e:
            print(f"Error parsing response: {e}")
            print(f"Response was: {response}")
            # Return "None" instead of "Error" for parsing failures
            return ["None"] * batch_size

    def process_batch(self, descriptions: List[str]) -> List[str]:
        """Process a batch of descriptions."""
        # Clean descriptions and handle NaN values
        clean_descriptions = []
        for desc in descriptions:
            if pd.isna(desc) or desc == "" or str(desc).lower() == 'nan':
                clean_descriptions.append("No description provided")
            else:
                clean_descriptions.append(str(desc))

        prompt = self.create_prompt(clean_descriptions)
        response = self.call_mistral_api(prompt)

        if response:
            return self.parse_api_response(response, len(descriptions))
        else:
            print("API call failed, returning None for all descriptions")
            return ["None"] * len(descriptions)

    def process_excel_file(self, input_file: str, output_file: str, batch_size: int = 20):
        """Process the entire Excel file."""
        try:
            # Read Excel file
            print(f"Reading Excel file: {input_file}")
            df = pd.read_excel(input_file)

            # Check if 'description' column exists
            if 'description' not in df.columns:
                print("Error: 'description' column not found in the Excel file.")
                print(f"Available columns: {list(df.columns)}")
                return

            # Initialize social media column
            df['social'] = ""

            total_rows = len(df)
            print(f"Processing {total_rows} rows in batches of {batch_size}")

            # Process in batches
            for i in range(0, total_rows, batch_size):
                end_idx = min(i + batch_size, total_rows)
                batch_descriptions = df['description'].iloc[i:end_idx].tolist()
                actual_batch_size = len(batch_descriptions)

                print(f"Processing batch {i//batch_size + 1}: rows {i+1} to {end_idx} ({actual_batch_size} descriptions)")

                # Process batch
                batch_results = self.process_batch(batch_descriptions)

                # Ensure we have the right number of results
                if len(batch_results) != actual_batch_size:
                    print(f"Warning: Expected {actual_batch_size} results, got {len(batch_results)}")
                    # Pad or trim results to match batch size
                    while len(batch_results) < actual_batch_size:
                        batch_results.append("None")
                    batch_results = batch_results[:actual_batch_size]

                # Update dataframe
                df.loc[i:i+actual_batch_size-1, 'social'] = batch_results

                # Add delay to avoid rate limiting
                if i + batch_size < total_rows:
                    time.sleep(2)

                print(f"Completed batch {i//batch_size + 1}")

            # Save to output file
            print(f"Saving results to: {output_file}")
            df.to_excel(output_file, index=False)
            print("Processing completed successfully!")

            # Show summary
            non_empty_social = df[(df['social'].notna()) &
                                (df['social'] != "") &
                                (df['social'] != "None") &
                                (df['social'] != "Error")]['social'].count()
            error_count = df[df['social'] == "Error"]['social'].count()
            none_count = df[df['social'] == "None"]['social'].count()

            print(f"\nSummary:")
            print(f"Total rows processed: {total_rows}")
            print(f"Rows with social media found: {non_empty_social}")
            print(f"Rows with no social media: {none_count}")
            print(f"Rows with errors: {error_count}")

        except Exception as e:
            print(f"Error processing Excel file: {e}")

def main():
    # Configuration
    API_KEY = "nsgxaPTwCJ3jc69RjD7RaQrNuq3l9O6z"
    INPUT_FILE = "scraperapi_results_18731_urls_20250812_014155_final_deduplicated_20250812_020840_processed.xlsx"
    OUTPUT_FILE = "scraperapi_results_18731_urls_20250812_014155_final_deduplicated_processed_with_social.xlsx"
    BATCH_SIZE = 20

    # Validate input file exists
    if not os.path.exists(INPUT_FILE):
        print(f"Error: Input file '{INPUT_FILE}' not found.")
        print("Please update the INPUT_FILE variable with the correct path to your Excel file.")
        return

    # Initialize extractor
    extractor = SocialMediaExtractor(API_KEY)

    # Process file
    extractor.process_excel_file(INPUT_FILE, OUTPUT_FILE, BATCH_SIZE)

if __name__ == "__main__":
    main()
