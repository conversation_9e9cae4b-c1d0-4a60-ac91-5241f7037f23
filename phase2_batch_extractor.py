#!/usr/bin/env python3
"""
Phase2 Batch Files Extractor

This script processes all phase2 batch files from phase2_batch_01_500_urls.json
to phase2_batch_18_315_urls.json with the following features:
- Processes all 18 phase2 batch files in sequence
- Saves progress after each batch file
- Skips URLs with duplicate phone numbers across all batches
- Resumes from last completed batch if interrupted
- Extracts: Phone Number, Name, Location, City, Social Media, Age, Raw Text

Usage:
    python phase2_batch_extractor.py
"""

import json
import requests
import pandas as pd
import re
import time
import logging
import os
from datetime import datetime
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any, Set
import concurrent.futures
from threading import Lock
import glob

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('phase2_batch_extractor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Phase2BatchExtractor:
    """Extractor for all phase2 batch files with deduplication"""

    def __init__(self, delay: float = 1.0, max_workers: int = 5):
        self.delay = delay
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        self.all_results = []
        self.seen_phones: Set[str] = set()
        self.start_time = datetime.now()
        self.processed_batches = set()
        self.lock = Lock()

    def normalize_phone(self, phone: str) -> str:
        """Normalize phone number for comparison"""
        if not phone:
            return ""
        digits_only = re.sub(r'\D', '', phone)
        if len(digits_only) > 10:
            return digits_only[-10:]
        return digits_only

    def format_phone(self, phone: str) -> str:
        """Format phone number as 1xxxxxxxxxx"""
        if not phone:
            return ""
        digits_only = re.sub(r'\D', '', phone)

        # If less than 10 digits, return original
        if len(digits_only) < 10:
            return phone

        # Take last 10 digits if more than 10
        if len(digits_only) > 10:
            digits_only = digits_only[-10:]

        # Format as 1 + 10 digits (total 11 digits)
        return "1" + digits_only

    def is_phone_duplicate(self, phone: str) -> bool:
        """Check if phone number has already been processed"""
        if not phone:
            return False
        # For formatted phone numbers (1xxxxxxxxxx), use the full formatted number
        if phone.startswith('1') and len(phone) == 11:
            check_phone = phone
        else:
            # For unformatted phones, normalize to 10 digits
            check_phone = self.normalize_phone(phone)

        if check_phone in self.seen_phones:
            return True
        self.seen_phones.add(check_phone)
        return False

    def extract_phone_number(self, soup: BeautifulSoup, html_content: str) -> Optional[str]:
        """Extract phone number from various possible locations"""
        # Method 1: Look for tel: links
        tel_link = soup.find('a', href=re.compile(r'tel:'))
        if tel_link:
            phone = tel_link.get_text(strip=True)
            if phone and re.search(r'\d{3}[-.]?\d{3}[-.]?\d{4}', phone):
                return phone

        # Method 2: Look in viewposttelephone class
        phone_div = soup.find('div', class_='viewposttelephone')
        if phone_div:
            phone_text = phone_div.get_text(strip=True)
            phone_match = re.search(r'(\d{3}[-.]?\d{3}[-.]?\d{4})', phone_text)
            if phone_match:
                return phone_match.group(1)

        # Method 3: Look for phone patterns in the entire content
        phone_patterns = [
            r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})',
            r'\((\d{3})\)[-.\s]?(\d{3})[-.\s]?(\d{4})',
            r'(\d{10})'
        ]

        for pattern in phone_patterns:
            matches = re.findall(pattern, html_content)
            if matches:
                if isinstance(matches[0], tuple):
                    return ''.join(matches[0])
                return matches[0]

        return None

    def extract_name(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract name from the profile"""
        # Method 1: Look for Nym field
        nym_span = soup.find('span', string=re.compile(r'Nym:'))
        if nym_span and nym_span.next_sibling:
            name = str(nym_span.next_sibling).strip()
            if name and name != 'None':
                return name

        # Method 2: Look in viewpostname class
        name_div = soup.find('div', class_='viewpostname')
        if name_div:
            text = name_div.get_text()
            nym_match = re.search(r'Nym:\s*(.+?)(?:\s|$)', text)
            if nym_match:
                return nym_match.group(1).strip()

        # Method 3: Look in post title
        title_div = soup.find('div', class_='viewposttitle')
        if title_div:
            title_text = title_div.get_text(strip=True)
            name_match = re.search(r'([A-Za-z]+)', title_text)
            if name_match:
                return name_match.group(1)

        return None

    def extract_location(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract location from the profile"""
        # Method 1: Look for Location field
        location_spans = soup.find_all('span', class_='postContentBold')
        for span in location_spans:
            if 'Location:' in span.get_text():
                next_text = span.next_sibling
                if next_text:
                    return str(next_text).strip()

        # Method 2: Look in location list items
        location_lis = soup.find_all('li')
        for li in location_lis:
            li_text = li.get_text()
            if 'Location:' in li_text:
                location_match = re.search(r'Location:\s*(.+)', li_text)
                if location_match:
                    return location_match.group(1).strip()

        return None

    def extract_age(self, soup: BeautifulSoup) -> Optional[int]:
        """Extract age from the profile"""
        # Method 1: Look for Age field in spans
        age_spans = soup.find_all('span', class_='postContentBold')
        for span in age_spans:
            if 'Age:' in span.get_text():
                next_text = span.next_sibling
                if next_text:
                    age_match = re.search(r'(\d+)', str(next_text))
                    if age_match:
                        return int(age_match.group(1))

        # Method 2: Look in age list items
        age_lis = soup.find_all('li')
        for li in age_lis:
            li_text = li.get_text()
            if 'Age:' in li_text:
                age_match = re.search(r'Age:\s*(\d+)', li_text)
                if age_match:
                    return int(age_match.group(1))

        # Method 3: Look in post title age span
        age_span = soup.find('span', class_='postTitleAge')
        if age_span:
            age_match = re.search(r'(\d+)', age_span.get_text())
            if age_match:
                return int(age_match.group(1))

        return None

    def extract_social_media(self, soup: BeautifulSoup, html_content: str) -> Optional[str]:
        """Extract social media information"""
        social_platforms = ['Instagram', 'Onlyfans', 'Twitter', 'Facebook', 'Snapchat', 'TikTok', 'OnlyFans']
        social_info = []

        for platform in social_platforms:
            pattern = rf'{platform}[:\s]+([A-Za-z0-9_\.]+)'
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if len(match) > 2:
                    social_info.append(f"{platform}: {match}")

        return '; '.join(social_info) if social_info else None

    def extract_raw_text(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract the main description text"""
        body_div = soup.find('div', class_='viewpostbody')
        if body_div:
            text = body_div.get_text(separator=' ', strip=True)
            text = re.sub(r'\s+', ' ', text)
            return text

        content_selectors = [
            'div.post-content',
            'div.content',
            'div.description',
            'div.post-body'
        ]

        for selector in content_selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text(separator=' ', strip=True)
                text = re.sub(r'\s+', ' ', text)
                return text

        return None

    def extract_profile_data(self, url: str, city: str = None, state: str = None) -> Dict[str, Any]:
        """Extract all profile data from a single URL"""
        result = {
            'url': url,
            'phone_number': None,
            'name': None,
            'location': None,
            'city': city,
            'state': state,
            'social_media': None,
            'age': None,
            'raw_text': None,
            'status': 'failed',
            'error': None,
            'extracted_at': datetime.now().isoformat(),
            'duplicate_phone': False
        }

        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')
            html_content = response.text

            # Extract phone number first
            phone = self.extract_phone_number(soup, html_content)

            # Format phone number
            formatted_phone = self.format_phone(phone) if phone else None

            # Check for duplicate phone using formatted number
            if formatted_phone and self.is_phone_duplicate(formatted_phone):
                result['phone_number'] = formatted_phone
                result['duplicate_phone'] = True
                result['status'] = 'skipped_duplicate'
                result['error'] = 'Duplicate phone number'
                logger.info(f"Skipped duplicate phone {formatted_phone} for {url}")
                return result

            # Extract all other fields
            result['phone_number'] = formatted_phone
            result['name'] = self.extract_name(soup)
            result['location'] = self.extract_location(soup)
            result['age'] = self.extract_age(soup)
            result['social_media'] = self.extract_social_media(soup, html_content)
            result['raw_text'] = self.extract_raw_text(soup)

            result['status'] = 'success'
            logger.info(f"Successfully extracted data from {url} - Phone: {formatted_phone}")

        except requests.exceptions.RequestException as e:
            result['error'] = f"Request error: {str(e)}"
            logger.error(f"Request error for {url}: {e}")
        except Exception as e:
            result['error'] = f"Parsing error: {str(e)}"
            logger.error(f"Parsing error for {url}: {e}")

        return result

    def load_batch_file(self, batch_file: str) -> List[Dict]:
        """Load URLs from a batch file"""
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if isinstance(data, list):
                return data
            else:
                logger.error(f"Unexpected JSON structure in {batch_file}")
                return []

        except Exception as e:
            logger.error(f"Error loading {batch_file}: {e}")
            return []

    def process_batch_file(self, batch_file: str) -> List[Dict]:
        """Process a single batch file"""
        logger.info(f"Processing batch file: {batch_file}")

        # Load URLs from batch file
        urls_data = self.load_batch_file(batch_file)
        if not urls_data:
            logger.error(f"No URLs found in {batch_file}")
            return []

        logger.info(f"Loaded {len(urls_data)} URLs from {batch_file}")

        # Process URLs with threading
        batch_results = self.process_urls_with_threading(urls_data)

        # Save batch results
        self.save_batch_results(batch_file, batch_results)

        return batch_results

    def process_urls_with_threading(self, urls_data: List[Dict]) -> List[Dict]:
        """Process URLs with threading"""
        batch_results = []

        # Split into sub-batches for workers
        batch_size = max(1, len(urls_data) // self.max_workers)
        sub_batches = [urls_data[i:i + batch_size] for i in range(0, len(urls_data), batch_size)]

        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_batch = {
                executor.submit(self.process_sub_batch, sub_batch): sub_batch
                for sub_batch in sub_batches
            }

            for future in concurrent.futures.as_completed(future_to_batch):
                try:
                    sub_batch_results = future.result()
                    batch_results.extend(sub_batch_results)
                except Exception as e:
                    logger.error(f"Sub-batch processing error: {e}")

        return batch_results

    def process_sub_batch(self, urls_batch: List[Dict]) -> List[Dict]:
        """Process a sub-batch of URLs"""
        sub_batch_results = []

        for url_data in urls_batch:
            url = url_data.get('url')
            city = url_data.get('city')
            state = url_data.get('state')

            result = self.extract_profile_data(url, city, state)
            sub_batch_results.append(result)

            time.sleep(self.delay)

        return sub_batch_results

    def save_batch_results(self, batch_file: str, batch_results: List[Dict]):
        """Save results for a completed batch"""
        batch_name = os.path.splitext(os.path.basename(batch_file))[0]
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"{batch_name}_results_{timestamp}.xlsx"

        df = pd.DataFrame(batch_results)
        column_order = [
            'url', 'phone_number', 'name', 'location', 'city', 'state',
            'age', 'social_media', 'raw_text', 'status', 'error', 'duplicate_phone', 'extracted_at'
        ]
        available_columns = [col for col in column_order if col in df.columns]
        df = df[available_columns]

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Profile_Data', index=False)

        # Log batch summary
        successful = sum(1 for r in batch_results if r['status'] == 'success')
        skipped = sum(1 for r in batch_results if r['status'] == 'skipped_duplicate')
        failed = len(batch_results) - successful - skipped

        logger.info(f"Batch {batch_name} completed:")
        logger.info(f"  Total: {len(batch_results)}")
        logger.info(f"  Successful: {successful}")
        logger.info(f"  Skipped (duplicates): {skipped}")
        logger.info(f"  Failed: {failed}")
        logger.info(f"  Output: {output_file}")

    def get_phase2_batch_files(self) -> List[str]:
        """Get all phase2 batch files in order"""
        batch_files = []
        for i in range(1, 19):  # batch_01 to batch_18
            if i == 18:
                filename = f"phase2_batch_{i:02d}_315_urls.json"
            else:
                filename = f"phase2_batch_{i:02d}_500_urls.json"

            if os.path.exists(filename):
                batch_files.append(filename)
            else:
                logger.warning(f"Batch file not found: {filename}")

        return batch_files

    def load_progress(self) -> Set[str]:
        """Load progress from previous run"""
        progress_file = "phase2_extraction_progress.json"
        if os.path.exists(progress_file):
            try:
                with open(progress_file, 'r') as f:
                    data = json.load(f)

                self.seen_phones = set(data.get('seen_phones', []))
                completed_batches = set(data.get('completed_batches', []))

                logger.info(f"Loaded progress: {len(completed_batches)} completed batches")
                logger.info(f"Loaded {len(self.seen_phones)} seen phone numbers")

                return completed_batches
            except Exception as e:
                logger.error(f"Error loading progress: {e}")

        return set()

    def save_progress(self, completed_batches: Set[str]):
        """Save current progress"""
        progress_data = {
            'completed_batches': list(completed_batches),
            'seen_phones': list(self.seen_phones),
            'timestamp': datetime.now().isoformat()
        }

        with open("phase2_extraction_progress.json", 'w') as f:
            json.dump(progress_data, f, indent=2)

    def process_all_batches(self):
        """Process all phase2 batch files"""
        logger.info("=== Phase2 Batch Extraction Started ===")

        # Get all batch files
        batch_files = self.get_phase2_batch_files()
        if not batch_files:
            logger.error("No phase2 batch files found")
            return

        logger.info(f"Found {len(batch_files)} batch files to process")

        # Load progress
        completed_batches = self.load_progress()

        # Process each batch file
        total_results = []
        for i, batch_file in enumerate(batch_files, 1):
            batch_name = os.path.splitext(os.path.basename(batch_file))[0]

            if batch_name in completed_batches:
                logger.info(f"Skipping already completed batch: {batch_file}")
                continue

            logger.info(f"Processing batch {i}/{len(batch_files)}: {batch_file}")

            try:
                batch_results = self.process_batch_file(batch_file)
                total_results.extend(batch_results)

                # Mark batch as completed
                completed_batches.add(batch_name)
                self.save_progress(completed_batches)

                logger.info(f"Completed batch {i}/{len(batch_files)}")

            except Exception as e:
                logger.error(f"Error processing {batch_file}: {e}")
                continue

        # Save final combined results
        if total_results:
            self.save_final_results(total_results)

        # Final summary
        successful = sum(1 for r in total_results if r['status'] == 'success')
        skipped = sum(1 for r in total_results if r['status'] == 'skipped_duplicate')
        failed = len(total_results) - successful - skipped

        logger.info("=== Phase2 Batch Extraction Complete ===")
        logger.info(f"Total URLs processed: {len(total_results):,}")
        logger.info(f"Successful extractions: {successful:,}")
        logger.info(f"Skipped (duplicate phones): {skipped:,}")
        logger.info(f"Failed extractions: {failed:,}")
        logger.info(f"Unique phone numbers: {len(self.seen_phones):,}")
        logger.info(f"Success rate: {successful/len(total_results)*100:.1f}%")
        logger.info(f"Total processing time: {datetime.now() - self.start_time}")

    def save_final_results(self, all_results: List[Dict]):
        """Save final combined results"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"phase2_all_batches_FINAL_results_{timestamp}.xlsx"

        df = pd.DataFrame(all_results)
        column_order = [
            'url', 'phone_number', 'name', 'location', 'city', 'state',
            'age', 'social_media', 'raw_text', 'status', 'error', 'duplicate_phone', 'extracted_at'
        ]
        available_columns = [col for col in column_order if col in df.columns]
        df = df[available_columns]

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='All_Profile_Data', index=False)

            # Create summary sheet
            summary_data = {
                'Metric': [
                    'Total URLs Processed',
                    'Successful Extractions',
                    'Skipped (Duplicate Phone)',
                    'Failed Extractions',
                    'Unique Phone Numbers',
                    'Success Rate (%)',
                    'Processing Start Time',
                    'Processing End Time',
                    'Total Processing Time'
                ],
                'Value': [
                    len(all_results),
                    sum(1 for r in all_results if r['status'] == 'success'),
                    sum(1 for r in all_results if r['status'] == 'skipped_duplicate'),
                    sum(1 for r in all_results if r['status'] == 'failed'),
                    len(self.seen_phones),
                    f"{sum(1 for r in all_results if r['status'] == 'success') / len(all_results) * 100:.2f}",
                    self.start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    str(datetime.now() - self.start_time)
                ]
            }

            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

        logger.info(f"Final combined results saved to: {output_file}")

def main():
    """Main function"""
    print("=" * 80)
    print("PHASE2 BATCH FILES EXTRACTOR")
    print("=" * 80)
    print("Processing files: phase2_batch_01_500_urls.json to phase2_batch_18_315_urls.json")
    print("")

    # Initialize extractor
    extractor = Phase2BatchExtractor(
        delay=1.0,  # 1 second delay between requests
        max_workers=5  # 5 concurrent workers
    )

    # Process all batches
    extractor.process_all_batches()

if __name__ == '__main__':
    main()
