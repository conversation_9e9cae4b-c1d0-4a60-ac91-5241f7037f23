#!/usr/bin/env python3
"""
Stealthy Deduplication Scraper
A stealth web scraper with phone number deduplication that uses direct requests without ScraperAPI
"""

import requests
import time
import random
import threading
import json
import os
import pandas as pd
import re
from datetime import datetime
from typing import List, Dict, Optional, Set
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
import urllib.parse
from fake_useragent import UserAgent
import warnings
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

class StealthyDeduplicationScraper:
    def __init__(self, max_workers: int = 2, enable_social_media: bool = False):
        self.max_workers = max_workers
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results = []
        self.results_lock = threading.Lock()
        self.processed_count = 0
        self.skipped_count = 0

        # Phone number deduplication
        self.scraped_phone_numbers: Set[str] = set()
        self.phone_lock = threading.Lock()

        # Stealth settings
        self.ua = UserAgent()
        self.session_pool = {}
        self.proxy_list = []  # Add proxies here if needed

        # Rate limiting for stealth
        self.min_delay = 2.0  # Minimum delay between requests
        self.max_delay = 5.0  # Maximum delay between requests
        self.last_request_time = {}  # Track last request time per worker

        # Social media extraction (disabled by default for stealth)
        self.enable_social_media = enable_social_media

        # Load existing phone numbers from previous results
        self.load_existing_phone_numbers()

    def load_existing_phone_numbers(self):
        """Load phone numbers from existing result files to avoid duplicates"""
        print("🔍 Loading existing phone numbers from previous results...")

        # Look for existing Excel files with scraped results
        result_files = []
        for f in os.listdir('.'):
            if (f.startswith(('scraperapi_results_', 'enhanced_', 'manual_extraction_', 'final_', 'stealthy_'))
                and f.endswith('.xlsx')):
                result_files.append(f)

        total_loaded = 0
        for file_path in result_files:
            try:
                print(f"📂 Checking {file_path}...")
                df = pd.read_excel(file_path)

                if 'phone' in df.columns:
                    # Get valid phone numbers (not empty, not NaN)
                    valid_phones = df[df['phone'].notna() & (df['phone'] != '') & (df['phone'] != 'nan')]['phone']
                    new_phones = set(valid_phones.astype(str))

                    # Clean phone numbers (remove formatting)
                    cleaned_phones = set()
                    for phone in new_phones:
                        cleaned_phone = self.clean_phone_number(phone)
                        if cleaned_phone:
                            cleaned_phones.add(cleaned_phone)

                    # Add to our set
                    before_count = len(self.scraped_phone_numbers)
                    self.scraped_phone_numbers.update(cleaned_phones)
                    added_count = len(self.scraped_phone_numbers) - before_count

                    print(f"   📱 Found {len(new_phones)} phones, added {added_count} new unique numbers")
                    total_loaded += added_count

            except Exception as e:
                print(f"   ⚠️ Error loading {file_path}: {e}")
                continue

        print(f"✅ Loaded {total_loaded} existing phone numbers from {len(result_files)} files")
        print(f"📊 Total unique phone numbers to skip: {len(self.scraped_phone_numbers)}")

    def clean_phone_number(self, phone_str: str) -> Optional[str]:
        """Clean phone number for consistent comparison"""
        if not phone_str or pd.isna(phone_str):
            return None

        # Convert to string and remove all non-digits
        phone_clean = re.sub(r'[^\d]', '', str(phone_str))

        # Must be at least 10 digits for US phone numbers
        if len(phone_clean) < 10:
            return None

        # Normalize to 10 digits (remove country code if present)
        if len(phone_clean) == 11 and phone_clean.startswith('1'):
            phone_clean = phone_clean[1:]
        elif len(phone_clean) > 11:
            return None  # Invalid length

        return phone_clean

    def is_phone_already_scraped(self, phone: str) -> bool:
        """Check if phone number has already been scraped"""
        cleaned_phone = self.clean_phone_number(phone)
        if not cleaned_phone:
            return False

        with self.phone_lock:
            return cleaned_phone in self.scraped_phone_numbers

    def add_phone_to_scraped(self, phone: str):
        """Add phone number to scraped set"""
        cleaned_phone = self.clean_phone_number(phone)
        if cleaned_phone:
            with self.phone_lock:
                self.scraped_phone_numbers.add(cleaned_phone)

    def get_stealth_session(self, worker_id: str) -> requests.Session:
        """Get a stealth session for the worker"""
        if worker_id not in self.session_pool:
            session = requests.Session()

            # Set random user agent
            session.headers.update({
                'User-Agent': self.ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
            })

            # Disable SSL verification for problematic sites
            session.verify = False

            # Set timeout
            session.timeout = 30

            self.session_pool[worker_id] = session

        return self.session_pool[worker_id]

    def fetch_with_stealth(self, url: str, worker_id: str, quick_check: bool = False) -> Optional[str]:
        """Fetch URL using stealth techniques"""

        # Rate limiting per worker
        current_time = time.time()
        if worker_id in self.last_request_time:
            time_since_last = current_time - self.last_request_time[worker_id]
            min_interval = random.uniform(self.min_delay, self.max_delay)
            if quick_check:
                min_interval = random.uniform(1.0, 2.0)  # Faster for quick checks

            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                time.sleep(sleep_time)

        self.last_request_time[worker_id] = time.time()

        max_retries = 3
        for attempt in range(max_retries):
            try:
                session = self.get_stealth_session(worker_id)

                # Rotate user agent occasionally
                if random.random() < 0.3:
                    session.headers['User-Agent'] = self.ua.random

                print(f"Worker {worker_id}: {'Quick checking' if quick_check else 'Fetching'} {url} (attempt {attempt + 1}/{max_retries})")

                # Add referrer for some sites
                headers = {}
                if 'listcrawler' in url.lower() or 'aaok' in url.lower():
                    headers['Referer'] = 'https://www.google.com/'

                # Make request
                response = session.get(
                    url,
                    headers=headers,
                    timeout=30,
                    allow_redirects=True
                )

                if response.status_code == 200:
                    html = response.text

                    if len(html) < 500:
                        print(f"Worker {worker_id}: Content too short ({len(html)} chars), retrying...")
                        if attempt < max_retries - 1:
                            time.sleep(random.uniform(3, 8))
                            continue

                    # Check for common blocking indicators
                    blocking_indicators = [
                        "Access denied",
                        "Blocked",
                        "403 Forbidden",
                        "Cloudflare",
                        "Just a moment",
                        "Enable JavaScript and cookies",
                        "Checking your browser",
                        "Please wait while we verify",
                        "Ray ID:",
                        "DDoS protection"
                    ]

                    html_lower = html.lower()
                    if any(indicator.lower() in html_lower for indicator in blocking_indicators):
                        print(f"Worker {worker_id}: Blocking detected, retrying with longer delay...")
                        if attempt < max_retries - 1:
                            time.sleep(random.uniform(10, 20))
                            continue

                    print(f"Worker {worker_id}: ✅ Successfully fetched {len(html)} characters")
                    return html

                elif response.status_code == 429:
                    print(f"Worker {worker_id}: Rate limited (429), waiting...")
                    time.sleep(random.uniform(30, 60))
                    continue

                elif response.status_code in [403, 406]:
                    print(f"Worker {worker_id}: Blocked ({response.status_code}), trying with different headers...")
                    if attempt < max_retries - 1:
                        # Try with different user agent
                        session.headers['User-Agent'] = self.ua.random
                        time.sleep(random.uniform(5, 15))
                        continue

                else:
                    print(f"Worker {worker_id}: HTTP {response.status_code} - {response.reason}")
                    if attempt < max_retries - 1:
                        sleep_time = random.uniform(5, 15) * (attempt + 1)
                        print(f"Worker {worker_id}: Waiting {sleep_time:.1f}s before retry...")
                        time.sleep(sleep_time)

            except requests.exceptions.Timeout:
                print(f"Worker {worker_id}: Request timeout on attempt {attempt + 1}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(10, 20))

            except Exception as e:
                print(f"Worker {worker_id}: Request failed on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(5, 15))

        print(f"Worker {worker_id}: ❌ All attempts failed for {url}")
        return None

    def quick_extract_phone_from_html(self, html: str) -> Optional[str]:
        """Quickly extract phone number from HTML for deduplication check"""
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Extract phone from viewposttelephone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                return phone_clean.strip() if phone_clean.strip() else None

            # Fallback: look for phone patterns in text
            phone_patterns = [
                r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
                r'\(\d{3}\)\s*\d{3}[-.]?\d{4}',
                r'\b\d{10}\b'
            ]

            page_text = soup.get_text()
            for pattern in phone_patterns:
                matches = re.findall(pattern, page_text)
                if matches:
                    return matches[0]

            return None
        except:
            return None

    def extract_data_from_html(self, html: str, url: str) -> Dict:
        """Extract profile data from HTML using proper selectors"""
        data = {
            'url': url,
            'name': '',
            'age': '',
            'phone': '',
            'location': '',
            'description': '',
            'website_type': '',
            'city': '',
            'social': '',
            'extraction_success': False,
            'scraped_at': datetime.now().isoformat(),
            'content_length': len(html),
            'scraper_type': 'stealthy'
        }

        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Detect website type
            domain = url.lower()
            if 'aypapi' in domain:
                data['website_type'] = 'aypapi'
            elif 'aaok' in domain or 'listcrawler' in domain:
                data['website_type'] = 'aaok'
            else:
                data['website_type'] = 'unknown'

            # Extract name - first try viewpostname, then fallback to viewposttitle
            name_elem = soup.select_one('.viewpostname')
            if name_elem:
                # First try to get text after the Nym: span
                nym_span = name_elem.select_one('span')
                if nym_span:
                    # Get all text after the span
                    name_text = name_elem.get_text(strip=True)
                    span_text = nym_span.get_text(strip=True)
                    if span_text in name_text:
                        extracted_name = name_text.replace(span_text, '').strip()
                        if extracted_name:  # Only use if not empty
                            data['name'] = extracted_name
                else:
                    # Fallback to full text content
                    name_text = name_elem.get_text(strip=True)
                    if 'Nym:' in name_text:
                        extracted_name = name_text.replace('Nym:', '').strip()
                        if extracted_name:  # Only use if not empty
                            data['name'] = extracted_name
                    elif name_text:
                        data['name'] = name_text

            # If name is still empty, try to extract from title
            if not data['name']:
                title_elem = soup.select_one('.viewposttitle')
                if title_elem:
                    title_text = title_elem.get_text(strip=True)
                    # Remove age from title to get name
                    age_elem = soup.select_one('.postTitleAge')
                    if age_elem:
                        age = age_elem.get_text(strip=True)
                        # Remove age and dash pattern from title
                        title_without_age = title_text.replace(f' - {age}', '').replace(f'- {age}', '').replace(f' -{age}', '').replace(f'-{age}', '').strip()
                        if title_without_age:
                            data['name'] = title_without_age
                    else:
                        # No age found, use title as is but limit length
                        if title_text:
                            data['name'] = title_text[:100].strip()

            # Extract age from postTitleAge span
            age_elem = soup.select_one('.postTitleAge')
            if age_elem:
                age_text = age_elem.get_text(strip=True)
                if age_text.isdigit() and 18 <= int(age_text) <= 65:
                    data['age'] = age_text

            # Extract phone from viewposttelephone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                if phone_clean.strip():
                    data['phone'] = phone_clean.strip()

            # Extract location from the second li in viewpostlocationIconBabylon
            location_container = soup.select_one('.viewpostlocationIconBabylon ul')
            if location_container:
                li_elements = location_container.select('li')
                if len(li_elements) >= 2:
                    location_text = li_elements[1].get_text(strip=True)
                    # Remove "Location:" prefix if present
                    if 'Location:' in location_text:
                        data['location'] = location_text.replace('Location:', '').strip()
                    else:
                        data['location'] = location_text

            # Extract description from viewpostbody
            body_elem = soup.select_one('.viewpostbody')
            if body_elem:
                data['description'] = body_elem.get_text(strip=True)[:500]

            # Get page title and extract city (2nd word)
            title = soup.title
            if title:
                page_title = title.get_text(strip=True)
                data['page_title'] = page_title

                # Extract city from page title (2nd word)
                words = page_title.split()
                if len(words) >= 2:
                    data['city'] = words[1]  # Take the 2nd word as city

            # Simple social media extraction (no API calls for stealth)
            if data['description']:
                social_patterns = [
                    r'instagram[:\s]*@?\w+',
                    r'telegram[:\s]*@?\w+',
                    r'twitter[:\s]*@?\w+',
                    r'snapchat[:\s]*@?\w+',
                    r'@\w+',
                ]

                social_matches = []
                desc_lower = data['description'].lower()
                for pattern in social_patterns:
                    matches = re.findall(pattern, desc_lower)
                    social_matches.extend(matches)

                data['social'] = ', '.join(social_matches[:3]) if social_matches else "None"
            else:
                data['social'] = "None"

            # Check if we got meaningful data
            if data['name'] or data['age'] or data['phone']:
                data['extraction_success'] = True

            # Additional validation - check for actual profile content
            if data['extraction_success']:
                # Look for profile indicators to confirm this is a real profile page
                profile_indicators = ['escort', 'profile', 'contact', 'call', 'text']
                page_text = soup.get_text().lower()
                if not any(indicator in page_text for indicator in profile_indicators):
                    data['extraction_success'] = False
                    data['error'] = 'No profile content detected'

        except Exception as e:
            print(f"❌ Error extracting data from {url}: {e}")
            data['error'] = str(e)

        return data

    def process_url_batch(self, urls: List[str], worker_id: str) -> List[Dict]:
        """Process a batch of URLs with phone number deduplication"""
        results = []

        print(f"Worker {worker_id}: Starting batch of {len(urls)} URLs")

        for i, url in enumerate(urls):
            print(f"\nWorker {worker_id}: Processing {i+1}/{len(urls)}: {url}")

            # Step 1: Quick phone check to avoid unnecessary scraping
            html_preview = self.fetch_with_stealth(url, worker_id, quick_check=True)

            if html_preview:
                quick_phone = self.quick_extract_phone_from_html(html_preview)

                if quick_phone and self.is_phone_already_scraped(quick_phone):
                    print(f"Worker {worker_id}: 🚫 SKIPPED - Phone {self.clean_phone_number(quick_phone)} already scraped")

                    # Add skipped entry
                    skipped_data = {
                        'url': url,
                        'name': '',
                        'age': '',
                        'phone': quick_phone,
                        'location': '',
                        'description': '',
                        'website_type': 'aaok' if 'aaok' in url.lower() else 'aypapi',
                        'city': '',
                        'social': '',
                        'extraction_success': False,
                        'skip_reason': 'phone_already_scraped',
                        'duplicate_phone': self.clean_phone_number(quick_phone),
                        'scraped_at': datetime.now().isoformat(),
                        'content_length': len(html_preview),
                        'scraper_type': 'stealthy'
                    }
                    results.append(skipped_data)

                    with self.results_lock:
                        self.skipped_count += 1

                    continue

                # Step 2: Process normally since phone is new or couldn't be extracted
                data = self.extract_data_from_html(html_preview, url)

                # Check if this phone was already scraped (double-check after full extraction)
                if data['phone'] and self.is_phone_already_scraped(data['phone']):
                    print(f"Worker {worker_id}: 🚫 DUPLICATE DETECTED - Phone {self.clean_phone_number(data['phone'])} already exists")
                    data['extraction_success'] = False
                    data['skip_reason'] = 'phone_duplicate_detected'
                    with self.results_lock:
                        self.skipped_count += 1
                else:
                    # Add phone to scraped set if extraction was successful
                    if data['phone'] and data['extraction_success']:
                        self.add_phone_to_scraped(data['phone'])

                results.append(data)

                if data['extraction_success'] and not data.get('skip_reason'):
                    print(f"Worker {worker_id}: ✅ SUCCESS")
                    print(f"    Name: '{data['name']}'")
                    print(f"    Age: {data['age']}")
                    print(f"    Phone: '{data['phone']}'")
                    print(f"    Type: {data['website_type']}")
                else:
                    print(f"Worker {worker_id}: ❌ No meaningful data extracted or duplicate")
                    if 'error' in data:
                        print(f"    Error: {data['error']}")
                    if 'skip_reason' in data:
                        print(f"    Skip Reason: {data['skip_reason']}")
            else:
                # Add failed entry
                failed_data = {
                    'url': url,
                    'name': '',
                    'age': '',
                    'phone': '',
                    'location': '',
                    'description': '',
                    'website_type': 'aaok' if 'aaok' in url.lower() else 'aypapi',
                    'city': '',
                    'social': '',
                    'extraction_success': False,
                    'error': 'Failed to fetch page content',
                    'scraped_at': datetime.now().isoformat(),
                    'content_length': 0,
                    'scraper_type': 'stealthy'
                }
                results.append(failed_data)
                print(f"Worker {worker_id}: ❌ Failed to fetch content")

            # Add results to main collection and save periodically
            with self.results_lock:
                self.results.append(data if html_preview else failed_data)
                self.processed_count += 1

                # Save intermediate results every 50 URLs (smaller batches for stealth)
                if self.processed_count % 50 == 0:
                    self.save_results(self.results, f"checkpoint_{self.processed_count}")
                    print(f"\n💾 CHECKPOINT: Saved {self.processed_count} URLs, Skipped {self.skipped_count} duplicates")

        successful = len([r for r in results if r.get('extraction_success', False) and not r.get('skip_reason')])
        skipped = len([r for r in results if r.get('skip_reason')])
        print(f"Worker {worker_id}: ✅ Batch completed: {successful} successful, {skipped} skipped duplicates, {len(results)-successful-skipped} failed")
        return results

    def process_urls_parallel(self, urls: List[str], max_urls: Optional[int] = None) -> List[Dict]:
        """Process URLs in parallel with enhanced deduplication"""

        if max_urls:
            urls = urls[:max_urls]

        print(f"\n🥷 Starting STEALTHY parallel processing of {len(urls)} URLs")
        print(f"👥 Workers: {self.max_workers}")
        print(f"📱 Pre-loaded phone numbers to skip: {len(self.scraped_phone_numbers)}")
        print(f"🕒 Delay between requests: {self.min_delay}-{self.max_delay} seconds")
        print("=" * 60)

        # Split URLs into batches for workers (smaller batches for stealth)
        batch_size = max(1, len(urls) // self.max_workers)
        if batch_size > 25:  # Limit batch size for stealth
            batch_size = 25

        batches = [urls[i:i + batch_size] for i in range(0, len(urls), batch_size)]

        all_results = []
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all batches
            future_to_worker = {}
            for i, batch in enumerate(batches):
                worker_id = f"W{i+1}"
                future = executor.submit(self.process_url_batch, batch, worker_id)
                future_to_worker[future] = worker_id

            # Collect results
            for future in as_completed(future_to_worker):
                worker_id = future_to_worker[future]
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    print(f"✅ {worker_id} completed")
                except Exception as e:
                    print(f"❌ {worker_id} failed: {e}")

        elapsed_time = time.time() - start_time

        print(f"\n🏁 STEALTHY PROCESSING COMPLETE")
        print(f"⏱️  Total time: {elapsed_time:.1f} seconds")
        print(f"📊 Processed: {len(all_results)} URLs")
        print(f"🚫 Skipped duplicates: {self.skipped_count}")
        print(f"📱 Unique phone numbers collected: {len(self.scraped_phone_numbers)}")

        return all_results

    def save_results(self, results: List[Dict], suffix: str = "final"):
        """Save results to Excel file with detailed analysis"""
        if not results:
            print("⚠️  No results to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"stealthy_dedup_results_{len(results)}_urls_{timestamp}_{suffix}.xlsx"

        try:
            df = pd.DataFrame(results)

            # Reorder columns
            column_order = [
                'url', 'name', 'age', 'phone', 'location', 'description',
                'website_type', 'extraction_success', 'skip_reason', 'duplicate_phone',
                'page_title', 'content_length', 'scraper_type', 'social', 'scraped_at', 'error'
            ]

            # Only include columns that exist
            existing_columns = [col for col in column_order if col in df.columns]
            df = df[existing_columns]

            # Create multiple sheets
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # All data
                df.to_excel(writer, sheet_name='All_Data', index=False)

                # Successful extractions only (new unique data)
                successful_df = df[(df['extraction_success'] == True) & (df['skip_reason'].isna())]
                if not successful_df.empty:
                    successful_df.to_excel(writer, sheet_name='New_Unique_Data', index=False)

                # Skipped duplicates
                skipped_df = df[df['skip_reason'].notna()]
                if not skipped_df.empty:
                    skipped_df.to_excel(writer, sheet_name='Skipped_Duplicates', index=False)

                # Failed extractions
                failed_df = df[(df['extraction_success'] == False) & (df['skip_reason'].isna())]
                if not failed_df.empty:
                    failed_df.to_excel(writer, sheet_name='Failed', index=False)

                # Website type breakdown
                aaok_df = df[df['website_type'] == 'aaok']
                aypapi_df = df[df['website_type'] == 'aypapi']

                if not aaok_df.empty:
                    aaok_df.to_excel(writer, sheet_name='AAOK_Sites', index=False)
                if not aypapi_df.empty:
                    aypapi_df.to_excel(writer, sheet_name='AYPAPI_Sites', index=False)

                # Statistics sheet
                stats_data = {
                    'Metric': [
                        'Total URLs Processed',
                        'New Unique Extractions',
                        'Skipped Duplicates',
                        'Failed Extractions',
                        'Deduplication Rate (%)',
                        'Success Rate (New Data) (%)',
                        'Total Unique Phone Numbers',
                        'Pre-loaded Phone Numbers',
                        'AAOK URLs',
                        'AYPAPI URLs',
                        'Unknown Type URLs',
                        'Average Content Length',
                        'Scraper Type',
                        'Processing Time (estimated)',
                        'Cost Savings (vs ScraperAPI)'
                    ],
                    'Value': [
                        len(df),
                        len(successful_df),
                        len(skipped_df),
                        len(failed_df),
                        f"{(len(skipped_df) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                        f"{(len(successful_df) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                        len(self.scraped_phone_numbers),
                        len(self.scraped_phone_numbers) - len(successful_df),
                        len(df[df['website_type'] == 'aaok']),
                        len(df[df['website_type'] == 'aypapi']),
                        len(df[df['website_type'] == 'unknown']),
                        f"{df['content_length'].mean():.0f}" if 'content_length' in df.columns else 'N/A',
                        'Stealthy (Direct)',
                        f"{len(df) * 3:.0f} seconds",
                        f"${len(df) * 0.001:.3f}"  # ScraperAPI cost avoided
                    ]
                }
                pd.DataFrame(stats_data).to_excel(writer, sheet_name='Statistics', index=False)

            # Calculate and display statistics
            successful = len(successful_df)
            failed = len(failed_df)
            success_rate = (successful / len(df)) * 100 if len(df) > 0 else 0

            print(f"\n💾 SAVED: {filename}")
            print(f"📊 STEALTHY SCRAPING RESULTS:")
            print(f"   Total URLs: {len(df)}")
            print(f"   New Unique Data: {successful} ({success_rate:.1f}%)")
            print(f"   Skipped Duplicates: {len(skipped_df)} ({(len(skipped_df)/len(df)*100):.1f}%)")
            print(f"   Failed: {failed}")
            print(f"   Total Unique Phone Numbers: {len(self.scraped_phone_numbers)}")

            # Show sample successful extractions
            if not successful_df.empty:
                print(f"\n✅ SAMPLE NEW UNIQUE DATA:")
                for i, row in successful_df.head(5).iterrows():
                    name = row.get('name', 'N/A')
                    age = row.get('age', 'N/A')
                    phone = row.get('phone', 'N/A')
                    website = row.get('website_type', 'N/A')
                    print(f"   • {name} | Age: {age} | Phone: {phone} | Type: {website}")

            # Show failure analysis
            if not failed_df.empty and 'error' in failed_df.columns:
                error_counts = failed_df['error'].value_counts()
                print(f"\n❌ TOP FAILURE REASONS:")
                for error, count in error_counts.head(3).items():
                    print(f"   • {error}: {count} URLs")

            # Cost savings estimate
            avoided_cost = len(df) * 0.001  # ScraperAPI cost avoided
            print(f"\n💰 ScraperAPI cost avoided: ${avoided_cost:.3f}")

        except Exception as e:
            print(f"❌ Error saving results: {e}")

def install_requirements():
    """Install required packages for stealthy scraping"""
    requirements = [
        'requests',
        'beautifulsoup4',
        'pandas',
        'openpyxl',
        'fake-useragent'
    ]

    print("📦 Installing required packages...")
    for package in requirements:
        try:
            import subprocess
            subprocess.check_call(['pip', 'install', package])
            print(f"   ✅ {package}")
        except:
            print(f"   ⚠️  Failed to install {package}, please install manually")

def main():
    """Main function to run the stealthy deduplication scraper"""

    print("🥷 STEALTHY DEDUPLICATION SCRAPER")
    print("=" * 50)
    print("📋 Features:")
    print("   • No API costs (direct requests)")
    print("   • Phone number deduplication")
    print("   • Stealth techniques (user agent rotation, delays)")
    print("   • Automatic existing data loading")
    print("   • Multi-threaded processing")
    print("=" * 50)

    # Check if fake_useragent is installed
    try:
        from fake_useragent import UserAgent
    except ImportError:
        print("⚠️  fake_useragent not found. Installing requirements...")
        install_requirements()
        print("✅ Installation complete. Please run the script again.")
        return

    # Initialize scraper with conservative settings for stealth
    scraper = StealthyDeduplicationScraper(
        max_workers=2,  # Keep low for stealth
        enable_social_media=False  # Disabled for stealth/speed
    )

    # Load URLs
    if not os.path.exists(scraper.urls_file):
        print(f"❌ URLs file not found: {scraper.urls_file}")
        print("Please ensure the file exists with your URLs to scrape.")
        return

    with open(scraper.urls_file, 'r') as f:
        urls_data = json.load(f)

    # Extract URLs from nested structure
    urls = []
    if isinstance(urls_data, dict):
        for combination_key, combination_data in urls_data.items():
            if isinstance(combination_data, dict) and 'urls' in combination_data:
                urls.extend(combination_data['urls'])
    elif isinstance(urls_data, list):
        urls = urls_data

    print(f"📂 Loaded {len(urls)} URLs from {scraper.urls_file}")

    if not urls:
        print("❌ No URLs found to process")
        return

    # Process URLs with deduplication
    max_urls = 200  # Production batch size - increase for larger runs

    print(f"\n🥷 Starting Stealthy Deduplication Scraper")
    print(f"📱 Pre-loaded {len(scraper.scraped_phone_numbers)} existing phone numbers")
    print(f"🎯 Processing up to {max_urls if max_urls else len(urls)} URLs")
    print(f"⏱️  Estimated time: {(max_urls or len(urls)) * 3 / 60:.1f} minutes")
    print("=" * 60)

    # Auto-proceed for production batches
    print(f"✅ Processing {max_urls} URLs")
    print(f"⏱️  Estimated time: {max_urls * 3 / 60:.1f} minutes")
    print(f"💰 Cost savings vs ScraperAPI: ${max_urls * 0.001:.2f}")
    print(f"🎯 Expected deduplication: ~{min(80, len(scraper.scraped_phone_numbers) * 100 // max_urls)}% of URLs will be skipped")

    # Process URLs
    results = scraper.process_urls_parallel(urls, max_urls=max_urls)

    # Final save
    scraper.save_results(results, "final")

    # Final statistics
    print(f"\n🎉 STEALTHY SCRAPING COMPLETED!")
    print(f"📊 Final Stats:")
    print(f"   • Total URLs processed: {len(results)}")
    print(f"   • Duplicates skipped: {scraper.skipped_count}")
    efficiency = (scraper.skipped_count / len(results) * 100) if len(results) > 0 else 0
    print(f"   • Deduplication efficiency: {efficiency:.1f}% saved")
    print(f"   • Unique phone numbers: {len(scraper.scraped_phone_numbers)}")
    print(f"   • No API costs incurred! 💰")

    # Tips for next run
    print(f"\n💡 TIPS FOR NEXT RUN:")
    print(f"   • Increase max_urls in the script for larger batches")
    print(f"   • Adjust delays if you encounter blocking")
    print(f"   • The scraper will skip {scraper.skipped_count} duplicates automatically")

if __name__ == "__main__":
    main()
