#!/usr/bin/env python3
"""
Two-Phase NYC Scraper: 
Phase 1: Extract all URLs from search pages and store in file
Phase 2: Batch process dedicated pages for the 5 NYC boroughs
"""

import sys
import os
import json
import time
import argparse
from typing import List, Dict, Optional
from datetime import datetime
import pandas as pd
import re
from pathlib import Path

from nyc_boroughs_scraper import NYCBoroughsScraper

class TwoPhaseNYCScraper:
    def __init__(self, mistral_api_key: str = None):
        """Initialize two-phase NYC scraper"""
        self.mistral_api_key = mistral_api_key or "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G"
        self.urls_file = "nyc_all_urls.json"
        self.target_boroughs = ['Brooklyn', 'Bronx', 'Queens', 'Manhattan', 'Staten Island']
        
        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('two_phase_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def get_nyc_combinations(self) -> List[Dict[str, str]]:
        """Get all NYC borough-source combinations"""
        temp_scraper = NYCBoroughsScraper(self.mistral_api_key)
        all_combinations = temp_scraper.get_nyc_boroughs()
        
        # Filter for target boroughs
        nyc_combinations = []
        for combo in all_combinations:
            if combo['city'] in self.target_boroughs:
                nyc_combinations.append(combo)
        
        self.logger.info(f"Found {len(nyc_combinations)} NYC borough-source combinations")
        return nyc_combinations
    
    def phase1_extract_all_urls(self) -> bool:
        """Phase 1: Extract all dedicated page URLs from search pages"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 1: EXTRACTING ALL URLS FROM SEARCH PAGES")
        self.logger.info("=" * 60)
        
        # Check if URLs file already exists
        if os.path.exists(self.urls_file):
            self.logger.info(f"URLs file {self.urls_file} already exists. Loading existing data...")
            try:
                with open(self.urls_file, 'r') as f:
                    existing_data = json.load(f)
                self.logger.info(f"Loaded {len(existing_data)} existing URL collections")
                return True
            except Exception as e:
                self.logger.warning(f"Failed to load existing URLs file: {e}")
        
        # Get all NYC combinations
        combinations = self.get_nyc_combinations()
        
        # Create scraper instance
        scraper = NYCBoroughsScraper(self.mistral_api_key)
        scraper.request_delay = 0.5  # Faster for URL extraction
        
        all_urls_data = {}
        
        for i, combo in enumerate(combinations):
            borough_name = combo['city']
            source = combo['source']
            base_url = combo['url']
            combo_key = f"{borough_name}_{source}"
            
            self.logger.info(f"Processing {i+1}/{len(combinations)}: {borough_name} from {source}")
            
            # Extract URLs from all search pages for this combination
            all_dedicated_urls = []
            page_num = 1
            empty_page_count = 0
            
            while True:
                page_url = re.sub(r'/\d+$', f'/{page_num}', base_url)
                self.logger.info(f"  Extracting URLs from page {page_num}: {page_url}")
                
                # Get search page HTML
                search_html = scraper.execute_curl_request(page_url, scraper.search_curl_template)
                if not search_html:
                    self.logger.warning(f"  Failed to get page {page_num}")
                    empty_page_count += 1
                else:
                    # Extract dedicated page URLs (filtered by age ≤30)
                    page_dedicated_urls = scraper.extract_dedicated_urls(search_html)
                    if not page_dedicated_urls:
                        empty_page_count += 1
                        self.logger.info(f"  No URLs found on page {page_num} (empty page {empty_page_count}/{scraper.max_consecutive_empty_pages})")
                    else:
                        empty_page_count = 0
                        all_dedicated_urls.extend(page_dedicated_urls)
                        self.logger.info(f"  Found {len(page_dedicated_urls)} URLs on page {page_num}")
                
                # Check stopping conditions
                if page_num > scraper.min_pages_to_scrape and empty_page_count >= scraper.max_consecutive_empty_pages:
                    self.logger.info(f"  Stopping after {scraper.max_consecutive_empty_pages} consecutive empty pages (scraped {page_num} pages)")
                    break
                elif page_num <= scraper.min_pages_to_scrape:
                    self.logger.info(f"  Continuing - need minimum {scraper.min_pages_to_scrape} pages (currently at {page_num})")
                
                time.sleep(scraper.request_delay)
                page_num += 1
                
                if page_num > scraper.max_pages_per_city:
                    self.logger.info(f"  Reached maximum page limit ({scraper.max_pages_per_city})")
                    break
            
            # Remove duplicates while preserving order
            unique_urls = list(dict.fromkeys(all_dedicated_urls))
            
            # Store URLs data
            all_urls_data[combo_key] = {
                'borough': borough_name,
                'source': source,
                'base_url': base_url,
                'pages_scraped': page_num - 1,
                'total_urls': len(unique_urls),
                'urls': unique_urls,
                'extracted_at': datetime.now().isoformat()
            }
            
            self.logger.info(f"  Completed {borough_name} ({source}): {len(unique_urls)} unique URLs from {page_num-1} pages")
            
            # Save progress after each combination
            try:
                with open(self.urls_file, 'w') as f:
                    json.dump(all_urls_data, f, indent=2)
                self.logger.info(f"  Saved progress to {self.urls_file}")
            except Exception as e:
                self.logger.error(f"  Failed to save progress: {e}")
        
        # Final summary
        total_urls = sum(data['total_urls'] for data in all_urls_data.values())
        total_pages = sum(data['pages_scraped'] for data in all_urls_data.values())
        
        self.logger.info("=" * 60)
        self.logger.info("PHASE 1 COMPLETED - URL EXTRACTION SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"Total combinations processed: {len(all_urls_data)}")
        self.logger.info(f"Total search pages scraped: {total_pages}")
        self.logger.info(f"Total dedicated URLs extracted: {total_urls}")
        
        for combo_key, data in all_urls_data.items():
            self.logger.info(f"  {data['borough']} ({data['source']}): {data['total_urls']} URLs from {data['pages_scraped']} pages")
        
        self.logger.info(f"All URLs saved to: {self.urls_file}")
        return True
    
    def phase2_batch_scrape_dedicated_pages(self, batch_size: int = 10) -> bool:
        """Phase 2: Batch scrape all dedicated pages using multiple API keys"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 2: BATCH SCRAPING DEDICATED PAGES")
        self.logger.info("=" * 60)
        
        # Load URLs data
        if not os.path.exists(self.urls_file):
            self.logger.error(f"URLs file {self.urls_file} not found. Run Phase 1 first.")
            return False
        
        try:
            with open(self.urls_file, 'r') as f:
                urls_data = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load URLs file: {e}")
            return False
        
        # Your 5 API keys for batch processing
        api_keys = [
            "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G",
            "OHUPD3rpUQBbbd9FHpwnQpdQXIckRXqv", 
            "zeUtrAhXZm7RXe2Knt0xWGb19j3vb3f4",
            "Z9G5EWlDgYq8RtkV8xPfs7hZuAYghzg0",
            "e7QxoqwJNSPjcXFVmnEVgpAInrkWlRLS"
        ]
        
        self.logger.info(f"Using {len(api_keys)} API keys for batch processing (batch size: {batch_size})")
        
        # Summary of what we'll process
        total_urls = sum(data['total_urls'] for data in urls_data.values())
        total_batches = (total_urls + batch_size - 1) // batch_size
        
        self.logger.info(f"Total URLs to process: {total_urls}")
        self.logger.info(f"Estimated batches: {total_batches}")
        self.logger.info(f"Estimated API calls: {total_batches} (vs {total_urls} individual calls)")
        self.logger.info(f"API call reduction: {((total_urls - total_batches) / total_urls * 100):.1f}%")
        
        # Process each borough-source combination
        all_extracted_data = []
        
        for combo_key, combo_data in urls_data.items():
            borough = combo_data['borough']
            source = combo_data['source']
            urls = combo_data['urls']
            
            self.logger.info(f"Processing {borough} ({source}): {len(urls)} URLs")
            
            if not urls:
                self.logger.info(f"  No URLs to process for {borough} ({source})")
                continue
            
            # Use round-robin API key selection
            api_key_index = list(urls_data.keys()).index(combo_key) % len(api_keys)
            api_key = api_keys[api_key_index]
            key_preview = api_key[:8] + "..." + api_key[-4:]
            
            self.logger.info(f"  Using API key: {key_preview}")
            
            # Create scraper for this combination
            scraper = NYCBoroughsScraper(api_key)
            
            # Download all HTML content first
            self.logger.info(f"  Downloading HTML for {len(urls)} pages...")
            html_data = []
            
            for i, url in enumerate(urls):
                if i > 0 and i % 100 == 0:
                    self.logger.info(f"    Downloaded {i}/{len(urls)} pages")
                
                html = scraper.execute_curl_request(url, scraper.dedicated_curl_template)
                if html:
                    html_data.append((url, html))
                else:
                    self.logger.warning(f"    Failed to get HTML for {url}")
                
                time.sleep(0.1)  # Small delay
            
            self.logger.info(f"  Successfully downloaded {len(html_data)} HTML pages")
            
            # Process in batches
            combo_extracted_data = []
            total_combo_batches = (len(html_data) + batch_size - 1) // batch_size
            
            for batch_num in range(total_combo_batches):
                start_idx = batch_num * batch_size
                end_idx = min(start_idx + batch_size, len(html_data))
                batch = html_data[start_idx:end_idx]
                
                self.logger.info(f"    Processing batch {batch_num + 1}/{total_combo_batches} ({len(batch)} pages)")
                
                # Process batch with Mistral (implement batch processing here)
                batch_results = self.process_batch_with_mistral(batch, scraper, borough, source, combo_data['base_url'])
                
                combo_extracted_data.extend(batch_results)
                
                # Rate limiting between batches
                time.sleep(1.0)
            
            self.logger.info(f"  Completed {borough} ({source}): {len(combo_extracted_data)} records from {total_combo_batches} batches")
            all_extracted_data.extend(combo_extracted_data)
            
            # Save checkpoint after each combination
            checkpoint_file = f"phase2_{borough}_{source}_checkpoint.xlsx"
            if combo_extracted_data:
                try:
                    df = pd.DataFrame(combo_extracted_data)
                    df.to_excel(checkpoint_file, index=False)
                    self.logger.info(f"  Saved checkpoint: {checkpoint_file}")
                except Exception as e:
                    self.logger.warning(f"  Failed to save checkpoint: {e}")
        
        # Save final results
        if all_extracted_data:
            final_file = "two_phase_nyc_final.xlsx"
            try:
                df = pd.DataFrame(all_extracted_data)
                
                # Reorder columns
                column_order = [
                    'state', 'city', 'source', 'title', 'name', 'age', 'phone', 
                    'description', 'social_media', 'email', 'website', 'posted_date', 
                    'post_id', 'url', 'search_url', 'scraped_at'
                ]
                
                existing_columns = [col for col in column_order if col in df.columns]
                remaining_columns = [col for col in df.columns if col not in existing_columns]
                final_columns = existing_columns + remaining_columns
                df = df[final_columns]
                
                # Sort data
                df = df.sort_values(['state', 'city', 'source', 'scraped_at'])
                
                df.to_excel(final_file, index=False)
                self.logger.info(f"Final results saved to: {final_file}")
                
                # Summary
                self.logger.info("=" * 60)
                self.logger.info("PHASE 2 COMPLETED - DEDICATED PAGE SCRAPING SUMMARY")
                self.logger.info("=" * 60)
                self.logger.info(f"Total records extracted: {len(all_extracted_data)}")
                
                # Summary by source
                aaok_count = len([r for r in all_extracted_data if r.get('source') == 'aaok'])
                aypapi_count = len([r for r in all_extracted_data if r.get('source') == 'aypapi'])
                self.logger.info(f"  - aaok.com: {aaok_count} records")
                self.logger.info(f"  - aypapi.com: {aypapi_count} records")
                
                # Summary by borough
                boroughs = list(set(r.get('city') for r in all_extracted_data))
                self.logger.info(f"NYC Boroughs processed: {len(boroughs)}")
                for borough in sorted(boroughs):
                    borough_count = len([r for r in all_extracted_data if r.get('city') == borough])
                    self.logger.info(f"  - {borough}: {borough_count} records")
                
            except Exception as e:
                self.logger.error(f"Failed to save final results: {e}")
                return False
        
        return True

    def process_batch_with_mistral(self, html_batch: List, scraper, borough: str, source: str, base_url: str) -> List[Dict]:
        """Process a batch of HTML pages with Mistral AI"""
        if not html_batch:
            return []

        try:
            from mistralai import Mistral
            from bs4 import BeautifulSoup

            # Prepare batch content for Mistral
            batch_content = []
            for i, (url, html_content) in enumerate(html_batch):
                # Extract text content from HTML
                soup = BeautifulSoup(html_content, 'html.parser')

                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()

                # Get text content
                text_content = soup.get_text()

                # Clean up text
                lines = (line.strip() for line in text_content.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                text_content = ' '.join(chunk for chunk in chunks if chunk)

                # Limit text length to prevent token overflow
                if len(text_content) > 2500:
                    text_content = text_content[:2500] + "..."

                batch_content.append({
                    'page_index': i,
                    'url': url,
                    'content': text_content
                })

            # Create batch prompt
            prompt = f"""
            You are extracting data from {len(batch_content)} escort profile pages. For each page, extract the following information:

            1. title: Profile title or headline
            2. name: Person's name
            3. age: Age (must be ≤30, skip if >30)
            4. phone: Phone number
            5. description: Profile description
            6. social_media: Social media handles/links (Instagram, Twitter, Snapchat, OnlyFans, etc.)
            7. email: Email address
            8. website: Website links
            9. posted_date: When the post was created
            10. post_id: Unique post identifier

            IMPORTANT: Only include profiles where age ≤30 AND gender is woman. Skip any profiles with age >30 or not women.

            Pages to process:
            """

            for page_data in batch_content:
                prompt += f"\n--- PAGE {page_data['page_index']} (URL: {page_data['url']}) ---\n"
                prompt += page_data['content'][:1800]  # Limit content per page
                prompt += "\n"

            prompt += """

            Return a JSON array with one object per valid page (only for women with age ≤30). Each object should have:
            {
                "page_index": <index>,
                "url": "<url>",
                "title": "<title>",
                "name": "<name>",
                "age": "<age>",
                "phone": "<phone>",
                "description": "<description>",
                "social_media": "<social_media>",
                "email": "<email>",
                "website": "<website>",
                "posted_date": "<posted_date>",
                "post_id": "<post_id>"
            }

            If any field is not found, use null. Only return valid JSON array.
            """

            # Make Mistral API call
            response = scraper.mistral_client.chat.complete(
                model="mistral-large-latest",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=3000
            )

            result_text = response.choices[0].message.content.strip()

            # Parse JSON response
            try:
                batch_results = json.loads(result_text)
                if not isinstance(batch_results, list):
                    batch_results = [batch_results]

                # Add metadata to each result
                for result in batch_results:
                    if result and isinstance(result, dict):
                        result['city'] = borough
                        result['state'] = 'New York'
                        result['source'] = source
                        result['search_url'] = base_url
                        result['scraped_at'] = datetime.now().isoformat()

                self.logger.info(f"      Batch processed {len(html_batch)} pages, extracted {len(batch_results)} valid profiles")
                return batch_results

            except json.JSONDecodeError:
                # Try to extract JSON array from the response
                json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
                if json_match:
                    batch_results = json.loads(json_match.group())

                    # Add metadata
                    for result in batch_results:
                        if result and isinstance(result, dict):
                            result['city'] = borough
                            result['state'] = 'New York'
                            result['source'] = source
                            result['search_url'] = base_url
                            result['scraped_at'] = datetime.now().isoformat()

                    self.logger.info(f"      Batch processed {len(html_batch)} pages, extracted {len(batch_results)} valid profiles (fallback parsing)")
                    return batch_results

                self.logger.warning(f"      Failed to parse batch Mistral response")
                return []

        except Exception as e:
            self.logger.error(f"      Batch Mistral extraction failed: {e}")
            return []

def main():
    """Main entry point for two-phase NYC scraper"""
    parser = argparse.ArgumentParser(description='Two-Phase NYC Boroughs Scraper')
    parser.add_argument('--phase', choices=['1', '2', 'both'], default='both',
                       help='Which phase to run: 1=extract URLs, 2=scrape pages, both=run both phases')
    parser.add_argument('--batch-size', type=int, default=10,
                       help='Number of pages per Mistral API batch for Phase 2 (default: 10)')
    parser.add_argument('--mistral-key',
                       help='Mistral AI API key (optional - defaults to built-in key)')
    parser.add_argument('--clean', action='store_true',
                       help='Clean up existing files and start fresh')

    args = parser.parse_args()

    # Validate batch size
    if args.batch_size < 1 or args.batch_size > 50:
        print("Error: Batch size must be between 1 and 50")
        return 1

    # Get Mistral API key
    mistral_key = args.mistral_key or "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G"

    # Clean files if requested
    if args.clean:
        files_to_clean = [
            "nyc_all_urls.json",
            "two_phase_nyc_final.xlsx",
            "phase2_*_checkpoint.xlsx"
        ]

        import glob
        for pattern in files_to_clean:
            for file in glob.glob(pattern):
                try:
                    os.remove(file)
                    print(f"Cleaned up: {file}")
                except Exception as e:
                    print(f"Failed to clean {file}: {e}")

        print("Cleanup completed. Starting fresh.")

    # Create scraper
    scraper = TwoPhaseNYCScraper(mistral_api_key=mistral_key)

    print("Two-Phase NYC Boroughs Scraper")
    print("=" * 50)
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Features: Age ≤30, Women only, Batch Mistral AI processing")
    print()

    start_time = time.time()
    success = True

    # Run Phase 1
    if args.phase in ['1', 'both']:
        print("Starting Phase 1: URL Extraction...")
        success = scraper.phase1_extract_all_urls()
        if not success:
            print("Phase 1 failed!")
            return 1
        print("Phase 1 completed successfully!")
        print()

    # Run Phase 2
    if args.phase in ['2', 'both']:
        print(f"Starting Phase 2: Batch Dedicated Page Scraping (batch size: {args.batch_size})...")
        success = scraper.phase2_batch_scrape_dedicated_pages(batch_size=args.batch_size)
        if not success:
            print("Phase 2 failed!")
            return 1
        print("Phase 2 completed successfully!")

    end_time = time.time()
    processing_time = end_time - start_time

    print()
    print("=" * 50)
    print("TWO-PHASE SCRAPING COMPLETED!")
    print(f"Total processing time: {processing_time/60:.1f} minutes")
    print("=" * 50)

    return 0

if __name__ == "__main__":
    sys.exit(main())
