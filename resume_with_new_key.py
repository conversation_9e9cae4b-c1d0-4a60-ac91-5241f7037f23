#!/usr/bin/env python3
"""
Resume Scraping with New API Key
Continues scraping from where we left off with a fresh ScrapingDog API key
"""

import json
import os
import sys
from datetime import datetime
from targeted_city_scraper import TargetedCityScraper

def find_latest_progress():
    """Find the most recent progress file"""
    import glob

    progress_files = glob.glob("scraping_progress_*.json")
    if not progress_files:
        return None, 0

    # Sort by modification time, most recent first
    progress_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_file = progress_files[0]

    # Load and check how many URLs were processed
    try:
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        processed_count = len(data.get('scraped_profiles', []))
        return latest_file, processed_count
    except:
        return latest_file, 0

def load_remaining_urls(urls_file, start_position, max_urls=200):
    """Load remaining URLs to process"""
    try:
        with open(urls_file, 'r', encoding='utf-8') as f:
            all_urls = json.load(f)

        # Get remaining URLs
        remaining_urls = all_urls[start_position:start_position + max_urls]

        print(f"📂 URL Status:")
        print(f"   Total URLs in file: {len(all_urls)}")
        print(f"   Starting position: {start_position}")
        print(f"   URLs to process: {len(remaining_urls)}")
        print(f"   Remaining after this batch: {len(all_urls) - start_position - len(remaining_urls)}")

        return remaining_urls

    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return None

def main():
    """Resume scraping with new API key"""

    print("🔄 Resume Scraping with New API Key")
    print("=" * 60)

    # Check for new API key in command line
    new_api_key = None
    batch_size = 200

    if len(sys.argv) > 1:
        new_api_key = sys.argv[1]
        if len(sys.argv) > 2:
            try:
                batch_size = int(sys.argv[2])
            except ValueError:
                print("⚠️ Invalid batch size, using default 200")
                batch_size = 200

    if not new_api_key:
        print("❌ No API key provided!")
        print("Usage:")
        print(f"  python {sys.argv[0]} <new_api_key> [batch_size]")
        print(f"  python {sys.argv[0]} 68a390dbc2920968e9acce34 200")
        return

    print(f"🔑 New API Key: ...{new_api_key[-8:]}")
    print(f"📊 Batch Size: {batch_size} URLs")

    # Find where we left off
    progress_file, processed_count = find_latest_progress()

    if progress_file:
        print(f"\n📂 Found progress file: {progress_file}")
        print(f"✅ Already processed: {processed_count} URLs")
        start_position = processed_count
    else:
        print(f"\n📄 No progress file found, starting from beginning")
        start_position = 0

    # Load URLs file
    urls_file = "targeted_all_urls_20250819_001221.json"
    if not os.path.exists(urls_file):
        print(f"❌ URLs file not found: {urls_file}")
        return

    # Load remaining URLs to process
    profile_urls = load_remaining_urls(urls_file, start_position, batch_size)
    if not profile_urls:
        print("❌ No URLs to process")
        return

    if len(profile_urls) == 0:
        print("🎉 All URLs have been processed!")
        return

    # Initialize scraper with new API key
    print(f"\n🚀 Initializing scraper with new API key")
    print(f"👥 Using 3 workers for parallel processing")
    print(f"💾 Progress will be saved automatically")

    try:
        scraper = TargetedCityScraper(
            scrapingdog_api_keys=[new_api_key],
            mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso",
            max_urls_to_process=len(profile_urls)
        )

        # Load existing progress if available
        if progress_file:
            scraper.load_progress(progress_file)

    except Exception as e:
        print(f"❌ Failed to initialize scraper: {e}")
        return

    # Run scraping
    print(f"\n🎯 Starting Scraping")
    print(f"📍 Position: {start_position} to {start_position + len(profile_urls) - 1}")
    print(f"📊 URLs to process: {len(profile_urls)}")
    print("=" * 60)
    print("⚠️  Press Ctrl+C to stop and save progress")
    print("🔧 Using improved anti-bot detection")
    print("🔄 Automatic retry on failed requests")
    print("-" * 60)

    try:
        results = scraper.phase2_scrape_all_profiles(
            profile_urls=profile_urls,
            max_workers=3,  # Use 3 workers for good balance
            resume_file=progress_file if progress_file else None
        )

        # Save comprehensive results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Final results file
        results_file = f"resumed_results_pos{start_position}_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        # Comprehensive summary
        total_processed = start_position + len(profile_urls)
        remaining_urls = 1632 - total_processed  # Total URLs in file minus processed

        summary = {
            'session_info': {
                'start_position': start_position,
                'urls_in_this_batch': len(profile_urls),
                'end_position': start_position + len(profile_urls) - 1,
                'total_processed_so_far': total_processed,
                'remaining_urls': max(0, remaining_urls)
            },
            'batch_results': {
                'successful_scrapes': len(results),
                'duplicates_skipped': scraper.stats.get('duplicates_skipped', 0),
                'failed_extractions': scraper.stats.get('failed_extractions', 0),
                'success_rate': round((len(results) / len(profile_urls)) * 100, 2) if profile_urls else 0
            },
            'api_usage': {
                'api_key_used': f"...{new_api_key[-8:]}",
                'requests_made': scraper.stats.get('api_requests', 0),
                'requests_remaining': 900 - scraper.current_key_requests,
                'can_continue': scraper.current_key_requests < 900
            },
            'files_created': {
                'results_file': results_file,
                'progress_file': scraper.current_progress_file if hasattr(scraper, 'current_progress_file') else 'N/A'
            },
            'completion_time': timestamp
        }

        summary_file = f"resumed_summary_pos{start_position}_{timestamp}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # Display final results
        print(f"\n🎉 BATCH COMPLETED!")
        print(f"📊 Session Results:")
        print(f"   📍 Processed positions: {start_position} to {summary['session_info']['end_position']}")
        print(f"   ✅ Successful scrapes: {summary['batch_results']['successful_scrapes']}")
        print(f"   📈 Success rate: {summary['batch_results']['success_rate']}%")
        print(f"   🔄 Duplicates skipped: {summary['batch_results']['duplicates_skipped']}")
        print(f"   ❌ Failed extractions: {summary['batch_results']['failed_extractions']}")

        print(f"\n🔋 API Usage:")
        print(f"   🎯 Requests made: {summary['api_usage']['requests_made']}")
        print(f"   🔋 Requests remaining: {summary['api_usage']['requests_remaining']}/900")

        print(f"\n📂 Files Created:")
        print(f"   💾 Results: {results_file}")
        print(f"   📋 Summary: {summary_file}")

        print(f"\n📈 Overall Progress:")
        print(f"   ✅ Total processed so far: {summary['session_info']['total_processed_so_far']}/1632")
        print(f"   📊 Progress: {(summary['session_info']['total_processed_so_far']/1632)*100:.1f}%")
        print(f"   🔄 URLs remaining: {summary['session_info']['remaining_urls']}")

        # Next steps
        if summary['session_info']['remaining_urls'] > 0:
            if summary['api_usage']['can_continue']:
                print(f"\n🔄 NEXT STEPS:")
                print(f"   ✅ API key still has {summary['api_usage']['requests_remaining']} requests")
                print(f"   🎯 Can process ~{summary['api_usage']['requests_remaining']} more URLs")
                print(f"   🚀 Run again to continue:")
                print(f"      python {sys.argv[0]} {new_api_key} {min(batch_size, summary['api_usage']['requests_remaining'])}")
            else:
                print(f"\n🔋 API Key Exhausted:")
                print(f"   ⚠️ Need new API key to continue")
                print(f"   📊 {summary['session_info']['remaining_urls']} URLs still remaining")
                print(f"   🔄 Will resume from position {summary['session_info']['total_processed_so_far']}")
        else:
            print(f"\n🎉 ALL URLS PROCESSED!")
            print(f"   ✅ Completed entire dataset")
            print(f"   🎊 Scraping project finished!")

        # Save Excel if we have results
        if results:
            try:
                import pandas as pd
                df = pd.DataFrame(results)
                excel_file = f"resumed_excel_pos{start_position}_{timestamp}.xlsx"
                df.to_excel(excel_file, index=False)
                print(f"   📊 Excel file: {excel_file}")
            except Exception as e:
                print(f"   ⚠️ Excel export failed: {e}")

    except KeyboardInterrupt:
        print(f"\n⚠️ Scraping interrupted by user")
        print(f"💾 Progress automatically saved")
        print(f"🔄 Resume with: python {sys.argv[0]} {new_api_key}")

    except Exception as e:
        print(f"\n❌ Error during scraping: {e}")
        print(f"💾 Progress automatically saved")
        print(f"🔄 Resume with: python {sys.argv[0]} {new_api_key}")

if __name__ == "__main__":
    main()
