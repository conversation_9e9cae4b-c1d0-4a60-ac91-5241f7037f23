#!/usr/bin/env python3
"""
Simple Launcher for Phase 2 Scraping
Easy interface to start or resume scraping with progress tracking
"""

import os
import sys
import json
import glob
import subprocess
from datetime import datetime

def show_banner():
    """Display welcome banner"""
    print("🚀 Phase 2 Targeted City Scraper Launcher")
    print("=" * 60)
    print("🔧 Using ScrapingDog API with automatic progress saving")
    print("💾 Progress saved every 200 URLs and on API key switches")
    print("🔄 Resume capability from any saved progress point")
    print("=" * 60)

def check_requirements():
    """Check if required files exist"""
    urls_file = "targeted_all_urls_20250819_001221.json"
    runner_script = "run_phase2_scraping.py"

    missing = []

    if not os.path.exists(urls_file):
        missing.append(f"❌ URLs file: {urls_file}")
    else:
        # Check how many URLs we have
        try:
            with open(urls_file, 'r') as f:
                data = json.load(f)
            print(f"✅ URLs file found: {len(data)} URLs to process")
        except:
            missing.append(f"⚠️  URLs file corrupted: {urls_file}")

    if not os.path.exists(runner_script):
        missing.append(f"❌ Runner script: {runner_script}")
    else:
        print(f"✅ Runner script found: {runner_script}")

    if missing:
        print("\n🚨 MISSING REQUIREMENTS:")
        for item in missing:
            print(f"   {item}")
        return False

    return True

def find_progress_files():
    """Find existing progress files"""
    progress_files = glob.glob("scraping_progress_*.json")
    if progress_files:
        # Sort by modification time, most recent first
        progress_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        return progress_files
    return []

def show_progress_info(progress_file):
    """Show information about a progress file"""
    try:
        with open(progress_file, 'r') as f:
            data = json.load(f)

        scraped_count = len(data.get('scraped_profiles', []))
        stats = data.get('stats', {})
        current_key = data.get('current_key_index', 0) + 1
        key_requests = data.get('current_key_requests', 0)

        mod_time = datetime.fromtimestamp(os.path.getmtime(progress_file))

        print(f"📂 {progress_file}")
        print(f"   📅 Modified: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   ✅ Profiles: {scraped_count}")
        print(f"   🎯 API Requests: {stats.get('api_requests', 0)}")
        print(f"   🔑 Current API Key: {current_key} ({key_requests}/900 requests)")

    except Exception as e:
        print(f"⚠️  {progress_file} - Error reading: {e}")

def main():
    """Main launcher function"""
    show_banner()

    print("\n🔍 CHECKING REQUIREMENTS...")
    if not check_requirements():
        print("\n❌ Cannot proceed. Please ensure all required files are present.")
        return

    print("\n📊 CHECKING FOR EXISTING PROGRESS...")
    progress_files = find_progress_files()

    if progress_files:
        print(f"✅ Found {len(progress_files)} progress file(s):")
        print()
        for i, pf in enumerate(progress_files[:3]):  # Show only first 3
            print(f"{i+1}. ", end="")
            show_progress_info(pf)
            print()

        if len(progress_files) > 3:
            print(f"   ... and {len(progress_files) - 3} more")
    else:
        print("📄 No existing progress files found - will start fresh")

    print("\n🎯 LAUNCH OPTIONS:")
    print("=" * 30)
    print("1. Start fresh scraping")

    if progress_files:
        print("2. Resume from latest progress")
        print("3. Choose specific progress file")

    print("4. Check progress only")
    print("5. Exit")

    while True:
        try:
            choice = input("\nSelect option (1-5): ").strip()

            if choice == '1':
                print("\n🚀 Starting fresh scraping...")
                print("⚠️  This will ignore any existing progress files")
                confirm = input("Continue? (y/N): ").strip().lower()
                if confirm == 'y':
                    print("\n🎯 Launching scraper...")
                    subprocess.run([sys.executable, "run_phase2_scraping.py"])
                break

            elif choice == '2' and progress_files:
                latest_file = progress_files[0]
                print(f"\n🔄 Resuming from: {latest_file}")
                print("🎯 Launching scraper...")
                subprocess.run([sys.executable, "run_phase2_scraping.py", "--resume", latest_file])
                break

            elif choice == '3' and progress_files:
                print("\n📂 Available progress files:")
                for i, pf in enumerate(progress_files):
                    print(f"{i+1}. {pf}")

                try:
                    file_choice = int(input(f"\nSelect file (1-{len(progress_files)}): ")) - 1
                    if 0 <= file_choice < len(progress_files):
                        selected_file = progress_files[file_choice]
                        print(f"\n🔄 Resuming from: {selected_file}")
                        print("🎯 Launching scraper...")
                        subprocess.run([sys.executable, "run_phase2_scraping.py", "--resume", selected_file])
                        break
                    else:
                        print("❌ Invalid selection")
                except ValueError:
                    print("❌ Please enter a number")

            elif choice == '4':
                if os.path.exists("check_progress.py"):
                    print("\n📊 Opening progress checker...")
                    subprocess.run([sys.executable, "check_progress.py"])
                else:
                    if progress_files:
                        print(f"\n📊 Latest progress:")
                        show_progress_info(progress_files[0])
                    else:
                        print("\n📄 No progress files found")
                break

            elif choice == '5':
                print("\n👋 Goodbye!")
                break

            else:
                print("❌ Invalid choice. Please try again.")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
