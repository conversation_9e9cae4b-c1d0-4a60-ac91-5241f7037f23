#!/usr/bin/env python3
"""
URL Scraper: Extract all URLs from search pages and store in file
Extracted from the Phase 1 functionality of two_phase_nyc_scraper.py
"""

import sys
import os
import json
import time
import argparse
from typing import List, Dict, Optional
from datetime import datetime
import re
import logging

from nyc_boroughs_scraper import NYCBoroughsScraper


class URLScraper:
    def __init__(self, mistral_api_key: str = None, output_file: str = "extracted_urls.json"):
        """Initialize URL scraper"""
        self.mistral_api_key = mistral_api_key or "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G"
        self.output_file = output_file
        self.target_boroughs = ['Brooklyn', 'Bronx', 'Queens', 'Manhattan', 'Staten Island']

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('url_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def get_nyc_combinations(self) -> List[Dict[str, str]]:
        """Get all NYC borough-source combinations"""
        temp_scraper = NYCBoroughsScraper(self.mistral_api_key)
        all_combinations = temp_scraper.get_nyc_boroughs()

        # Filter for target boroughs
        nyc_combinations = []
        for combo in all_combinations:
            if combo['city'] in self.target_boroughs:
                nyc_combinations.append(combo)

        self.logger.info(f"Found {len(nyc_combinations)} NYC borough-source combinations")
        return nyc_combinations

    def extract_all_urls(self, force_refresh: bool = False) -> bool:
        """Extract all dedicated page URLs from search pages"""
        self.logger.info("=" * 60)
        self.logger.info("EXTRACTING ALL URLS FROM SEARCH PAGES")
        self.logger.info("=" * 60)

        # Check if URLs file already exists
        if os.path.exists(self.output_file) and not force_refresh:
            self.logger.info(f"URLs file {self.output_file} already exists. Loading existing data...")
            try:
                with open(self.output_file, 'r') as f:
                    existing_data = json.load(f)
                self.logger.info(f"Loaded {len(existing_data)} existing URL collections")
                return True
            except Exception as e:
                self.logger.warning(f"Failed to load existing URLs file: {e}")

        # Get all NYC combinations
        combinations = self.get_nyc_combinations()

        # Create scraper instance
        scraper = NYCBoroughsScraper(self.mistral_api_key)
        scraper.request_delay = 0.5  # Faster for URL extraction

        all_urls_data = {}

        for i, combo in enumerate(combinations):
            borough_name = combo['city']
            source = combo['source']
            base_url = combo['url']
            combo_key = f"{borough_name}_{source}"

            self.logger.info(f"Processing {i+1}/{len(combinations)}: {borough_name} from {source}")

            # Extract URLs from all search pages for this combination
            all_dedicated_urls = []
            page_num = 1
            empty_page_count = 0

            while True:
                page_url = re.sub(r'/\d+$', f'/{page_num}', base_url)
                self.logger.info(f"  Extracting URLs from page {page_num}: {page_url}")

                # Get search page HTML
                search_html = scraper.execute_curl_request(page_url, scraper.search_curl_template)
                if not search_html:
                    self.logger.warning(f"  Failed to get page {page_num}")
                    empty_page_count += 1
                else:
                    # Extract dedicated page URLs (filtered by age ≤30)
                    page_dedicated_urls = scraper.extract_dedicated_urls(search_html)
                    if not page_dedicated_urls:
                        empty_page_count += 1
                        self.logger.info(f"  No URLs found on page {page_num} (empty page {empty_page_count}/{scraper.max_consecutive_empty_pages})")
                    else:
                        empty_page_count = 0
                        all_dedicated_urls.extend(page_dedicated_urls)
                        self.logger.info(f"  Found {len(page_dedicated_urls)} URLs on page {page_num}")

                # Check stopping conditions
                if page_num > scraper.min_pages_to_scrape and empty_page_count >= scraper.max_consecutive_empty_pages:
                    self.logger.info(f"  Stopping after {scraper.max_consecutive_empty_pages} consecutive empty pages (scraped {page_num} pages)")
                    break
                elif page_num <= scraper.min_pages_to_scrape:
                    self.logger.info(f"  Continuing - need minimum {scraper.min_pages_to_scrape} pages (currently at {page_num})")

                time.sleep(scraper.request_delay)
                page_num += 1

                if page_num > scraper.max_pages_per_city:
                    self.logger.info(f"  Reached maximum page limit ({scraper.max_pages_per_city})")
                    break

            # Remove duplicates while preserving order
            unique_urls = list(dict.fromkeys(all_dedicated_urls))

            # Store URLs data
            all_urls_data[combo_key] = {
                'borough': borough_name,
                'source': source,
                'base_url': base_url,
                'pages_scraped': page_num - 1,
                'total_urls': len(unique_urls),
                'urls': unique_urls,
                'extracted_at': datetime.now().isoformat()
            }

            self.logger.info(f"  Completed {borough_name} ({source}): {len(unique_urls)} unique URLs from {page_num-1} pages")

            # Save progress after each combination
            try:
                with open(self.output_file, 'w') as f:
                    json.dump(all_urls_data, f, indent=2)
                self.logger.info(f"  Saved progress to {self.output_file}")
            except Exception as e:
                self.logger.error(f"  Failed to save progress: {e}")

        # Final summary
        total_urls = sum(data['total_urls'] for data in all_urls_data.values())
        total_pages = sum(data['pages_scraped'] for data in all_urls_data.values())

        self.logger.info("=" * 60)
        self.logger.info("URL EXTRACTION COMPLETED - SUMMARY")
        self.logger.info("=" * 60)
        self.logger.info(f"Total combinations processed: {len(all_urls_data)}")
        self.logger.info(f"Total search pages scraped: {total_pages}")
        self.logger.info(f"Total dedicated URLs extracted: {total_urls}")

        for combo_key, data in all_urls_data.items():
            self.logger.info(f"  {data['borough']} ({data['source']}): {data['total_urls']} URLs from {data['pages_scraped']} pages")

        self.logger.info(f"All URLs saved to: {self.output_file}")
        return True

    def get_url_stats(self) -> Optional[Dict]:
        """Get statistics about extracted URLs"""
        if not os.path.exists(self.output_file):
            self.logger.warning(f"URLs file {self.output_file} not found")
            return None

        try:
            with open(self.output_file, 'r') as f:
                data = json.load(f)

            stats = {
                'total_combinations': len(data),
                'total_urls': sum(combo['total_urls'] for combo in data.values()),
                'total_pages': sum(combo['pages_scraped'] for combo in data.values()),
                'by_borough': {},
                'by_source': {}
            }

            for combo_key, combo_data in data.items():
                borough = combo_data['borough']
                source = combo_data['source']

                if borough not in stats['by_borough']:
                    stats['by_borough'][borough] = {'urls': 0, 'pages': 0}
                stats['by_borough'][borough]['urls'] += combo_data['total_urls']
                stats['by_borough'][borough]['pages'] += combo_data['pages_scraped']

                if source not in stats['by_source']:
                    stats['by_source'][source] = {'urls': 0, 'pages': 0}
                stats['by_source'][source]['urls'] += combo_data['total_urls']
                stats['by_source'][source]['pages'] += combo_data['pages_scraped']

            return stats

        except Exception as e:
            self.logger.error(f"Failed to get URL stats: {e}")
            return None

    def export_urls_list(self, output_file: str = "urls_list.txt") -> bool:
        """Export all URLs to a simple text file (one URL per line)"""
        if not os.path.exists(self.output_file):
            self.logger.warning(f"URLs file {self.output_file} not found")
            return False

        try:
            with open(self.output_file, 'r') as f:
                data = json.load(f)

            all_urls = []
            for combo_data in data.values():
                all_urls.extend(combo_data['urls'])

            # Remove duplicates while preserving order
            unique_urls = list(dict.fromkeys(all_urls))

            with open(output_file, 'w') as f:
                for url in unique_urls:
                    f.write(url + '\n')

            self.logger.info(f"Exported {len(unique_urls)} unique URLs to {output_file}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to export URLs list: {e}")
            return False


def main():
    """Main entry point for URL scraper"""
    parser = argparse.ArgumentParser(description='Standalone URL Scraper for NYC Boroughs')
    parser.add_argument('--output', default='extracted_urls.json',
                       help='Output JSON file for extracted URLs (default: extracted_urls.json)')
    parser.add_argument('--force-refresh', action='store_true',
                       help='Force refresh even if output file exists')
    parser.add_argument('--stats', action='store_true',
                       help='Show statistics about extracted URLs')
    parser.add_argument('--export-list',
                       help='Export URLs to simple text file (one URL per line)')
    parser.add_argument('--mistral-key',
                       help='Mistral AI API key (optional - defaults to built-in key)')

    args = parser.parse_args()

    # Get Mistral API key
    mistral_key = args.mistral_key or "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G"

    # Create scraper
    scraper = URLScraper(mistral_api_key=mistral_key, output_file=args.output)

    print("NYC Boroughs URL Scraper")
    print("=" * 50)
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Features: Age ≤30, Women only")
    print(f"Output file: {args.output}")
    print()

    start_time = time.time()

    # Show stats if requested
    if args.stats:
        print("Getting URL statistics...")
        stats = scraper.get_url_stats()
        if stats:
            print("\nURL Statistics:")
            print("-" * 30)
            print(f"Total combinations: {stats['total_combinations']}")
            print(f"Total URLs: {stats['total_urls']}")
            print(f"Total pages scraped: {stats['total_pages']}")

            print("\nBy Borough:")
            for borough, data in stats['by_borough'].items():
                print(f"  {borough}: {data['urls']} URLs from {data['pages']} pages")

            print("\nBy Source:")
            for source, data in stats['by_source'].items():
                print(f"  {source}: {data['urls']} URLs from {data['pages']} pages")
        else:
            print("No statistics available (URLs file not found or corrupted)")
        print()

    # Export URLs list if requested
    if args.export_list:
        print(f"Exporting URLs to {args.export_list}...")
        success = scraper.export_urls_list(args.export_list)
        if success:
            print("URLs exported successfully!")
        else:
            print("Failed to export URLs")
        print()

    # Extract URLs
    print("Starting URL extraction...")
    success = scraper.extract_all_urls(force_refresh=args.force_refresh)

    end_time = time.time()
    processing_time = end_time - start_time

    if success:
        print()
        print("=" * 50)
        print("URL EXTRACTION COMPLETED!")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Results saved to: {args.output}")
        print("=" * 50)
        return 0
    else:
        print("URL extraction failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
