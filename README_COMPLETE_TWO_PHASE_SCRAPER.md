# Complete Two-Phase Escort Profile Scraper

This document provides comprehensive documentation for the complete two-phase scraper system that combines URL extraction from search pages (Phase 1) and detailed data extraction from individual profiles (Phase 2) into a single integrated workflow.

## 📋 Table of Contents

1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Features](#features)
4. [Installation & Setup](#installation--setup)
5. [Quick Start Guide](#quick-start-guide)
6. [Usage Examples](#usage-examples)
7. [Phase 1: URL Extraction](#phase-1-url-extraction)
8. [Phase 2: Data Extraction](#phase-2-data-extraction)
9. [Configuration Options](#configuration-options)
10. [Output Files](#output-files)
11. [Progress Management](#progress-management)
12. [Troubleshooting](#troubleshooting)
13. [File Structure](#file-structure)

## 🔍 Overview

The Complete Two-Phase Scraper is an integrated system that automates the entire process of:

1. **Phase 1**: Extracting profile URLs from escort website search pages
2. **Phase 2**: Extracting detailed information from individual profile pages

This system consolidates the previously separate `fresh_url_extractor.py` (Phase 1) and various Phase 2 extraction scripts into a single, streamlined workflow.

### Key Benefits

- **Unified Workflow**: Single script handles both phases seamlessly
- **Intelligent Resume**: Can resume from interruptions in either phase
- **Flexible Execution**: Run both phases together or Phase 2 only
- **Progress Tracking**: Real-time progress saving and checkpoints
- **Duplicate Detection**: Cross-profile phone number deduplication
- **Configurable Settings**: Customizable for different use cases

## 🏗 System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                 COMPLETE TWO-PHASE SCRAPER                 │
├─────────────────────────────────────────────────────────────┤
│  PHASE 1: URL EXTRACTION                                    │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │  Search Pages   │───▶│   Profile URLs  │               │
│  │  (aaok/aypapi)  │    │   JSON Files    │               │
│  └─────────────────┘    └─────────────────┘               │
│           │                       │                        │
│           ▼                       ▼                        │
│  PHASE 2: DATA EXTRACTION                                  │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │  Profile Pages  │───▶│  Extracted Data │               │
│  │  (Individual)   │    │   Excel Files   │               │
│  └─────────────────┘    └─────────────────┘               │
└─────────────────────────────────────────────────────────────┘
```

## ✨ Features

### Phase 1 Features
- **Multi-Source Scraping**: Extracts URLs from aaok and aypapi domains
- **Geographic Targeting**: Supports multiple cities and states
- **Age Pre-filtering**: Filters profiles by estimated age (≤30) when possible
- **API Integration**: Uses ScraperAPI for reliable page fetching
- **Validation**: Validates extracted URLs for correctness

### Phase 2 Features
- **Comprehensive Data Extraction**: 
  - Phone numbers (formatted as 1xxxxxxxxxx)
  - Names, ages, locations
  - Social media accounts
  - Full profile descriptions
- **Duplicate Prevention**: Phone-based deduplication across all profiles
- **Progress Saving**: Automatic checkpoints every 500 URLs
- **Resume Capability**: Continue from interruption points
- **Error Handling**: Robust error handling with detailed logging

### Integration Features
- **Seamless Workflow**: Automatic transition from Phase 1 to Phase 2
- **Flexible Execution**: Run complete workflow or Phase 2 only
- **Interactive Mode**: Guided setup with user-friendly prompts
- **Test Mode**: Limited-scope runs for testing and validation

## 🛠 Installation & Setup

### Prerequisites

- Python 3.7+
- Internet connection
- ScraperAPI account (for Phase 1)

### Install Dependencies

```bash
pip install -r requirements.txt
```

### Required Packages

```
requests==2.31.0
beautifulsoup4==4.12.2
pandas==2.1.4
openpyxl==3.1.2
lxml==4.9.3
```

### API Configuration

The scraper uses ScraperAPI for Phase 1. The API key is included in the script, but you can modify it if needed:

```python
scraperapi_key = "********************************"
```

## 🚀 Quick Start Guide

### Method 1: Interactive Mode (Recommended for beginners)

```bash
python run_complete_scraper.py
```

This launches an interactive guide that walks you through all options.

### Method 2: Complete Workflow (Both Phases)

```bash
# Run both phases for all default cities
python run_complete_scraper.py --cities "Baltimore,Philadelphia,Pittsburgh"

# Custom settings
python run_complete_scraper.py --max-pages 20 --delay 1.5 --workers 3
```

### Method 3: Phase 2 Only (Use existing URLs)

```bash
# Use most recent URL file
python run_complete_scraper.py --phase2-only

# Use specific URL file
python run_complete_scraper.py --phase2-only --existing-json fresh_all_urls_20250818_234554.json
```

### Method 4: Test Mode (Limited scope for testing)

```bash
python run_complete_scraper.py --test-mode
```

## 📚 Usage Examples

### Complete Workflow Examples

```bash
# Basic complete workflow - all cities, default settings
python run_complete_scraper.py

# Specific cities with custom page limit
python run_complete_scraper.py --cities "Baltimore,Philadelphia" --max-pages 10

# Complete workflow with custom Phase 2 settings
python run_complete_scraper.py --delay 2.0 --workers 2 --checkpoint-interval 250
```

### Phase 2 Only Examples

```bash
# Use existing URLs with default settings
python run_complete_scraper.py --phase2-only

# Specific file with custom settings
python run_complete_scraper.py --phase2-only \
  --existing-json phase1_all_urls_20250119_143022.json \
  --delay 1.5 --workers 4
```

### Advanced Usage

```bash
# Direct script usage (bypass runner)
python complete_two_phase_scraper.py --cities "Baltimore,Philadelphia" --max-pages 15

# Phase 2 only with direct script
python complete_two_phase_scraper.py --phase2-only --existing-json urls.json
```

## 📡 Phase 1: URL Extraction

### Process Overview

1. **Search Page Scraping**: Fetches search results from escort websites
2. **URL Extraction**: Parses HTML to extract profile URLs
3. **Age Filtering**: Filters profiles by estimated age when available
4. **Validation**: Validates URL format and domain
5. **Storage**: Saves URLs to JSON files

### Target Cities

Default cities (customizable):
- South New Jersey, New Jersey
- Philadelphia, Pennsylvania
- Pittsburgh, Pennsylvania
- Wilmington, Delaware
- Dover, Delaware
- Baltimore, Maryland
- Annapolis, Maryland

### Search Sources

- **aaok.com.listcrawler.eu**: Primary source for escort profiles
- **aypapi.com.listcrawler.eu**: Secondary source for escort profiles

### Output Files

```
phase1_urls_[City]_[State].json          # Individual city results
phase1_all_urls_[timestamp].json         # Combined results from all cities
```

## 📊 Phase 2: Data Extraction

### Extracted Data Fields

| Field | Description | Format | Example |
|-------|-------------|--------|---------|
| url | Original profile URL | String | https://aaok.com.listcrawler.eu/post/... |
| phone_number | Normalized phone number | 1xxxxxxxxxx | *********** |
| name | Extracted name | String | Sarah |
| location | Specific location | String | Downtown Baltimore |
| city | City from URL data | String | Baltimore |
| state | State from URL data | String | Maryland |
| age | Age in years | Integer | 28 |
| social_media | Social accounts found | String | Instagram: user123; OnlyFans: user456 |
| raw_text | Full profile description | String | My name is Sarah... |
| status | Processing status | success/failed/skipped_duplicate | success |
| error | Error message if failed | String | Request timeout |
| duplicate_phone | Is duplicate phone | Boolean | false |
| extracted_at | Extraction timestamp | String | 2025-01-19 14:30:45 |

### Phone Number Normalization

All phone numbers are standardized to: **1xxxxxxxxxx** (11 digits starting with 1)

Examples:
- `************` → `***********`
- `(*************` → `***********`
- `****** 123 4567` → `***********`

## ⚙️ Configuration Options

### Phase 1 Settings

| Parameter | Default | Description |
|-----------|---------|-------------|
| max_pages | 15 | Maximum pages to scrape per city/source |
| scraperapi_key | (included) | API key for ScraperAPI service |

### Phase 2 Settings

| Parameter | Default | Description |
|-----------|---------|-------------|
| delay | 1.0 | Seconds between requests |
| max_workers | 5 | Number of concurrent workers |
| checkpoint_interval | 500 | Save progress every N URLs |

### Test Mode Adjustments

When `--test-mode` is used:
- delay: 2.0 seconds (slower for stability)
- max_workers: 3 (fewer concurrent requests)
- checkpoint_interval: 100 (more frequent saves)
- max_pages: 3 (limited pages per source)

## 📁 Output Files

### Phase 1 Output Files

```
phase1_urls_Baltimore_Maryland.json                 # City-specific URLs
phase1_urls_Philadelphia_Pennsylvania.json          # City-specific URLs
...
phase1_all_urls_20250119_143022.json               # Combined all cities
```

### Phase 2 Output Files

```
complete_phase2_final_results_20250119_151530.xlsx  # Final results
phase2_checkpoint_500_urls_20250119_150045.xlsx     # Checkpoint files
phase2_checkpoint_1000_urls_20250119_150315.xlsx    # Checkpoint files
...
```

### Progress Files

```
phase2_progress_state_20250119_150045.json          # Resume state
complete_two_phase_scraper.log                      # Detailed logs
```

## 💾 Progress Management

### Automatic Checkpoints

The system automatically saves progress:

- **Phase 1**: After each city is completed
- **Phase 2**: Every 500 URLs (configurable)
- **Final Results**: At completion of each phase

### Resume Functionality

If the scraper is interrupted:

1. **Phase 1**: Restart and it will continue from the next unprocessed city
2. **Phase 2**: Automatically resumes from the last checkpoint

### Manual Progress Control

```bash
# View current progress files
ls -la phase2_progress_state_*.json
ls -la phase2_checkpoint_*.xlsx

# Clean up progress (start fresh)
rm -f phase2_progress_state_*.json
rm -f phase2_checkpoint_*.xlsx
```

## 🐛 Troubleshooting

### Common Issues

#### 1. **No URLs Found in Phase 1**

```
Phase 1 complete but no URLs extracted
```

**Causes & Solutions:**
- Check internet connection
- Verify ScraperAPI key is valid
- Reduce max_pages if timeouts occur
- Check if target websites are accessible

#### 2. **Phase 2 Memory Issues**

```
MemoryError during Phase 2 processing
```

**Solutions:**
```bash
# Reduce concurrent workers
python run_complete_scraper.py --workers 2

# Increase checkpoint frequency
python run_complete_scraper.py --checkpoint-interval 250
```

#### 3. **Connection Errors**

```
Multiple request timeouts or connection errors
```

**Solutions:**
```bash
# Increase delay between requests
python run_complete_scraper.py --delay 2.0

# Reduce concurrent workers
python run_complete_scraper.py --workers 2
```

#### 4. **Resume Not Working**

**Check for required files:**
```bash
ls -la phase2_progress_state_*.json
ls -la phase1_all_urls_*.json
```

**If files are missing:**
- Phase 1: Re-run the complete workflow
- Phase 2: Use `--existing-json` with a valid URL file

#### 5. **Permission Errors**

```
Permission denied writing to Excel file
```

**Solutions:**
- Close any open Excel files
- Ensure write permissions in the directory
- Run from a directory you own

### Debug Mode

Enable detailed logging by modifying the script:

```python
logging.basicConfig(level=logging.DEBUG)  # Change from INFO
```

### Performance Optimization

#### For Speed:
```bash
python run_complete_scraper.py --workers 8 --delay 0.5
```

#### For Stability:
```bash
python run_complete_scraper.py --workers 2 --delay 2.0
```

## 📁 File Structure

```
project/
├── complete_two_phase_scraper.py          # Main scraper script
├── run_complete_scraper.py                # User-friendly runner
├── README_COMPLETE_TWO_PHASE_SCRAPER.md   # This documentation
├── requirements.txt                       # Python dependencies
├── 
├── INPUT FILES (created by Phase 1):
├── phase1_urls_[City]_[State].json        # City-specific URLs
├── phase1_all_urls_[timestamp].json       # Combined URL files
├── 
├── OUTPUT FILES (created by Phase 2):
├── complete_phase2_final_results_[timestamp].xlsx  # Final results
├── 
├── PROGRESS FILES:
├── phase2_progress_state_[timestamp].json # Resume state
├── phase2_checkpoint_[N]_urls_[timestamp].xlsx # Checkpoint data
├── complete_two_phase_scraper.log         # Detailed logs
└── run_complete_scraper.log               # Runner logs
```

## 🎯 Best Practices

### 1. **Start with Test Mode**

Always test with limited scope first:
```bash
python run_complete_scraper.py --test-mode
```

### 2. **Monitor System Resources**

- Watch CPU and memory usage during large runs
- Ensure sufficient disk space for output files
- Monitor network bandwidth usage

### 3. **Use Appropriate Delays**

- Start with default delay (1.0 second)
- Increase if getting frequent errors
- Decrease only if connection is very stable

### 4. **Backup Important Files**

```bash
# Backup URL files
cp phase1_all_urls_*.json backups/

# Backup progress files
cp phase2_progress_state_*.json backups/
```

### 5. **Regular Checkpoint Verification**

```bash
# Check if checkpoint files are being created
ls -la phase2_checkpoint_*.xlsx

# Verify checkpoint file integrity
python -c "import pandas as pd; print(len(pd.read_excel('latest_checkpoint.xlsx')))"
```

## 📊 Performance Expectations

### Phase 1 Performance

| Cities | Pages/Source | Est. Time | Est. URLs |
|--------|--------------|-----------|-----------|
| 1 city | 15 pages | 15-30 min | 200-500 |
| 3 cities | 15 pages | 45-90 min | 600-1500 |
| 7 cities (default) | 15 pages | 2-3 hours | 1500-3500 |

### Phase 2 Performance

| URLs | Workers | Delay | Est. Time | Success Rate |
|------|---------|-------|-----------|--------------|
| 1,000 | 5 | 1.0s | 45-60 min | 85-95% |
| 2,000 | 5 | 1.0s | 90-120 min | 85-95% |
| 5,000 | 5 | 1.0s | 4-5 hours | 85-95% |

### Factors Affecting Performance

- **Network speed and stability**
- **Website response times**
- **System resources (CPU, memory)**
- **Number of concurrent workers**
- **Delay between requests**

## 📞 Support & Maintenance

### Regular Maintenance Tasks

1. **Clean up old files**:
   ```bash
   # Remove old checkpoints (keep recent ones)
   find . -name "phase2_checkpoint_*.xlsx" -mtime +7 -delete
   
   # Archive old results
   mkdir -p archives/$(date +%Y-%m)
   mv *_results_*.xlsx archives/$(date +%Y-%m)/
   ```

2. **Monitor disk usage**:
   ```bash
   # Check disk space
   df -h .
   
   # Check file sizes
   du -sh *.xlsx *.json
   ```

3. **Update dependencies**:
   ```bash
   pip install --upgrade -r requirements.txt
   ```

### Log Analysis

```bash
# View recent errors
grep -i error complete_two_phase_scraper.log | tail -20

# Check success rates
grep -c "Successfully extracted" complete_two_phase_scraper.log
grep -c "Error extracting" complete_two_phase_scraper.log

# Monitor progress
tail -f complete_two_phase_scraper.log
```

---

**Last Updated**: January 19, 2025  
**Version**: 1.0  
**Compatibility**: Python 3.7+  
**Dependencies**: See requirements.txt