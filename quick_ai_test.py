#!/usr/bin/env python3
"""
Quick AI test - tests just a few dedicated pages with Mistral AI enhancement
"""

import sys
from pathlib import Path
from web_scraper import WebScraper

def quick_ai_test():
    """Test AI enhancement with just 3 dedicated pages"""
    print("Quick AI Enhancement Test")
    print("=" * 50)
    
    # Use your Mistral API key
    scraper = WebScraper(mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso")
    scraper.request_delay = 0.5  # Faster for testing
    
    try:
        # Get first city-source combination
        cities = scraper.parse_url_list()
        if not cities:
            print("✗ No cities available for testing")
            return False
        
        first_city = cities[0]
        print(f"Testing with: {first_city['city']}, {first_city['state']} from {first_city['source']}")
        
        # Get search page (just page 1)
        search_html = scraper.execute_curl_request(first_city['url'], scraper.search_curl_template)
        if not search_html:
            print("✗ Failed to get search page")
            return False
        
        # Extract URLs (filtered by age ≤30)
        dedicated_urls = scraper.extract_dedicated_urls(search_html)
        if not dedicated_urls:
            print("✗ No dedicated URLs found for age ≤30")
            return False
        
        print(f"Found {len(dedicated_urls)} dedicated URLs for age ≤30")
        
        # Test just the first 3 dedicated pages
        test_urls = dedicated_urls[:3]
        city_data = []
        
        for i, url in enumerate(test_urls):
            print(f"\nTesting dedicated page {i+1}/3...")
            print(f"URL: {url}")
            
            # Get page
            html = scraper.execute_curl_request(url, scraper.dedicated_curl_template)
            if html:
                data = scraper.extract_dedicated_page_data(html, url)
                if data:  # This will be None if not a woman
                    data['city'] = first_city['city']
                    data['state'] = first_city['state']
                    data['source'] = first_city['source']
                    city_data.append(data)
                    
                    print(f"  ✓ Extracted (woman): {data.get('title', 'No title')[:50]}...")
                    print(f"  Age: {data.get('age', 'N/A')}")
                    print(f"  Name: {data.get('name', 'N/A')}")
                    print(f"  Phone: {data.get('phone', 'N/A')}")
                    if data.get('description'):
                        desc = data['description'][:100] + "..." if len(data['description']) > 100 else data['description']
                        print(f"  Description: {desc}")
                else:
                    print(f"  ✗ Skipped (not a woman or failed extraction)")
            else:
                print(f"  ✗ Failed to get page")
        
        if city_data:
            # Save test results
            scraper.scraped_data = city_data
            scraper.save_to_excel("quick_ai_test_output.xlsx")
            
            print(f"\n✓ Successfully scraped {len(city_data)} women ≤30 years with AI enhancement")
            print("✓ Test data saved to quick_ai_test_output.xlsx")
            
            return True
        else:
            print("✗ No women ≤30 years found in test pages")
            return False
            
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        return False

def main():
    """Run quick AI test"""
    success = quick_ai_test()
    
    if success:
        print("\n" + "=" * 50)
        print("✓ Quick AI test PASSED! Mistral AI enhancement is working.")
        print("\nFeatures validated:")
        print("  ✓ aaok.com as main source (no escortalligator)")
        print("  ✓ Age ≤30 years filtering")
        print("  ✓ Women only filtering")
        print("  ✓ Mistral AI enhanced text extraction")
        print("\nTo run full production:")
        print("  python web_scraper.py --max-cities 5  # Test with 5 city-source combinations")
        print("  python web_scraper.py                 # Full scrape (all 374 combinations)")
        return 0
    else:
        print("\n" + "=" * 50)
        print("✗ Quick AI test FAILED. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
