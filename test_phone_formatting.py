#!/usr/bin/env python3
"""
Test script for phone number formatting in Phase2 batch extractor
"""

import sys
import os

# Add current directory to path to import the extractor
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from phase2_batch_extractor import Phase2BatchExtractor

def test_phone_formatting():
    """Test phone number formatting function"""
    extractor = Phase2BatchExtractor()

    test_cases = [
        # Input phone, Expected formatted output
        ("************", "15551234567"),
        ("(*************", "15551234567"),
        ("************", "15551234567"),
        ("5551234567", "15551234567"),
        ("1-************", "15551234567"),
        ("1 (*************", "15551234567"),
        ("****** 123 4567", "15551234567"),
        ("15551234567", "15551234567"),
        ("************", "12094502658"),
        ("************", "16028537752"),
        ("************", "14167002049"),
        ("************", "14058880702"),
        ("", ""),
        (None, ""),
        ("123", "123"),  # Less than 10 digits, return original
        ("12345678901", "12345678901"),  # 11 digits: take last 10 "2345678901", add 1: "12345678901"
        ("123456789012", "13456789012"),  # 12 digits: take last 10 "3456789012", add 1: "13456789012"
    ]

    print("Testing phone number formatting:")
    print("Input Phone Number".ljust(20) + " -> " + "Formatted Output")
    print("-" * 50)

    all_passed = True
    for input_phone, expected in test_cases:
        result = extractor.format_phone(input_phone)
        status = "✓" if result == expected else "✗"

        input_display = str(input_phone) if input_phone else "None"
        print(f"{input_display:<20} -> {result:<15} {status}")

        if result != expected:
            print(f"  Expected: {expected}")
            all_passed = False

    print("\n" + "="*50)
    if all_passed:
        print("✓ All phone formatting tests PASSED!")
    else:
        print("✗ Some phone formatting tests FAILED!")

    return all_passed

def test_duplicate_detection():
    """Test duplicate phone number detection"""
    extractor = Phase2BatchExtractor()

    test_phones = [
        "************",    # First occurrence
        "(*************",  # Should be detected as duplicate
        "************",    # Should be detected as duplicate
        "************",    # Different number
        "************",    # Should be detected as duplicate
        "************",    # Different number
        "************",    # Should be detected as duplicate
    ]

    print("\nTesting duplicate phone detection:")
    print("Phone Number".ljust(20) + " -> " + "Status")
    print("-" * 40)

    for phone in test_phones:
        formatted = extractor.format_phone(phone)
        is_duplicate = extractor.is_phone_duplicate(formatted)
        status = "DUPLICATE" if is_duplicate else "NEW"
        print(f"{phone:<20} -> {status} ({formatted})")

    print(f"\nUnique phones seen: {len(extractor.seen_phones)}")
    print("Seen phones:", list(extractor.seen_phones))

def test_normalize_vs_format():
    """Test the difference between normalize_phone and format_phone"""
    extractor = Phase2BatchExtractor()

    test_phones = [
        "************",
        "(*************",
        "+1-************",
        "15551234567",
        "************"
    ]

    print("\nTesting normalize vs format:")
    print("Input Phone".ljust(20) + " | " + "Normalized".ljust(12) + " | " + "Formatted")
    print("-" * 55)

    for phone in test_phones:
        normalized = extractor.normalize_phone(phone)
        formatted = extractor.format_phone(phone)
        print(f"{phone:<20} | {normalized:<12} | {formatted}")

def main():
    """Run all tests"""
    print("=" * 60)
    print("PHONE NUMBER FORMATTING TESTS")
    print("=" * 60)

    # Test 1: Phone formatting
    test_phone_formatting()

    # Test 2: Duplicate detection
    test_duplicate_detection()

    # Test 3: Normalize vs Format comparison
    test_normalize_vs_format()

    print("\n" + "=" * 60)
    print("Testing complete!")

if __name__ == '__main__':
    main()
