#!/usr/bin/env python3
"""
Simple 10 URL Test - Check current scraping results
Tests 10 URLs starting from position 200 to see what we get
"""

import json
import os
import requests
import time
import random
from datetime import datetime

def test_direct_api_call(api_key, url):
    """Test direct API call to ScrapingDog"""

    # Try with basic settings first
    params = {
        'api_key': api_key,
        'url': url,
        'dynamic': 'false',
    }

    try:
        response = requests.get('https://api.scrapingdog.com/scrape', params=params, timeout=30)
        return response.status_code, response.text
    except Exception as e:
        return None, str(e)

def analyze_content(html_content):
    """Analyze if content looks like real profile or abuse page"""
    if not html_content:
        return "No content"

    html_lower = html_content.lower()

    # Check for abuse page indicators
    abuse_indicators = [
        "report abuse",
        "× report abuse",
        "content/photos/phone scam/fake",
        "must authenticate your email",
        "escort south jersey | listing"
    ]

    abuse_found = any(indicator in html_lower for indicator in abuse_indicators)

    # Check for real profile indicators
    profile_indicators = [
        "description",
        "contact",
        "phone",
        "age",
        "location"
    ]

    profile_found = any(indicator in html_lower for indicator in profile_indicators)

    content_length = len(html_content)

    if abuse_found and content_length < 2000:
        return f"Abuse page ({content_length} chars)"
    elif profile_found and content_length > 1000:
        return f"Real content ({content_length} chars)"
    elif content_length < 500:
        return f"Too short ({content_length} chars)"
    else:
        return f"Unknown ({content_length} chars)"

def main():
    """Main test function"""

    print("🧪 Simple 10 URL Test")
    print("=" * 40)

    # Load URLs file
    urls_file = "targeted_all_urls_20250819_001221.json"

    if not os.path.exists(urls_file):
        print(f"❌ URLs file {urls_file} not found!")
        return

    try:
        with open(urls_file, 'r', encoding='utf-8') as f:
            all_urls = json.load(f)
    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return

    # Test URLs from position 200 (since we processed 0-199)
    start_pos = 200
    test_urls = all_urls[start_pos:start_pos + 10]

    print(f"📂 Testing 10 URLs from positions {start_pos}-{start_pos + 9}")
    print(f"   Total URLs available: {len(all_urls)}")

    # Try different API keys
    api_keys = [
        "68a38c83d2e5dbc2ff118e5c",  # First key (likely exhausted)
        "68a390dbc2920968e9acce34",  # Second key (likely exhausted)
    ]

    results = []

    for i, url_data in enumerate(test_urls, 1):
        print(f"\n📋 Testing URL {i}/10")
        print(f"   URL: {url_data['url']}")

        success = False

        # Try each API key
        for j, api_key in enumerate(api_keys, 1):
            print(f"   🔑 Trying API key {j}: ...{api_key[-8:]}")

            status_code, content = test_direct_api_call(api_key, url_data['url'])

            if status_code == 200:
                analysis = analyze_content(content)
                print(f"   ✅ Success: {analysis}")

                results.append({
                    'position': start_pos + i - 1,
                    'url': url_data['url'],
                    'api_key': f"...{api_key[-8:]}",
                    'status': 'success',
                    'content_analysis': analysis,
                    'content_length': len(content) if content else 0,
                    'first_100_chars': content[:100] if content else ""
                })
                success = True
                break

            elif status_code == 403:
                print(f"   ❌ API key exhausted (403)")

            elif status_code == 404:
                print(f"   ❌ URL not found (404)")

            else:
                print(f"   ❌ Error {status_code}: {content[:100] if content else 'No content'}...")

            # Small delay between API key attempts
            time.sleep(1)

        if not success:
            results.append({
                'position': start_pos + i - 1,
                'url': url_data['url'],
                'api_key': 'none',
                'status': 'failed',
                'content_analysis': 'All API keys failed',
                'content_length': 0,
                'first_100_chars': ""
            })

        # Delay between URLs
        time.sleep(random.uniform(1, 3))

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"test_10_simple_results_{timestamp}.json"

    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Summary
    print(f"\n📊 TEST RESULTS SUMMARY")
    print("=" * 30)

    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] == 'failed']

    print(f"✅ Successful: {len(successful)}/10")
    print(f"❌ Failed: {len(failed)}/10")
    print(f"📈 Success rate: {(len(successful)/10)*100:.1f}%")

    if successful:
        print(f"\n📄 SUCCESSFUL RESULTS:")
        for result in successful:
            print(f"   Position {result['position']}: {result['content_analysis']}")
            print(f"      First 100 chars: {result['first_100_chars']}...")
            print()

        # Content quality analysis
        real_content = [r for r in successful if 'Real content' in r['content_analysis']]
        abuse_pages = [r for r in successful if 'Abuse page' in r['content_analysis']]

        print(f"📈 CONTENT QUALITY:")
        print(f"   ✅ Real content: {len(real_content)}")
        print(f"   ⚠️ Abuse pages: {len(abuse_pages)}")
        print(f"   🤔 Unknown: {len(successful) - len(real_content) - len(abuse_pages)}")

    print(f"\n💾 Results saved to: {results_file}")

    # Check if we can continue
    if len(successful) > 0:
        print(f"\n🎯 RECOMMENDATION:")
        if len(real_content) > len(abuse_pages):
            print(f"   ✅ Good success rate with real content!")
            print(f"   🚀 Scraping appears to be working")
        else:
            print(f"   ⚠️ Getting mostly abuse pages")
            print(f"   🔧 May need better anti-bot measures")
    else:
        print(f"\n🔋 API KEYS STATUS:")
        print(f"   ❌ All API keys appear to be exhausted")
        print(f"   🔑 Need fresh API key to continue")

if __name__ == "__main__":
    main()
