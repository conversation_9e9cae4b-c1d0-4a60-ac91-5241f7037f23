#!/usr/bin/env python3
"""
Test Script for Complete Two-Phase Scraper

This script tests the functionality of the complete two-phase scraper
with a limited scope to verify everything works correctly.

Usage:
    python test_complete_scraper.py
"""

import os
import json
import sys
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required modules can be imported"""
    logger.info("Testing imports...")

    try:
        import requests
        import pandas as pd
        from bs4 import BeautifulSoup
        import openpyxl
        logger.info("✅ All required modules imported successfully")
        return True
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.info("Install missing dependencies with: pip install -r requirements.txt")
        return False

def test_scraper_class():
    """Test that the scraper class can be instantiated"""
    logger.info("Testing scraper class instantiation...")

    try:
        from complete_two_phase_scraper import CompleteTwoPhaseScraper
        scraper = CompleteTwoPhaseScraper()
        logger.info("✅ Scraper class instantiated successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Error instantiating scraper: {e}")
        return False

def create_test_urls_file():
    """Create a small test JSON file with a few URLs for testing"""
    logger.info("Creating test URLs file...")

    test_urls = [
        {
            "url": "https://aaok.com.listcrawler.eu/post/escorts/usa/maryland/baltimore/123456789",
            "city": "Baltimore",
            "state": "Maryland",
            "source": "aaok",
            "page": 1,
            "estimated_age": None
        },
        {
            "url": "https://aypapi.com.listcrawler.eu/post/escorts/usa/pennsylvania/philadelphia/987654321",
            "city": "Philadelphia",
            "state": "Pennsylvania",
            "source": "aypapi",
            "page": 1,
            "estimated_age": 25
        },
        {
            "url": "https://aaok.com.listcrawler.eu/post/escorts/usa/delaware/wilmington/456789123",
            "city": "Wilmington",
            "state": "Delaware",
            "source": "aaok",
            "page": 1,
            "estimated_age": None
        }
    ]

    test_file = "test_urls_sample.json"

    try:
        with open(test_file, 'w') as f:
            json.dump(test_urls, f, indent=2)
        logger.info(f"✅ Test URLs file created: {test_file}")
        return test_file
    except Exception as e:
        logger.error(f"❌ Error creating test file: {e}")
        return None

def test_phase2_extraction(test_file):
    """Test Phase 2 extraction with limited URLs"""
    logger.info("Testing Phase 2 extraction...")

    try:
        from complete_two_phase_scraper import CompleteTwoPhaseScraper

        # Create scraper with test settings
        scraper = CompleteTwoPhaseScraper(
            delay=2.0,  # Slower for testing
            max_workers=1,  # Single worker for testing
            checkpoint_interval=2  # Checkpoint every 2 URLs
        )

        # Test data extraction on a few URLs
        logger.info("Starting Phase 2 test extraction...")
        results_file = scraper.phase2_extract_data(test_file)

        if results_file and os.path.exists(results_file):
            logger.info(f"✅ Phase 2 test completed successfully: {results_file}")
            return True
        else:
            logger.error("❌ Phase 2 test failed - no results file created")
            return False

    except Exception as e:
        logger.error(f"❌ Phase 2 test error: {e}")
        return False

def test_url_validation():
    """Test URL validation functionality"""
    logger.info("Testing URL validation...")

    try:
        from complete_two_phase_scraper import CompleteTwoPhaseScraper
        scraper = CompleteTwoPhaseScraper()

        # Test valid URLs
        valid_urls = [
            "https://aaok.com.listcrawler.eu/post/escorts/usa/maryland/baltimore/123456789",
            "https://aypapi.com.listcrawler.eu/post/escorts/usa/pennsylvania/philadelphia/987654321"
        ]

        # Test invalid URLs
        invalid_urls = [
            "https://google.com",
            "https://escortalligator.com/post/escorts/usa/maryland/baltimore/123456789",
            "not_a_url",
            ""
        ]

        # Test valid URLs
        for url in valid_urls:
            if not scraper.validate_url(url):
                logger.error(f"❌ Valid URL failed validation: {url}")
                return False

        # Test invalid URLs
        for url in invalid_urls:
            if scraper.validate_url(url):
                logger.error(f"❌ Invalid URL passed validation: {url}")
                return False

        logger.info("✅ URL validation tests passed")
        return True

    except Exception as e:
        logger.error(f"❌ URL validation test error: {e}")
        return False

def test_phone_normalization():
    """Test phone number normalization"""
    logger.info("Testing phone number normalization...")

    try:
        from complete_two_phase_scraper import CompleteTwoPhaseScraper
        scraper = CompleteTwoPhaseScraper()

        test_cases = [
            ("************", "15551234567"),
            ("(*************", "15551234567"),
            ("****** 123 4567", "15551234567"),
            ("5551234567", "15551234567"),
            ("**************", "15551234567"),
            ("", ""),
            ("123", "123"),
            (None, "")
        ]

        for input_phone, expected in test_cases:
            result = scraper.normalize_phone(input_phone) if input_phone is not None else scraper.normalize_phone("")
            if result != expected:
                logger.error(f"❌ Phone normalization failed: '{input_phone}' -> '{result}' (expected '{expected}')")
                return False

        logger.info("✅ Phone normalization tests passed")
        return True

    except Exception as e:
        logger.error(f"❌ Phone normalization test error: {e}")
        return False

def test_file_operations():
    """Test file reading and writing operations"""
    logger.info("Testing file operations...")

    try:
        # Test JSON file operations
        test_data = {"test": "data", "timestamp": datetime.now().isoformat()}
        test_file = "test_temp_file.json"

        # Write test
        with open(test_file, 'w') as f:
            json.dump(test_data, f)

        # Read test
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)

        if loaded_data != test_data:
            logger.error("❌ JSON file operations failed")
            return False

        # Clean up
        os.remove(test_file)

        # Test pandas Excel operations
        import pandas as pd

        test_df = pd.DataFrame({
            'url': ['https://example.com/1', 'https://example.com/2'],
            'phone_number': ['15551234567', '15559876543'],
            'name': ['Test1', 'Test2'],
            'status': ['success', 'success']
        })

        test_excel = "test_temp_file.xlsx"
        test_df.to_excel(test_excel, index=False)

        loaded_df = pd.read_excel(test_excel)

        if len(loaded_df) != len(test_df):
            logger.error("❌ Excel file operations failed")
            return False

        # Clean up
        os.remove(test_excel)

        logger.info("✅ File operations tests passed")
        return True

    except Exception as e:
        logger.error(f"❌ File operations test error: {e}")
        return False

def check_existing_files():
    """Check for existing files that might interfere with tests"""
    logger.info("Checking for existing files...")

    patterns = [
        'test_urls_sample.json',
        'complete_phase2_final_results_*.xlsx',
        'phase2_progress_state_*.json',
        'phase2_checkpoint_*.xlsx'
    ]

    found_files = []
    for pattern in patterns:
        if '*' in pattern:
            # Handle wildcard patterns
            import glob
            matching_files = glob.glob(pattern)
            found_files.extend(matching_files)
        else:
            if os.path.exists(pattern):
                found_files.append(pattern)

    if found_files:
        logger.warning(f"Found existing files that may interfere with tests:")
        for file in found_files:
            logger.warning(f"  - {file}")

        response = input("Remove these files before testing? (y/n): ").strip().lower()
        if response == 'y':
            for file in found_files:
                try:
                    os.remove(file)
                    logger.info(f"Removed: {file}")
                except Exception as e:
                    logger.warning(f"Could not remove {file}: {e}")

    return True

def run_full_test_suite():
    """Run the complete test suite"""
    logger.info("=" * 60)
    logger.info("COMPLETE TWO-PHASE SCRAPER - TEST SUITE")
    logger.info("=" * 60)

    tests = [
        ("Import Test", test_imports),
        ("File Operations Test", test_file_operations),
        ("Scraper Class Test", test_scraper_class),
        ("URL Validation Test", test_url_validation),
        ("Phone Normalization Test", test_phone_normalization),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")

    logger.info(f"\n" + "=" * 60)
    logger.info(f"TEST RESULTS: {passed}/{total} tests passed")
    logger.info("=" * 60)

    if passed == total:
        logger.info("🎉 ALL TESTS PASSED!")

        # Optional: Run a small Phase 2 extraction test
        response = input("\nRun Phase 2 extraction test with sample URLs? (y/n): ").strip().lower()
        if response == 'y':
            logger.info("\n--- Phase 2 Extraction Test ---")
            test_file = create_test_urls_file()
            if test_file:
                if test_phase2_extraction(test_file):
                    logger.info("✅ Phase 2 extraction test PASSED")
                    # Clean up test file
                    try:
                        os.remove(test_file)
                    except:
                        pass
                else:
                    logger.error("❌ Phase 2 extraction test FAILED")

        return True
    else:
        logger.error(f"💥 {total - passed} TEST(S) FAILED")
        logger.info("Please fix the issues before running the scraper.")
        return False

def main():
    """Main test function"""
    logger.info("Starting Complete Two-Phase Scraper Test Suite...")

    # Check for existing files
    check_existing_files()

    # Run tests
    success = run_full_test_suite()

    if success:
        logger.info("\n🎯 All tests passed! The scraper should work correctly.")
        logger.info("You can now run the complete scraper with:")
        logger.info("  python run_complete_scraper.py --test-mode")
    else:
        logger.error("\n💥 Some tests failed. Please fix the issues first.")
        sys.exit(1)

if __name__ == "__main__":
    main()
