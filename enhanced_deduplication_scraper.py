#!/usr/bin/env python3
"""
Enhanced ScraperAPI Scraper with Phone Number Deduplication
Checks if phone numbers are already scraped before processing URLs to avoid duplicates
"""

import requests
import time
import random
import threading
import json
import os
import pandas as pd
import re
from datetime import datetime
from typing import List, Dict, Optional, Set
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed

class EnhancedDeduplicationScraper:
    def __init__(self, api_keys: List[str], max_workers: int = 3, enable_social_media: bool = True, mistral_api_key: str = None):
        self.api_keys = api_keys
        self.current_key_index = 0
        self.requests_with_current_key = 0
        self.max_requests_per_key = 4500  # Switch to next key after 4500 requests
        self.key_lock = threading.Lock()  # Thread-safe key rotation

        self.max_workers = max_workers
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results = []
        self.results_lock = threading.Lock()
        self.processed_count = 0
        self.skipped_count = 0

        # Phone number deduplication
        self.scraped_phone_numbers: Set[str] = set()
        self.phone_lock = threading.Lock()

        # ScraperAPI settings
        self.scraperapi_url = "https://api.scraperapi.com/"

        # Social media extraction settings
        self.enable_social_media = enable_social_media
        self.mistral_api_key = mistral_api_key
        if self.enable_social_media and self.mistral_api_key:
            self.mistral_base_url = "https://api.mistral.ai/v1/chat/completions"
            self.mistral_headers = {
                "Authorization": f"Bearer {self.mistral_api_key}",
                "Content-Type": "application/json"
            }

        # Rate limiting
        self.min_delay = 0.5  # Minimum delay between requests
        self.max_delay = 1.5  # Maximum delay between requests
        self.last_request_time = {}  # Track last request time per worker

        # Load existing phone numbers from previous results
        self.load_existing_phone_numbers()

    def load_existing_phone_numbers(self):
        """Load phone numbers from existing result files to avoid duplicates"""
        print("🔍 Loading existing phone numbers from previous results...")

        # Look for existing Excel files with scraped results
        result_files = [f for f in os.listdir('.') if f.startswith('scraperapi_results_') and f.endswith('.xlsx')]

        total_loaded = 0
        for file_path in result_files:
            try:
                print(f"📂 Checking {file_path}...")
                df = pd.read_excel(file_path)

                if 'phone' in df.columns:
                    # Get valid phone numbers (not empty, not NaN)
                    valid_phones = df[df['phone'].notna() & (df['phone'] != '') & (df['phone'] != 'nan')]['phone']
                    new_phones = set(valid_phones.astype(str))

                    # Clean phone numbers (remove formatting)
                    cleaned_phones = set()
                    for phone in new_phones:
                        cleaned_phone = self.clean_phone_number(phone)
                        if cleaned_phone:
                            cleaned_phones.add(cleaned_phone)

                    # Add to our set
                    before_count = len(self.scraped_phone_numbers)
                    self.scraped_phone_numbers.update(cleaned_phones)
                    added_count = len(self.scraped_phone_numbers) - before_count

                    print(f"   📱 Found {len(new_phones)} phones, added {added_count} new unique numbers")
                    total_loaded += added_count

            except Exception as e:
                print(f"   ⚠️ Error loading {file_path}: {e}")
                continue

        print(f"✅ Loaded {total_loaded} existing phone numbers from {len(result_files)} files")
        print(f"📊 Total unique phone numbers to skip: {len(self.scraped_phone_numbers)}")

    def clean_phone_number(self, phone_str: str) -> Optional[str]:
        """Clean phone number for consistent comparison"""
        if not phone_str or pd.isna(phone_str):
            return None

        # Convert to string and remove all non-digits
        phone_clean = re.sub(r'[^\d]', '', str(phone_str))

        # Must be at least 10 digits for US phone numbers
        if len(phone_clean) < 10:
            return None

        # Normalize to 10 digits (remove country code if present)
        if len(phone_clean) == 11 and phone_clean.startswith('1'):
            phone_clean = phone_clean[1:]
        elif len(phone_clean) > 11:
            return None  # Invalid length

        return phone_clean

    def is_phone_already_scraped(self, phone: str) -> bool:
        """Check if phone number has already been scraped"""
        cleaned_phone = self.clean_phone_number(phone)
        if not cleaned_phone:
            return False

        with self.phone_lock:
            return cleaned_phone in self.scraped_phone_numbers

    def add_phone_to_scraped(self, phone: str):
        """Add phone number to scraped set"""
        cleaned_phone = self.clean_phone_number(phone)
        if cleaned_phone:
            with self.phone_lock:
                self.scraped_phone_numbers.add(cleaned_phone)

    def get_current_api_key(self) -> str:
        """Get current API key and rotate if needed"""
        with self.key_lock:
            # Check if we need to rotate to next key
            if self.requests_with_current_key >= self.max_requests_per_key:
                if self.current_key_index < len(self.api_keys) - 1:
                    self.current_key_index += 1
                    self.requests_with_current_key = 0
                    print(f"🔄 Rotated to API key {self.current_key_index + 1}/{len(self.api_keys)}")
                else:
                    print(f"⚠️  All API keys used up to limit. Continuing with last key...")

            # Increment request count
            self.requests_with_current_key += 1
            current_key = self.api_keys[self.current_key_index]
            return current_key

    def quick_phone_check(self, url: str, worker_id: str) -> Optional[str]:
        """Quickly fetch page and extract phone number for deduplication check"""
        try:
            # Rate limiting per worker
            current_time = time.time()
            if worker_id in self.last_request_time:
                time_since_last = current_time - self.last_request_time[worker_id]
                min_interval = random.uniform(0.2, 0.5)  # Faster for quick check
                if time_since_last < min_interval:
                    sleep_time = min_interval - time_since_last
                    time.sleep(sleep_time)

            self.last_request_time[worker_id] = time.time()

            # Get current API key
            api_key = self.get_current_api_key()

            # ScraperAPI parameters for quick check
            payload = {
                'api_key': api_key,
                'url': url,
                'render': 'false',
                'country_code': 'us',
                'session_number': random.randint(1, 1000)
            }

            # Make request with shorter timeout for quick check
            response = requests.get(
                self.scraperapi_url,
                params=payload,
                timeout=30
            )

            if response.status_code == 200:
                html = response.text

                # Quick phone extraction
                soup = BeautifulSoup(html, 'html.parser')
                phone_elem = soup.select_one('.viewposttelephone')

                if phone_elem:
                    phone_text = phone_elem.get_text(strip=True)
                    # Clean phone number
                    phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                    return phone_clean.strip() if phone_clean.strip() else None

            return None

        except Exception as e:
            print(f"Worker {worker_id}: ⚠️ Quick phone check failed for {url}: {e}")
            return None

    def fetch_with_scraperapi(self, url: str, worker_id: str) -> Optional[str]:
        """Fetch URL using ScraperAPI with proper error handling"""
        # Rate limiting per worker
        current_time = time.time()
        if worker_id in self.last_request_time:
            time_since_last = current_time - self.last_request_time[worker_id]
            min_interval = random.uniform(self.min_delay, self.max_delay)
            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                time.sleep(sleep_time)

        self.last_request_time[worker_id] = time.time()

        max_retries = 3
        for attempt in range(max_retries):
            try:
                # Get current API key (with rotation)
                api_key = self.get_current_api_key()

                print(f"Worker {worker_id}: Fetching {url} via ScraperAPI (attempt {attempt + 1}/{max_retries}) [Key {self.current_key_index + 1}]")

                # ScraperAPI parameters
                payload = {
                    'api_key': api_key,
                    'url': url,
                    'render': 'false',
                    'country_code': 'us',
                    'premium': 'true',
                    'session_number': random.randint(1, 1000)
                }

                # Make request to ScraperAPI
                response = requests.get(
                    self.scraperapi_url,
                    params=payload,
                    timeout=60
                )

                if response.status_code == 200:
                    html = response.text

                    if len(html) < 500:
                        print(f"Worker {worker_id}: Content too short ({len(html)} chars), retrying...")
                        if attempt < max_retries - 1:
                            time.sleep(random.uniform(5, 10))
                            continue

                    # Check for anti-bot protection
                    protection_indicators = [
                        "Just a moment",
                        "Enable JavaScript and cookies",
                        "Checking your browser",
                        "Please wait while we verify"
                    ]

                    if any(indicator in html for indicator in protection_indicators):
                        print(f"Worker {worker_id}: Anti-bot protection detected, retrying...")
                        if attempt < max_retries - 1:
                            time.sleep(random.uniform(10, 20))
                            continue

                    print(f"Worker {worker_id}: ✅ Successfully fetched {len(html)} characters")
                    return html

                else:
                    print(f"Worker {worker_id}: HTTP {response.status_code} - {response.reason}")
                    if attempt < max_retries - 1:
                        sleep_time = random.uniform(5, 15) * (attempt + 1)
                        print(f"Worker {worker_id}: Waiting {sleep_time:.1f}s before retry...")
                        time.sleep(sleep_time)

            except requests.exceptions.Timeout:
                print(f"Worker {worker_id}: Request timeout on attempt {attempt + 1}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(10, 20))

            except Exception as e:
                print(f"Worker {worker_id}: Request failed on attempt {attempt + 1}: {e}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(5, 15))

        print(f"Worker {worker_id}: ❌ All attempts failed for {url}")
        return None

    def extract_social_media(self, description: str) -> str:
        """Extract social media information from description using Mistral AI"""
        if not self.enable_social_media or not self.mistral_api_key or not description:
            return "None"

        try:
            prompt = f"""Extract social media usernames and platforms from this text.
Return ONLY the social media information in format "platform:username" separated by commas, or "None" if no social media found.

Text: {description[:500]}

Examples of good responses:
- "instagram:@username, twitter:@handle"
- "telegram:@username"
- "None"
"""

            payload = {
                "model": "mistral-small",
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": 100,
                "temperature": 0.1
            }

            response = requests.post(
                self.mistral_base_url,
                headers=self.mistral_headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                social_info = result['choices'][0]['message']['content'].strip()
                return social_info if social_info else "None"
            else:
                return "API Error"

        except Exception as e:
            print(f"❌ Social media extraction error: {e}")
            return "Error"

    def extract_data_from_html(self, html: str, url: str) -> Dict:
        """Extract profile data from HTML using proper selectors"""
        data = {
            'url': url,
            'name': '',
            'age': '',
            'phone': '',
            'location': '',
            'description': '',
            'website_type': '',
            'city': '',
            'social': '',
            'extraction_success': False,
            'scraped_at': datetime.now().isoformat(),
            'content_length': len(html)
        }

        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Detect website type
            domain = url.lower()
            if 'aypapi' in domain:
                data['website_type'] = 'aypapi'
            elif 'aaok' in domain:
                data['website_type'] = 'aaok'
            else:
                data['website_type'] = 'unknown'

            # Extract name
            name_elem = soup.select_one('.viewpostname')
            if name_elem:
                nym_span = name_elem.select_one('span')
                if nym_span:
                    name_text = name_elem.get_text(strip=True)
                    span_text = nym_span.get_text(strip=True)
                    if span_text in name_text:
                        extracted_name = name_text.replace(span_text, '').strip()
                        if extracted_name:
                            data['name'] = extracted_name
                else:
                    name_text = name_elem.get_text(strip=True)
                    if 'Nym:' in name_text:
                        extracted_name = name_text.replace('Nym:', '').strip()
                        if extracted_name:
                            data['name'] = extracted_name
                    elif name_text:
                        data['name'] = name_text

            # If name is still empty, try to extract from title
            if not data['name']:
                title_elem = soup.select_one('.viewposttitle')
                if title_elem:
                    title_text = title_elem.get_text(strip=True)
                    age_elem = soup.select_one('.postTitleAge')
                    if age_elem:
                        age = age_elem.get_text(strip=True)
                        title_without_age = title_text.replace(f' - {age}', '').replace(f'- {age}', '').replace(f' -{age}', '').replace(f'-{age}', '').strip()
                        if title_without_age:
                            data['name'] = title_without_age
                    else:
                        if title_text:
                            data['name'] = title_text[:100].strip()

            # Extract age
            age_elem = soup.select_one('.postTitleAge')
            if age_elem:
                age_text = age_elem.get_text(strip=True)
                if age_text.isdigit() and 18 <= int(age_text) <= 65:
                    data['age'] = age_text

            # Extract phone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                if phone_clean.strip():
                    data['phone'] = phone_clean.strip()

            # Extract location
            location_container = soup.select_one('.viewpostlocationIconBabylon ul')
            if location_container:
                li_elements = location_container.select('li')
                if len(li_elements) >= 2:
                    location_text = li_elements[1].get_text(strip=True)
                    if 'Location:' in location_text:
                        data['location'] = location_text.replace('Location:', '').strip()
                    else:
                        data['location'] = location_text

            # Extract description
            body_elem = soup.select_one('.viewpostbody')
            if body_elem:
                data['description'] = body_elem.get_text(strip=True)[:500]

            # Extract city from page title
            title = soup.title
            if title:
                page_title = title.get_text(strip=True)
                data['page_title'] = page_title
                words = page_title.split()
                if len(words) >= 2:
                    data['city'] = words[1]

            # Extract social media information
            if data['description'] and self.enable_social_media:
                data['social'] = self.extract_social_media(data['description'])
            else:
                data['social'] = "None"

            # Check if we got meaningful data
            if data['name'] or data['age'] or data['phone']:
                data['extraction_success'] = True

        except Exception as e:
            print(f"❌ Error extracting data from {url}: {e}")
            data['error'] = str(e)

        return data

    def process_url_batch(self, urls: List[str], worker_id: str) -> List[Dict]:
        """Process a batch of URLs with phone number deduplication"""
        results = []

        print(f"Worker {worker_id}: Starting batch of {len(urls)} URLs")

        for i, url in enumerate(urls):
            print(f"\nWorker {worker_id}: Processing {i+1}/{len(urls)}: {url}")

            # Step 1: Quick phone check to avoid unnecessary scraping
            quick_phone = self.quick_phone_check(url, worker_id)

            if quick_phone and self.is_phone_already_scraped(quick_phone):
                print(f"Worker {worker_id}: 🚫 SKIPPED - Phone {self.clean_phone_number(quick_phone)} already scraped")

                # Add skipped entry
                skipped_data = {
                    'url': url,
                    'extraction_success': False,
                    'skip_reason': 'phone_already_scraped',
                    'duplicate_phone': self.clean_phone_number(quick_phone),
                    'scraped_at': datetime.now().isoformat()
                }
                results.append(skipped_data)

                with self.results_lock:
                    self.skipped_count += 1

                continue

            # Step 2: Full scraping since phone is new or couldn't be quickly extracted
            html = self.fetch_with_scraperapi(url, worker_id)

            if html:
                # Extract data
                data = self.extract_data_from_html(html, url)

                # Check if this phone was already scraped (double-check after full extraction)
                if data['phone'] and self.is_phone_already_scraped(data['phone']):
                    print(f"Worker {worker_id}: 🚫 DUPLICATE DETECTED - Phone {self.clean_phone_number(data['phone'])} already exists")
                    data['extraction_success'] = False
                    data['skip_reason'] = 'phone_duplicate_detected'
                    with self.results_lock:
                        self.skipped_count += 1
                else:
                    # Add phone to scraped set if extraction was successful
                    if data['phone'] and data['extraction_success']:
                        self.add_phone_to_scraped(data['phone'])

                results.append(data)

                if data['extraction_success'] and not data.get('skip_reason'):
                    print(f"Worker {worker_id}: ✅ SUCCESS")
                    print(f"    Name: '{data['name']}'")
                    print(f"    Age: {data['age']}")
                    print(f"    Phone: '{data['phone']}'")
                    print(f"    Type: {data['website_type']}")
                else:
                    print(f"Worker {worker_id}: ❌ No meaningful data extracted or duplicate")
                    if 'error' in data:
                        print(f"    Error: {data['error']}")
                    if 'skip_reason' in data:
                        print(f"    Skip Reason: {data['skip_reason']}")
            else:
                # Add failed entry
                failed_data = {
                    'url': url,
                    'extraction_success': False,
                    'error': 'Failed to fetch page content via ScraperAPI',
                    'scraped_at': datetime.now().isoformat()
                }
                results.append(failed_data)
                print(f"Worker {worker_id}: ❌ Failed to fetch content")

            # Add results to main collection and save periodically
            with self.results_lock:
                self.results.append(data if html else failed_data)
                self.processed_count += 1

                # Save intermediate results every 1000 URLs
                if self.processed_count % 1000 == 0:
                    self.save_results(self.results, f"checkpoint_{self.processed_count}")
                    print(f"\n💾 CHECKPOINT: Saved {self.processed_count} URLs, Skipped {self.skipped_count} duplicates")

        successful = len([r for r in results if r.get('extraction_success', False) and not r.get('skip_reason')])
        skipped = len([r for r in results if r.get('skip_reason')])
        print(f"Worker {worker_id}: ✅ Batch completed: {successful} successful, {skipped} skipped duplicates, {len(results)-successful-skipped} failed")
        return results

    def process_urls_parallel(self, urls: List[str], max_urls: Optional[int] = None) -> List[Dict]:
        """Process URLs in parallel with enhanced deduplication"""

        if max_urls:
            urls = urls[:max_urls]

        print(f"\n🚀 Starting parallel processing of {len(urls)} URLs")
        print(f"👥 Workers: {self.max_workers}")
        print(f"🔑 API Keys: {len(self.api_keys)}")
        print(f"📱 Pre-loaded phone numbers to skip: {len(self.scraped_phone_numbers)}")
        print("=" * 60)

        # Split URLs into batches for workers
        batch_size = max(1, len(urls) // self.max_workers)
        batches = [urls[i:i + batch_size] for i in range(0, len(urls), batch_size)]

        all_results = []
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all batches
            future_to_worker = {}
            for i, batch in enumerate(batches):
                worker_id = f"W{i+1}"
                future = executor.submit(self.process_url_batch, batch, worker_id)
                future_to_worker[future] = worker_id

            # Collect results
            for future in as_completed(future_to_worker):
                worker_id = future_to_worker[future]
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    print(f"✅ {worker_id} completed")
                except Exception as e:
                    print(f"❌ {worker_id} failed: {e}")

        elapsed_time = time.time() - start_time

        print(f"\n🏁 PROCESSING COMPLETE")
        print(f"⏱️  Total time: {elapsed_time:.1f} seconds")
        print(f"📊 Processed: {len(all_results)} URLs")
        print(f"🚫 Skipped duplicates: {self.skipped_count}")
        print(f"📱 Unique phone numbers collected: {len(self.scraped_phone_numbers)}")

        return all_results

    def save_results(self, results: List[Dict], suffix: str = "final"):
        """Save results to Excel file with detailed analysis"""
        if not results:
            print("⚠️  No results to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"enhanced_dedup_results_{len(results)}_urls_{timestamp}_{suffix}.xlsx"

        try:
            df = pd.DataFrame(results)

            # Reorder columns
            column_order = [
                'url', 'name', 'age', 'phone', 'location', 'description',
                'website_type', 'extraction_success', 'skip_reason', 'duplicate_phone',
                'page_title', 'content_length', 'scraped_at', 'error'
            ]

            # Only include columns that exist
            existing_columns = [col for col in column_order if col in df.columns]
            df = df[existing_columns]

            # Create multiple sheets
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # All data
                df.to_excel(writer, sheet_name='All_Data', index=False)

                # Successful extractions only (new unique data)
                successful_df = df[(df['extraction_success'] == True) & (df['skip_reason'].isna())]
                if not successful_df.empty:
                    successful_df.to_excel(writer, sheet_name='New_Unique_Data', index=False)

                # Skipped duplicates
                skipped_df = df[df['skip_reason'].notna()]
                if not skipped_df.empty:
                    skipped_df.to_excel(writer, sheet_name='Skipped_Duplicates', index=False)

                # Failed extractions
                failed_df = df[(df['extraction_success'] == False) & (df['skip_reason'].isna())]
                if not failed_df.empty:
                    failed_df.to_excel(writer, sheet_name='Failed', index=False)

                # Statistics sheet
                stats_data = {
                    'Metric': [
                        'Total URLs Processed',
                        'New Unique Extractions',
                        'Skipped Duplicates',
                        'Failed Extractions',
                        'Deduplication Rate (%)',
                        'Success Rate (New Data) (%)',
                        'Total Unique Phone Numbers',
                        'Pre-loaded Phone Numbers',
                        'API Keys Used',
                        'Estimated ScraperAPI Cost ($)'
                    ],
                    'Value': [
                        len(df),
                        len(successful_df),
                        len(skipped_df),
                        len(failed_df),
                        f"{(len(skipped_df) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                        f"{(len(successful_df) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                        len(self.scraped_phone_numbers),
                        len(self.scraped_phone_numbers) - len(successful_df),
                        len(self.api_keys),
                        f"{len(df) * 0.001:.3f}"
                    ]
                }
                pd.DataFrame(stats_data).to_excel(writer, sheet_name='Statistics', index=False)

            # Display statistics
            print(f"\n💾 SAVED: {filename}")
            print(f"📊 DEDUPLICATION RESULTS:")
            print(f"   Total URLs: {len(df)}")
            print(f"   New Unique Data: {len(successful_df)}")
            print(f"   Skipped Duplicates: {len(skipped_df)} ({(len(skipped_df)/len(df)*100):.1f}%)")
            print(f"   Failed: {len(failed_df)}")
            print(f"   Total Unique Phone Numbers: {len(self.scraped_phone_numbers)}")

            # Show sample new data
            if not successful_df.empty:
                print(f"\n✅ SAMPLE NEW UNIQUE DATA:")
                for i, row in successful_df.head(5).iterrows():
                    print(f"   • {row.get('name', 'N/A')} | Age: {row.get('age', 'N/A')} | Phone: {row.get('phone', 'N/A')}")

        except Exception as e:
            print(f"❌ Error saving results: {e}")

def main():
    """Main function to run the enhanced deduplication scraper"""

    # Your ScraperAPI keys
    api_keys = [
        '********************************'
    ]

    # Mistral API key for social media extraction (optional)
    mistral_api_key = "nsgxaPTwCJ3jc69RjD7RaQrNuq3l9O6z"

    # Initialize scraper
    scraper = EnhancedDeduplicationScraper(
        api_keys=api_keys,
        max_workers=3,
        enable_social_media=True,
        mistral_api_key=mistral_api_key
    )

    # Load URLs
    if not os.path.exists(scraper.urls_file):
        print(f"❌ URLs file not found: {scraper.urls_file}")
        return

    with open(scraper.urls_file, 'r') as f:
        urls_data = json.load(f)

    # Extract URLs from nested structure
    urls = []
    if isinstance(urls_data, dict):
        for combination_key, combination_data in urls_data.items():
            if isinstance(combination_data, dict) and 'urls' in combination_data:
                urls.extend(combination_data['urls'])
    elif isinstance(urls_data, list):
        urls = urls_data

    print(f"📂 Loaded {len(urls)} URLs from {scraper.urls_file}")

    # Process URLs with deduplication
    max_urls = 50  # Set to None for all URLs, or a specific number for testing

    print(f"🚀 Starting Enhanced Deduplication Scraper")
    print(f"📱 Pre-loaded {len(scraper.scraped_phone_numbers)} existing phone numbers")
    print(f"🎯 Processing up to {max_urls if max_urls else len(urls)} URLs")
    print("=" * 60)

    # Process URLs
    results = scraper.process_urls_parallel(urls, max_urls=max_urls)

    # Final save
    scraper.save_results(results, "final")

    print(f"\n🎉 ENHANCED SCRAPING COMPLETED!")
    print(f"📊 Final Stats:")
    print(f"   • Total URLs processed: {len(results)}")
    print(f"   • Duplicates skipped: {scraper.skipped_count}")
    efficiency = (scraper.skipped_count / len(results) * 100) if len(results) > 0 else 0
    print(f"   • Deduplication efficiency: {efficiency:.1f}% saved")
    print(f"   • Unique phone numbers: {len(scraper.scraped_phone_numbers)}")

if __name__ == "__main__":
    main()
