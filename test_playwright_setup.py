#!/usr/bin/env python3
"""
Quick test script for Playwright setup
"""

import asyncio
from playwright.async_api import async_playwright

async def test_playwright():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()

        print("🌐 Testing basic page navigation...")
        await page.goto("https://httpbin.org/get")
        title = await page.title()

        print(f"✅ Successfully loaded page with title: {title}")

        await browser.close()
        print("🎉 Playwright is working correctly!")

if __name__ == "__main__":
    asyncio.run(test_playwright())
