#!/usr/bin/env python3
"""
Test script to demonstrate the resume functionality
"""

import sys
import os
import time
from state_scraper import StateScraper

def test_resume_functionality():
    """Test the resume functionality by simulating interruption"""
    print("Testing Resume Functionality")
    print("=" * 50)
    
    # Use your Mistral API key
    scraper = StateScraper(mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso")
    scraper.request_delay = 0.5  # Faster for testing
    
    state_name = "Alabama"
    
    try:
        print(f"Phase 1: Starting initial scrape for {state_name} (limited to 2 combinations)")
        
        # Clean any existing progress first
        state_safe_name = state_name.lower().replace(' ', '_')
        progress_file = f"state_{state_safe_name}_progress.json"
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print(f"Cleaned existing progress file: {progress_file}")
        
        # Start scraping with limit (simulate partial completion)
        success = scraper.scrape_state(state_name, max_cities=2, resume=False)
        
        if success:
            print(f"✓ Phase 1 completed: {len(scraper.scraped_data)} records collected")
            
            # Check if progress file was created
            if os.path.exists(progress_file):
                print(f"✓ Progress file created: {progress_file}")
                
                # Show progress file content
                import json
                with open(progress_file, 'r') as f:
                    progress_data = json.load(f)
                print(f"Progress data: {progress_data}")
            else:
                print("✗ No progress file found")
                return False
        else:
            print("✗ Phase 1 failed")
            return False
        
        print("\n" + "-" * 50)
        print("Phase 2: Simulating resume from previous session")
        
        # Create a new scraper instance to simulate restart
        scraper2 = StateScraper(mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso")
        scraper2.request_delay = 0.5
        
        # Try to resume (this should load the progress and continue)
        print("Attempting to resume...")
        success2 = scraper2.scrape_state(state_name, max_cities=4, resume=True)
        
        if success2:
            print(f"✓ Phase 2 completed: {len(scraper2.scraped_data)} total records")
            
            # Check if progress file was cleaned up
            if not os.path.exists(progress_file):
                print("✓ Progress file cleaned up after completion")
            else:
                print("! Progress file still exists (may indicate incomplete run)")
            
            return True
        else:
            print("✗ Phase 2 failed")
            return False
            
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        return False

def test_progress_file_structure():
    """Test the structure of progress files"""
    print("\nTesting Progress File Structure")
    print("=" * 50)
    
    scraper = StateScraper(mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso")
    
    # Test progress save/load
    test_progress = {
        'state': 'TestState',
        'current_city': 'TestCity',
        'current_source': 'aaok',
        'progress_index': 1,
        'total_combinations': 10,
        'processed_count': 1
    }
    
    test_file = "test_progress.json"
    scraper.progress_file = test_file
    
    try:
        # Save progress
        scraper.save_progress(test_progress)
        print(f"✓ Progress saved to {test_file}")
        
        # Load progress
        loaded_progress = scraper.load_progress()
        print(f"✓ Progress loaded: {loaded_progress}")
        
        # Verify data
        if loaded_progress.get('state') == 'TestState':
            print("✓ Progress data integrity verified")
        else:
            print("✗ Progress data integrity failed")
            return False
        
        # Clean up
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"✓ Test file cleaned up: {test_file}")
        
        return True
        
    except Exception as e:
        print(f"✗ Progress file test failed: {e}")
        return False

def main():
    """Run resume functionality tests"""
    print("Resume Functionality Test Suite")
    print("=" * 60)
    
    tests = [
        test_progress_file_structure,
        test_resume_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED\n")
            else:
                print("✗ FAILED\n")
        except Exception as e:
            print(f"✗ FAILED with exception: {e}\n")
    
    print("=" * 60)
    print(f"Resume Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All resume functionality tests passed!")
        print("\nResume features available:")
        print("  ✓ Automatic progress saving after every city")
        print("  ✓ Resume from interruption")
        print("  ✓ Data checkpoints")
        print("  ✓ Progress file cleanup on completion")
        print("\nUsage examples:")
        print("  python state_scraper.py Alabama  # Auto-resume if interrupted")
        print("  python state_scraper.py Alabama --no-resume  # Start fresh")
        print("  python state_scraper.py Alabama --clean-progress  # Clean and start fresh")
        print("  python web_scraper.py  # Full scraper with resume")
        print("  python web_scraper.py --clean-progress  # Clean and start fresh")
        return 0
    else:
        print("✗ Some resume functionality tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
