#!/usr/bin/env python3
"""
Enhanced NYC Scraper - <PERSON><PERSON><PERSON> handles both AAOK and AYPAPI website structures
"""

import sys
import os
import json
import time
import threading
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import pandas as pd
import re
from pathlib import Path
from bs4 import BeautifulSoup
from urllib.parse import urlparse

from nyc_boroughs_scraper import NYCBoroughsScraper

class EnhancedNYCScraper:
    def __init__(self, max_workers: int = 5):
        """Initialize enhanced NYC scraper with proper AYPAPI support"""

        self.max_workers = max_workers
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results_lock = threading.Lock()
        self.all_extracted_data = []
        self.save_interval = 1000  # Save every 1000 URLs
        self.last_save_count = 0

        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] - %(message)s',
            handlers=[
                logging.FileHandler('enhanced_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

        self.logger.info(f"Enhanced Scraper initialized with {max_workers} workers")
        self.logger.info(f"Supports: AAOK + AYPAPI website structures")
        self.logger.info(f"Auto-save enabled: Excel file saved every {self.save_interval} URLs")

    def detect_website_type(self, url: str, html_content: str) -> str:
        """Detect which website type we're dealing with"""
        domain = urlparse(url).netloc.lower()

        if 'aypapi' in domain or 'escortbabylon' in domain:
            return 'aypapi'
        elif 'aaok' in domain or 'adultsearch' in domain:
            return 'aaok'
        else:
            # Try to detect from content
            if 'aypapi' in html_content.lower() or 'escortbabylon' in html_content.lower():
                return 'aypapi'
            elif 'aaok' in html_content.lower() or 'adultsearch' in html_content.lower():
                return 'aaok'

        return 'unknown'

    def extract_aypapi_data(self, html_content: str, url: str) -> Dict:
        """Extract data specifically from AYPAPI/EscortBabylon structure"""

        extracted_data = {
            'url': url,
            'extraction_status': 'FAILED',
            'website_type': 'aypapi',
            'is_female': False,
            'name': None,
            'age': None,
            'phone': None,
            'description': None,
            'social_media': None,
            'address': None,
            'raw_text': None,
            'post_id': None,
            'post_date': None,
            'scraped_at': datetime.now().isoformat()
        }

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Extract post ID from various sources
            post_id_match = re.search(r'pid.*?value="(\d+)"', html_content)
            if post_id_match:
                extracted_data['post_id'] = post_id_match.group(1)
            else:
                # Try URL
                url_id_match = re.search(r'/(\d+)$', url)
                if url_id_match:
                    extracted_data['post_id'] = url_id_match.group(1)

            # 1. Extract GENDER - from "I am" section
            gender_elem = soup.find('div', class_='i-am')
            if gender_elem:
                gender_value = gender_elem.find('span', class_='iamisee__value')
                if gender_value:
                    gender_text = gender_value.get_text().strip().lower()
                    if any(word in gender_text for word in ['woman', 'female', 'girl', 'lady']):
                        extracted_data['is_female'] = True

            # 2. Extract NAME - Try CSS selector first, then patterns
            name_elem = soup.select_one('.viewposttitle')
            if name_elem:
                extracted_data['name'] = name_elem.get_text(strip=True)
            else:
                # Check for any name indicators in the title or content
                title_elem = soup.find('div', class_='viewposttitle')
                if title_elem:
                    title_text = title_elem.get_text().strip()
                    # Try to extract name patterns from title (very basic)
                    name_patterns = [
                        r'(?:hi\s+i\'m\s+|call\s+me\s+|i\'m\s+|my\s+name\s+is\s+)([a-zA-Z]{2,15})',
                        r'^([A-Za-z]{2,15})\s+\d+',  # Name followed by age
                    ]

                    for pattern in name_patterns:
                        match = re.search(pattern, title_text, re.IGNORECASE)
                        if match:
                            potential_name = match.group(1).strip()
                            if len(potential_name) > 1 and potential_name.isalpha():
                                extracted_data['name'] = potential_name
                                break

            # 3. Extract AGE - from title age span
            age_elem = soup.find('span', class_='postTitleAge')
            if age_elem:
                age_text = age_elem.get_text().strip()
                if age_text.isdigit() and 18 <= int(age_text) <= 65:
                    extracted_data['age'] = age_text
            else:
                # Try age list in content
                age_li = soup.find('li')
                if age_li and 'Age:' in age_li.get_text():
                    age_match = re.search(r'Age:\s*(\d+)', age_li.get_text())
                    if age_match and 18 <= int(age_match.group(1)) <= 65:
                        extracted_data['age'] = age_match.group(1)

            # 4. Extract PHONE - Try CSS selector first, then tel links
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                extracted_data['phone'] = phone_elem.get_text(strip=True)
            else:
                # Try tel links
                phone_elem = soup.find('a', href=re.compile(r'^tel:'))
                if phone_elem:
                    phone_href = phone_elem.get('href', '')
                    phone_number = phone_href.replace('tel:', '').strip()
                    # Clean phone number
                    phone_clean = re.sub(r'[^\d]', '', phone_number)
                    if len(phone_clean) == 10:
                        # Format as XXX-XXX-XXXX
                        formatted_phone = f"{phone_clean[:3]}-{phone_clean[3:6]}-{phone_clean[6:]}"
                        extracted_data['phone'] = formatted_phone
                    elif len(phone_clean) == 11 and phone_clean.startswith('1'):
                        # Format as XXX-XXX-XXXX (remove leading 1)
                        phone_clean = phone_clean[1:]
                        formatted_phone = f"{phone_clean[:3]}-{phone_clean[3:6]}-{phone_clean[6:]}"
                        extracted_data['phone'] = formatted_phone
                    else:
                        extracted_data['phone'] = phone_number
                else:
                    # Look for phone in viewposttelephone div
                    phone_div = soup.find('div', class_='viewposttelephone')
                    if phone_div:
                        phone_link = phone_div.find('a')
                        if phone_link:
                            phone_text = phone_link.get_text().strip()
                            extracted_data['phone'] = phone_text

            # 5. Extract DESCRIPTION - from body
            body_elem = soup.find('div', class_='viewpostbody')
            if body_elem:
                description_text = body_elem.get_text().strip()
                if description_text and len(description_text) > 5:
                    # Clean up description
                    description_clean = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', ' ', description_text)
                    description_clean = ' '.join(description_clean.split())  # Remove extra whitespace
                    extracted_data['description'] = description_clean

            # 6. Extract POST DATE
            date_elem = soup.find('div', class_='postCreatedOn')
            if date_elem:
                date_text = date_elem.get_text().strip()
                extracted_data['post_date'] = date_text

            # 7. Get full page text for social media and address extraction
            all_text = soup.get_text()
            extracted_data['raw_text'] = all_text

            # 8. Extract SOCIAL MEDIA from full text - Enhanced patterns
            social_media_patterns = [
                r'(?:instagram|insta|ig)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:snapchat|snap)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:twitter|twt)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:onlyfans|of)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:tiktok|tt)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:telegram|tg)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:kik)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:whatsapp|wa)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'[@]([a-zA-Z0-9_.]{3,25})',  # Generic @username
                r'(?:follow\s+me|add\s+me|find\s+me)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
            ]

            social_media_found = []
            for pattern in social_media_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        social_handle = match[0] if match[0] else match[1]
                    else:
                        social_handle = match

                    # Filter out common false positives
                    if (len(social_handle) >= 2 and
                        not social_handle.lower() in ['com', 'net', 'org', 'www', 'http', 'https', 'me', 'you', 'my', 'and', 'the'] and
                        not social_handle.isdigit()):
                        social_media_found.append(social_handle.strip())

            if social_media_found:
                # Remove duplicates and clean up
                unique_social = list(set(social_media_found))
                unique_social = [s for s in unique_social if len(s) > 1]
                if unique_social:
                    extracted_data['social_media'] = ', '.join(unique_social[:5])  # Limit to 5

            # 9. Extract ADDRESS/LOCATION - Enhanced patterns
            address_patterns = [
                r'\b\d+\s+[A-Za-z\s]{3,}(?:street|st|avenue|ave|road|rd|boulevard|blvd|lane|ln|drive|dr|place|pl|way|ct|court)\b',
                r'\b(?:manhattan|brooklyn|bronx|queens|staten\s+island|midtown|downtown|uptown|east\s+side|west\s+side|harlem|soho|tribeca)\b',
                r'\b\d{5}(?:-\d{4})?\b',  # ZIP codes
                r'\b[A-Za-z\s]+,\s*NY\b',
                r'\b[A-Za-z\s]+,\s*New\s+York\b',
                r'\bnyc\b',
                r'\bnew\s+york\s+city\b',
                r'\b(?:incall|outcall|hotel|apartment|place)\s+(?:in|at|near)\s+([A-Za-z\s]{3,25})',
                r'\b(?:located|visiting|available)\s+(?:in|at|near)\s+([A-Za-z\s]{3,25})',
            ]

            addresses_found = []
            for pattern in address_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        addr = match[0] if match[0] else match[1]
                    else:
                        addr = match

                    addr = addr.strip()
                    if len(addr) > 2 and not addr.lower() in ['and', 'the', 'with', 'for', 'you', 'me']:
                        addresses_found.append(addr)

            if addresses_found:
                # Remove duplicates and clean up
                unique_addresses = list(set(addresses_found))
                unique_addresses = [a for a in unique_addresses if len(a) > 2]
                if unique_addresses:
                    extracted_data['address'] = ', '.join(unique_addresses[:3])  # Limit to 3

            # Mark as successful if we extracted meaningful data
            success_indicators = [
                extracted_data['phone'],
                extracted_data['description'],
                extracted_data['age'],
                extracted_data['social_media'],
                extracted_data['address']
            ]

            if any(indicator for indicator in success_indicators):
                extracted_data['extraction_status'] = 'SUCCESS'

            return extracted_data

        except Exception as e:
            self.logger.warning(f"Failed to extract AYPAPI data from {url}: {e}")
            extracted_data['extraction_status'] = 'ERROR'
            extracted_data['raw_text'] = f"Extraction error: {str(e)}"
            return extracted_data

    def extract_aaok_data(self, html_content: str, url: str) -> Dict:
        """Extract data specifically from AAOK structure"""

        extracted_data = {
            'url': url,
            'extraction_status': 'FAILED',
            'website_type': 'aaok',
            'is_female': False,
            'name': None,
            'age': None,
            'phone': None,
            'description': None,
            'social_media': None,
            'address': None,
            'raw_text': None,
            'post_id': None,
            'post_date': None,
            'scraped_at': datetime.now().isoformat()
        }

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Extract post ID from URL
            post_id_match = re.search(r'/(\d+)$', url)
            if post_id_match:
                extracted_data['post_id'] = post_id_match.group(1)

            # Get all text for pattern matching
            all_text = soup.get_text()
            extracted_data['raw_text'] = all_text

            # 1. Extract GENDER - Look for gender indicators
            gender_patterns = [
                r'(?:gender|sex)[:\s]*(?:female|woman|girl|lady)',
                r'(?:i\s+am)[:\s]*(?:a\s+)?(?:female|woman|girl|lady)',
                r'(?:female|woman|girl|lady)',
            ]

            for pattern in gender_patterns:
                if re.search(pattern, all_text, re.IGNORECASE):
                    extracted_data['is_female'] = True
                    break

            # 2. Extract NAME - Try CSS selector first, then regex patterns
            name_elem = soup.select_one('.viewposttitle')
            if name_elem:
                extracted_data['name'] = name_elem.get_text(strip=True)
            else:
                name_patterns = [
                    r'(?:name|call\s+me|hi\s+i\'m|i\'m)[:\s]*([A-Za-z]{2,15})',
                    r'^([A-Za-z]{2,15})\s*\d+\s*years?\s*old',
                    r'hi\s+guys?\s+i\'m\s+([A-Za-z]{2,15})',
                ]

                for pattern in name_patterns:
                    match = re.search(pattern, all_text, re.IGNORECASE)
                    if match:
                        potential_name = match.group(1).strip()
                        if len(potential_name) > 1 and potential_name.isalpha():
                            extracted_data['name'] = potential_name
                            break

            # 3. Extract AGE - Try CSS selector first, then regex patterns
            age_elem = soup.select_one('.postTitleAge')
            if age_elem:
                age_text = age_elem.get_text(strip=True)
                if age_text.isdigit() and 18 <= int(age_text) <= 65:
                    extracted_data['age'] = age_text
            else:
                age_patterns = [
                    r'(?:age|years?\s+old)[:\s]*(\d{2})',
                    r'(\d{2})\s*years?\s*old',
                    r'i\'m\s+(\d{2})',
                ]

                for pattern in age_patterns:
                    match = re.search(pattern, all_text, re.IGNORECASE)
                    if match and 18 <= int(match.group(1)) <= 65:
                        extracted_data['age'] = match.group(1)
                        break

            # 4. Extract PHONE - Try CSS selector first, then regex patterns
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                extracted_data['phone'] = phone_elem.get_text(strip=True)
            else:
                phone_patterns = [
                    r'(?:call|text|phone|number)[:\s]*(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})',
                    r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{4})',
                    r'(\(\d{3}\)\s*\d{3}[-.\s]?\d{4})',
                ]

                for pattern in phone_patterns:
                    matches = re.findall(pattern, all_text)
                    for match in matches:
                        phone_clean = re.sub(r'[^\d]', '', match)
                        if len(phone_clean) == 10:
                            formatted_phone = f"{phone_clean[:3]}-{phone_clean[3:6]}-{phone_clean[6:]}"
                            extracted_data['phone'] = formatted_phone
                            break
                    if extracted_data['phone']:
                        break

            # 5. Extract DESCRIPTION - Get main content
            # Look for common content areas
            content_selectors = [
                'div[class*="content"]',
                'div[class*="body"]',
                'div[class*="description"]',
                'div[class*="text"]',
                'p',
            ]

            description_text = ""
            for selector in content_selectors:
                elements = soup.select(selector)
                for elem in elements:
                    text = elem.get_text().strip()
                    if len(text) > 20 and len(text) > len(description_text):
                        description_text = text

            if description_text:
                # Clean up description
                description_clean = re.sub(r'[^\w\s\.\,\!\?\;\:\-\(\)]', ' ', description_text)
                description_clean = ' '.join(description_clean.split())
                if len(description_clean) > 10:
                    extracted_data['description'] = description_clean[:500]  # Limit length

            # 6. Extract SOCIAL MEDIA
            social_media_patterns = [
                r'(?:instagram|insta|ig)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:snapchat|snap)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:twitter|twt)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:onlyfans|of)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:tiktok|tt)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'(?:telegram|tg)[:\s]*[@]?([a-zA-Z0-9_.]{2,30})',
                r'[@]([a-zA-Z0-9_.]{3,25})',
            ]

            social_media_found = []
            for pattern in social_media_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                social_media_found.extend(matches)

            if social_media_found:
                unique_social = list(set([s.strip() for s in social_media_found if len(s.strip()) > 1]))
                if unique_social:
                    extracted_data['social_media'] = ', '.join(unique_social[:5])

            # 7. Extract ADDRESS/LOCATION
            address_patterns = [
                r'\b\d+\s+[A-Za-z\s]{3,}(?:street|st|avenue|ave|road|rd|boulevard|blvd)\b',
                r'\b(?:manhattan|brooklyn|bronx|queens|staten\s+island)\b',
                r'\b\d{5}\b',
                r'\b[A-Za-z\s]+,\s*NY\b',
                r'\bnyc\b',
            ]

            addresses_found = []
            for pattern in address_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                addresses_found.extend(matches)

            if addresses_found:
                unique_addresses = list(set([a.strip() for a in addresses_found if len(a.strip()) > 2]))
                if unique_addresses:
                    extracted_data['address'] = ', '.join(unique_addresses[:3])

            # Mark as successful if we extracted meaningful data
            success_indicators = [
                extracted_data['phone'],
                extracted_data['description'],
                extracted_data['age'],
                extracted_data['name']
            ]

            if any(indicator for indicator in success_indicators):
                extracted_data['extraction_status'] = 'SUCCESS'

            return extracted_data

        except Exception as e:
            self.logger.warning(f"Failed to extract AAOK data from {url}: {e}")
            extracted_data['extraction_status'] = 'ERROR'
            extracted_data['raw_text'] = f"Extraction error: {str(e)}"
            return extracted_data

    def extract_all_data_enhanced(self, html_content: str, url: str) -> Dict:
        """Extract data with enhanced website-specific handling"""

        # Detect website type
        website_type = self.detect_website_type(url, html_content)

        if website_type == 'aypapi':
            return self.extract_aypapi_data(html_content, url)
        elif website_type == 'aaok':
            return self.extract_aaok_data(html_content, url)
        else:
            # Default fallback - try both methods and pick the one with more data
            aypapi_result = self.extract_aypapi_data(html_content, url)
            aaok_result = self.extract_aaok_data(html_content, url)

            # Count non-null fields to determine which extraction was better
            aypapi_score = sum(1 for key in ['phone', 'description', 'age', 'name', 'social_media']
                             if aypapi_result.get(key))
            aaok_score = sum(1 for key in ['phone', 'description', 'age', 'name', 'social_media']
                           if aaok_result.get(key))

            if aypapi_score >= aaok_score:
                aypapi_result['website_type'] = 'unknown_aypapi_method'
                return aypapi_result
            else:
                aaok_result['website_type'] = 'unknown_aaok_method'
                return aaok_result

    def process_combination_enhanced(self, combo_key: str, combo_data: Dict, worker_id: int) -> List[Dict]:
        """Process a single combination with enhanced extraction"""

        borough = combo_data['borough']
        source = combo_data['source']
        urls = combo_data['urls']

        self.logger.info(f"Worker {worker_id} processing {borough} ({source}): {len(urls)} URLs - ENHANCED EXTRACTION")

        if not urls:
            self.logger.info(f"Worker {worker_id} no URLs to process for {borough} ({source})")
            return []

        try:
            # Create scraper for this worker
            scraper = NYCBoroughsScraper("dummy_key")  # No API key needed

            all_extracted_data = []

            for i, url in enumerate(urls):
                if i > 0 and i % 50 == 0:
                    self.logger.info(f"Worker {worker_id} processed {i}/{len(urls)} URLs")

                # Download HTML
                html = scraper.execute_curl_request(url, scraper.dedicated_curl_template)

                if html and len(html) > 100:  # Minimum content check
                    # Extract all data with enhanced method
                    extracted_data = self.extract_all_data_enhanced(html, url)

                    # Add metadata
                    extracted_data['city'] = borough
                    extracted_data['source'] = source
                    extracted_data['worker_id'] = worker_id

                    all_extracted_data.append(extracted_data)
                else:
                    # Failed to download HTML
                    failed_data = {
                        'url': url,
                        'city': borough,
                        'source': source,
                        'extraction_status': 'DOWNLOAD_FAILED',
                        'website_type': 'unknown',
                        'is_female': False,
                        'name': None,
                        'age': None,
                        'phone': None,
                        'description': None,
                        'social_media': None,
                        'address': None,
                        'raw_text': 'Failed to download HTML or content too short',
                        'post_id': None,
                        'post_date': None,
                        'worker_id': worker_id,
                        'scraped_at': datetime.now().isoformat()
                    }
                    all_extracted_data.append(failed_data)

                # Small delay to avoid overwhelming servers
                time.sleep(0.1)  # Slightly longer delay for stability

            self.logger.info(f"Worker {worker_id} completed {borough} ({source}): {len(all_extracted_data)} records extracted")

            # Save checkpoint
            checkpoint_file = f"enhanced_{borough}_{source}_checkpoint.xlsx"
            if all_extracted_data:
                try:
                    df = pd.DataFrame(all_extracted_data)
                    df.to_excel(checkpoint_file, index=False)
                    self.logger.info(f"Worker {worker_id} saved checkpoint: {checkpoint_file}")
                except Exception as e:
                    self.logger.warning(f"Worker {worker_id} failed to save checkpoint: {e}")

            return all_extracted_data

        except Exception as e:
            self.logger.error(f"Worker {worker_id} failed processing {borough} ({source}): {e}")
            return []

    def save_intermediate_results(self, force_save: bool = False):
        """Save intermediate results every 1000 URLs or when forced"""
        with self.results_lock:
            current_count = len(self.all_extracted_data)

            if force_save or (current_count >= self.last_save_count + self.save_interval):
                if current_count > 0:
                    try:
                        df = pd.DataFrame(self.all_extracted_data)

                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"enhanced_extraction_{current_count}_urls_{timestamp}.xlsx"

                        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                            # All data
                            df.to_excel(writer, sheet_name='ALL_DATA', index=False)

                            # Successful extractions
                            success_df = df[df['extraction_status'] == 'SUCCESS']
                            if not success_df.empty:
                                success_df.to_excel(writer, sheet_name='SUCCESS', index=False)

                            # Female profiles
                            female_df = df[df['is_female'] == True]
                            if not female_df.empty:
                                female_df.to_excel(writer, sheet_name='FEMALE_PROFILES', index=False)

                            # By website type
                            aypapi_df = df[df['website_type'].str.contains('aypapi', na=False)]
                            if not aypapi_df.empty:
                                aypapi_df.to_excel(writer, sheet_name='AYPAPI_EXTRACTED', index=False)

                            aaok_df = df[df['website_type'].str.contains('aaok', na=False)]
                            if not aaok_df.empty:
                                aaok_df.to_excel(writer, sheet_name='AAOK_EXTRACTED', index=False)

                            # Failed for review
                            failed_df = df[df['extraction_status'].isin(['FAILED', 'ERROR', 'DOWNLOAD_FAILED'])]
                            if not failed_df.empty:
                                failed_df.to_excel(writer, sheet_name='FAILED', index=False)

                        self.logger.info(f"📊 ENHANCED EXTRACTION SAVE: {current_count} URLs saved to {filename}")
                        self.last_save_count = current_count

                        # Statistics
                        success_count = len(success_df) if not success_df.empty else 0
                        female_count = len(female_df) if not female_df.empty else 0
                        aypapi_count = len(aypapi_df) if not aypapi_df.empty else 0
                        aaok_count = len(aaok_df) if not aaok_df.empty else 0

                        self.logger.info(f"   Success: {success_count}, Female: {female_count}")
                        self.logger.info(f"   AYPAPI: {aypapi_count}, AAOK: {aaok_count}")

                    except Exception as e:
                        self.logger.error(f"Failed to save intermediate results: {e}")

                return True
            return False

    def run_enhanced_extraction(self, max_urls: int = None) -> bool:
        """Run enhanced extraction with proper AYPAPI support"""
        self.logger.info("=" * 60)
        self.logger.info("ENHANCED EXTRACTION - AYPAPI + AAOK SUPPORT")
        self.logger.info("=" * 60)

        # Load URLs data
        if not os.path.exists(self.urls_file):
            self.logger.error(f"URLs file {self.urls_file} not found. Run Phase 1 first.")
            return False

        try:
            with open(self.urls_file, 'r') as f:
                urls_data = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load URLs file: {e}")
            return False

        self.logger.info(f"Using {self.max_workers} parallel workers for enhanced extraction")
        self.logger.info(f"Website support: AYPAPI (EscortBabylon) + AAOK (AdultSearch)")

        # Summary
        total_urls = sum(data['total_urls'] for data in urls_data.values())
        self.logger.info(f"Total URLs to process: {total_urls}")

        # Apply URL limit for testing if specified
        if max_urls:
            self.logger.info(f"TESTING MODE: Limiting to first {max_urls} URLs")
            urls_processed = 0
            limited_urls_data = {}

            for combo_key, combo_data in urls_data.items():
                if urls_processed >= max_urls:
                    break

                remaining_urls = max_urls - urls_processed
                if combo_data['total_urls'] <= remaining_urls:
                    # Take all URLs from this combination
                    limited_urls_data[combo_key] = combo_data
                    urls_processed += combo_data['total_urls']
                else:
                    # Take partial URLs from this combination
                    limited_combo_data = combo_data.copy()
                    limited_combo_data['urls'] = combo_data['urls'][:remaining_urls]
                    limited_combo_data['total_urls'] = remaining_urls
                    limited_urls_data[combo_key] = limited_combo_data
                    urls_processed += remaining_urls

            urls_data = limited_urls_data
            total_urls = urls_processed
            self.logger.info(f"Limited to {total_urls} URLs for testing")

        self.logger.info(f"Enhanced extraction features:")
        self.logger.info(f"  - Automatic website type detection")
        self.logger.info(f"  - AYPAPI-specific extraction logic")
        self.logger.info(f"  - AAOK-specific extraction logic")
        self.logger.info(f"  - Enhanced pattern matching")
        self.logger.info(f"  - Improved data cleaning")

        # Process combinations in parallel
        successful_workers = 0
        failed_workers = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_combo = {
                executor.submit(self.process_combination_enhanced, combo_key, combo_data, i): (combo_key, combo_data)
                for i, (combo_key, combo_data) in enumerate(urls_data.items())
            }

            # Process completed tasks
            for future in as_completed(future_to_combo):
                combo_key, combo_data = future_to_combo[future]
                try:
                    result = future.result()
                    if result is not None:
                        successful_workers += 1

                        # Thread-safe data collection with auto-save
                        with self.results_lock:
                            self.all_extracted_data.extend(result)

                        # Check if we should save intermediate results
                        self.save_intermediate_results()

                        self.logger.info(f"✓ Completed: {combo_data['borough']} from {combo_data['source']} ({len(result)} records)")
                    else:
                        failed_workers += 1
                        self.logger.error(f"✗ Failed: {combo_data['borough']} from {combo_data['source']}")
                except Exception as e:
                    failed_workers += 1
                    self.logger.error(f"✗ Exception in {combo_data['borough']} from {combo_data['source']}: {e}")

        # Force save any remaining data
        self.save_intermediate_results(force_save=True)

        # Save final comprehensive results
        if self.all_extracted_data:
            final_file = "FINAL_enhanced_extraction_nyc.xlsx"
            try:
                df = pd.DataFrame(self.all_extracted_data)

                # Create comprehensive final file
                with pd.ExcelWriter(final_file, engine='openpyxl') as writer:
                    # All data
                    df.to_excel(writer, sheet_name='ALL_DATA', index=False)

                    # Successful extractions only
                    success_df = df[df['extraction_status'] == 'SUCCESS']
                    success_df.to_excel(writer, sheet_name='SUCCESS', index=False)

                    # Female profiles only (main target)
                    female_df = df[df['is_female'] == True]
                    female_df.to_excel(writer, sheet_name='FEMALE_PROFILES', index=False)

                    # High-quality profiles (phone + description + age)
                    quality_df = df[
                        (df['phone'].notna()) &
                        (df['description'].notna()) &
                        (df['age'].notna())
                    ]
                    quality_df.to_excel(writer, sheet_name='HIGH_QUALITY', index=False)

                    # By website type
                    aypapi_df = df[df['website_type'].str.contains('aypapi', na=False)]
                    aypapi_df.to_excel(writer, sheet_name='AYPAPI_EXTRACTED', index=False)

                    aaok_df = df[df['website_type'].str.contains('aaok', na=False)]
                    aaok_df.to_excel(writer, sheet_name='AAOK_EXTRACTED', index=False)

                    # By source
                    source_aaok_df = df[df['source'] == 'aaok']
                    source_aaok_df.to_excel(writer, sheet_name='SOURCE_AAOK', index=False)

                    source_aypapi_df = df[df['source'] == 'aypapi']
                    source_aypapi_df.to_excel(writer, sheet_name='SOURCE_AYPAPI', index=False)

                    # Profiles with social media
                    social_df = df[df['social_media'].notna()]
                    if not social_df.empty:
                        social_df.to_excel(writer, sheet_name='WITH_SOCIAL_MEDIA', index=False)

                    # Failed extractions for review
                    failed_df = df[df['extraction_status'].isin(['FAILED', 'ERROR', 'DOWNLOAD_FAILED'])]
                    if not failed_df.empty:
                        failed_df.to_excel(writer, sheet_name='FAILED_FOR_REVIEW', index=False)

                    # Summary statistics
                    summary_data = []

                    # Overall stats
                    total_count = len(df)
                    success_count = len(success_df)
                    female_count = len(female_df)
                    quality_count = len(quality_df)

                    summary_data.append({'Metric': 'Total URLs Processed', 'Count': total_count})
                    summary_data.append({'Metric': 'Successful Extractions', 'Count': success_count})
                    summary_data.append({'Metric': 'Female Profiles', 'Count': female_count})
                    summary_data.append({'Metric': 'High Quality Profiles', 'Count': quality_count})
                    summary_data.append({'Metric': 'Success Rate', 'Count': f"{success_count/total_count*100:.1f}%"})
                    summary_data.append({'Metric': 'Female Rate', 'Count': f"{female_count/total_count*100:.1f}%"})

                    # By website extraction type
                    aypapi_extracted = len(aypapi_df)
                    aaok_extracted = len(aaok_df)
                    summary_data.append({'Metric': 'AYPAPI Method Used', 'Count': aypapi_extracted})
                    summary_data.append({'Metric': 'AAOK Method Used', 'Count': aaok_extracted})

                    # By source
                    source_aaok_count = len(source_aaok_df)
                    source_aypapi_count = len(source_aypapi_df)
                    summary_data.append({'Metric': 'Source AAOK Records', 'Count': source_aaok_count})
                    summary_data.append({'Metric': 'Source AYPAPI Records', 'Count': source_aypapi_count})

                    # By borough
                    boroughs = df['city'].value_counts()
                    for borough, count in boroughs.items():
                        summary_data.append({'Metric': f'{borough} Records', 'Count': count})

                    # Data quality metrics
                    phone_count = len(df[df['phone'].notna()])
                    social_count = len(df[df['social_media'].notna()])
                    address_count = len(df[df['address'].notna()])

                    summary_data.append({'Metric': 'With Phone Numbers', 'Count': phone_count})
                    summary_data.append({'Metric': 'With Social Media', 'Count': social_count})
                    summary_data.append({'Metric': 'With Address Info', 'Count': address_count})

                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='SUMMARY', index=False)

                self.logger.info(f"Final enhanced extraction results saved to: {final_file}")

                # Final summary
                self.logger.info("=" * 60)
                self.logger.info("ENHANCED EXTRACTION COMPLETED")
                self.logger.info("=" * 60)
                self.logger.info(f"Successful workers: {successful_workers}, Failed workers: {failed_workers}")
                self.logger.info(f"Total records processed: {len(self.all_extracted_data)}")

                # Detailed breakdown
                success_count = len(df[df['extraction_status'] == 'SUCCESS'])
                female_count = len(df[df['is_female'] == True])
                quality_count = len(df[
                    (df['phone'].notna()) &
                    (df['description'].notna()) &
                    (df['age'].notna())
                ])

                self.logger.info(f"  - Successful extractions: {success_count} ({success_count/len(df)*100:.1f}%)")
                self.logger.info(f"  - Female profiles: {female_count} ({female_count/len(df)*100:.1f}%)")
                self.logger.info(f"  - High quality profiles: {quality_count} ({quality_count/len(df)*100:.1f}%)")

                # By extraction method
                aypapi_extracted = len(df[df['website_type'].str.contains('aypapi', na=False)])
                aaok_extracted = len(df[df['website_type'].str.contains('aaok', na=False)])

                self.logger.info(f"  - AYPAPI method used: {aypapi_extracted}")
                self.logger.info(f"  - AAOK method used: {aaok_extracted}")

                # By source
                source_aaok_count = len(df[df['source'] == 'aaok'])
                source_aypapi_count = len(df[df['source'] == 'aypapi'])

                self.logger.info(f"  - Source AAOK records: {source_aaok_count}")
                self.logger.info(f"  - Source AYPAPI records: {source_aypapi_count}")

                # Data quality
                phone_count = len(df[df['phone'].notna()])
                social_count = len(df[df['social_media'].notna()])
                address_count = len(df[df['address'].notna()])

                self.logger.info(f"Data Quality:")
                self.logger.info(f"  - With phone numbers: {phone_count} ({phone_count/len(df)*100:.1f}%)")
                self.logger.info(f"  - With social media: {social_count} ({social_count/len(df)*100:.1f}%)")
                self.logger.info(f"  - With address info: {address_count} ({address_count/len(df)*100:.1f}%)")

                # Borough breakdown
                boroughs = df['city'].value_counts()
                self.logger.info(f"NYC Boroughs processed: {len(boroughs)}")
                for borough, count in boroughs.items():
                    female_borough = len(df[(df['city'] == borough) & (df['is_female'] == True)])
                    success_borough = len(df[(df['city'] == borough) & (df['extraction_status'] == 'SUCCESS')])
                    self.logger.info(f"  - {borough}: {count} total, {success_borough} success, {female_borough} female")

                self.logger.info(f"Enhanced Extraction Benefits:")
                self.logger.info(f"  - Proper AYPAPI website support")
                self.logger.info(f"  - Automatic website type detection")
                self.logger.info(f"  - Enhanced pattern matching")
                self.logger.info(f"  - Better data cleaning and validation")
                self.logger.info(f"  - Multiple analysis sheets")

            except Exception as e:
                self.logger.error(f"Failed to save final results: {e}")
                return False

        return failed_workers == 0

def main():
    """Main entry point for enhanced NYC scraper"""
    parser = argparse.ArgumentParser(description='Enhanced NYC Boroughs Scraper - Proper AYPAPI + AAOK Support')
    parser.add_argument('--workers', type=int, default=5,
                       help='Number of parallel workers (default: 5)')
    parser.add_argument('--max-urls', type=int, default=None,
                       help='Maximum URLs to process (for testing)')

    args = parser.parse_args()

    # Validate parameters
    if args.workers < 1 or args.workers > 10:
        print("Error: Number of workers must be between 1 and 10")
        return 1

    # Create enhanced scraper
    scraper = EnhancedNYCScraper(max_workers=args.workers)

    print("Enhanced NYC Boroughs Scraper")
    print("=" * 50)
    print(f"Configuration: {args.workers} workers")
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Enhanced features:")
    print("  - Automatic website type detection")
    print("  - AYPAPI (EscortBabylon) specific extraction")
    print("  - AAOK (AdultSearch) specific extraction")
    print("  - Enhanced pattern matching for:")
    print("    * Gender identification")
    print("    * Name extraction")
    print("    * Phone number formatting")
    print("    * Social media detection")
    print("    * Address/location extraction")
    print("  - Improved data validation and cleaning")
    print("  - Multiple analysis sheets in output")
    print()

    start_time = time.time()

    print("Starting enhanced extraction...")
    success = scraper.run_enhanced_extraction(max_urls=args.max_urls)

    end_time = time.time()
    processing_time = end_time - start_time

    if success:
        print(f"\n✓ Enhanced extraction completed successfully!")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Total records extracted: {len(scraper.all_extracted_data)}")

        # Quick stats
        if scraper.all_extracted_data:
            df = pd.DataFrame(scraper.all_extracted_data)
            success_count = len(df[df['extraction_status'] == 'SUCCESS'])
            female_count = len(df[df['is_female'] == True])
            phone_count = len(df[df['phone'].notna()])

            print(f"Quick Statistics:")
            print(f"  - Successful extractions: {success_count}")
            print(f"  - Female profiles: {female_count}")
            print(f"  - With phone numbers: {phone_count}")
            print(f"  - Success rate: {success_count/len(df)*100:.1f}%")

        return 0
    else:
        print(f"\n✗ Enhanced extraction completed with some failures")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Partial results collected: {len(scraper.all_extracted_data)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
