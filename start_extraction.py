#!/usr/bin/env python3
"""
Auto-start extraction script for fresh_all_urls_20250818_234554.json

This script automatically starts the enhanced dedup extractor with the following features:
- Processes fresh_all_urls_20250818_234554.json
- Saves progress every 500 URLs
- Skips URLs with duplicate phone numbers
- Resumes from checkpoints if interrupted

Usage:
    python start_extraction.py
"""

import os
import sys
import logging
from datetime import datetime
from enhanced_dedup_extractor import EnhancedDedupExtractor, load_fresh_urls

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('extraction_process.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Main auto-start function"""
    print("=" * 80)
    print("FRESH URLS DATA EXTRACTION - AUTO START")
    print("=" * 80)
    print("")

    # Check if required file exists
    required_file = "fresh_all_urls_20250818_234554.json"
    if not os.path.exists(required_file):
        logger.error(f"Required file not found: {required_file}")
        return

    # Load URLs and show info
    try:
        urls_data = load_fresh_urls()
        logger.info(f"Loaded {len(urls_data):,} URLs from {required_file}")

        if not urls_data:
            logger.error("No URLs found to process")
            return

        # Show file size
        size_mb = os.path.getsize(required_file) / (1024 * 1024)
        logger.info(f"File size: {size_mb:.2f} MB")

        # Estimate processing time
        estimated_hours = len(urls_data) * 2 / 3600  # 2 seconds per URL
        logger.info(f"Estimated processing time: {estimated_hours:.1f} hours")

    except Exception as e:
        logger.error(f"Error loading URLs: {e}")
        return

    print("")
    print("EXTRACTION FEATURES:")
    print("✓ Saves progress every 500 URLs")
    print("✓ Skips duplicate phone numbers")
    print("✓ Resumes from checkpoints if interrupted")
    print("✓ Extracts: Phone, Name, Age, Location, City, Social Media, Raw Text")
    print("")

    logger.info("Starting enhanced extraction process automatically...")
    logger.info("You can safely interrupt (Ctrl+C) and resume later")
    print("")

    try:
        # Initialize extractor
        extractor = EnhancedDedupExtractor(
            delay=1.0,  # 1 second delay between requests
            max_workers=5,  # 5 concurrent workers
            checkpoint_interval=500  # Save every 500 URLs
        )

        # Process URLs
        results = extractor.process_urls(urls_data)

        # Save final results
        output_file = extractor.save_final_results(results)

        # Final summary
        successful = sum(1 for r in results if r['status'] == 'success')
        skipped = sum(1 for r in results if r['status'] == 'skipped_duplicate')
        failed = len(results) - successful - skipped
        processing_time = datetime.now() - extractor.start_time

        print("")
        print("=" * 80)
        print("EXTRACTION COMPLETE!")
        print("=" * 80)
        print(f"Total URLs processed: {len(results):,}")
        print(f"Successful extractions: {successful:,}")
        print(f"Skipped (duplicate phones): {skipped:,}")
        print(f"Failed extractions: {failed:,}")
        print(f"Unique phone numbers: {len(extractor.seen_phones):,}")
        print(f"Success rate: {successful/len(results)*100:.1f}%")
        print(f"Output file: {output_file}")
        print(f"Processing time: {processing_time}")
        print("")

        # Show some sample results
        if successful > 0:
            print("SAMPLE SUCCESSFUL EXTRACTIONS:")
            success_results = [r for r in results if r['status'] == 'success'][:5]
            for i, result in enumerate(success_results, 1):
                print(f"{i:2d}. Phone: {result.get('phone_number', 'N/A'):<15} "
                      f"Name: {result.get('name', 'N/A'):<20} "
                      f"Age: {result.get('age', 'N/A'):<3} "
                      f"Location: {result.get('location', 'N/A')}")

        print("")
        logger.info("Extraction completed successfully!")

    except KeyboardInterrupt:
        print("\n")
        logger.info("Extraction interrupted by user")
        logger.info("Progress has been saved - you can resume by running this script again")
        print("")
        print("To resume extraction, simply run:")
        print("python start_extraction.py")

    except Exception as e:
        logger.error(f"Extraction error: {e}")
        logger.error("Check the extraction_process.log file for detailed error information")
        print("")
        print("If you encounter persistent errors, you can:")
        print("1. Check your internet connection")
        print("2. Reduce the number of workers (edit max_workers in the script)")
        print("3. Increase the delay between requests (edit delay in the script)")

if __name__ == '__main__':
    main()
