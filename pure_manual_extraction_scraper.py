#!/usr/bin/env python3
"""
Pure Manual Extraction NYC Scraper - No Mistral needed, extract everything from HTML directly
"""

import sys
import os
import json
import time
import threading
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import pandas as pd
import re
from pathlib import Path
from bs4 import BeautifulSoup

from nyc_boroughs_scraper import NYCBoroughsScraper

class PureManualNYCScraper:
    def __init__(self, max_workers: int = 5):
        """Initialize pure manual extraction NYC scraper"""
        
        self.max_workers = max_workers
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results_lock = threading.Lock()
        self.all_extracted_data = []
        self.save_interval = 1000  # Save every 1000 URLs
        self.last_save_count = 0
        
        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] - %(message)s',
            handlers=[
                logging.FileHandler('pure_manual_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"Pure Manual Scraper initialized with {max_workers} workers")
        self.logger.info(f"Approach: 100% Manual extraction from HTML - No Mistral API needed")
        self.logger.info(f"Auto-save enabled: Excel file saved every {self.save_interval} URLs")
    
    def extract_all_data_manually(self, html_content: str, url: str) -> Dict:
        """Extract ALL data manually from HTML - no Mistral needed"""
        
        extracted_data = {
            'url': url,
            'extraction_status': 'FAILED',
            'is_female': False,
            'name': None,
            'age': None,
            'phone': None,
            'description': None,
            'social_media': None,
            'address': None,
            'raw_text': None,
            'post_id': None,
            'scraped_at': datetime.now().isoformat()
        }
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract post ID from URL
            post_id_match = re.search(r'/(\d+)$', url)
            if post_id_match:
                extracted_data['post_id'] = post_id_match.group(1)
            
            # 1. Extract GENDER - from "I am" section
            gender_elem = soup.find('div', class_='i-am')
            if gender_elem:
                gender_value = gender_elem.find('span', class_='iamisee__value')
                if gender_value:
                    gender_text = gender_value.get_text().strip().lower()
                    # Check if it's female/woman
                    if any(word in gender_text for word in ['woman', 'female', 'girl', 'lady']):
                        extracted_data['is_female'] = True
            
            # 2. Extract NAME - from name section
            name_elem = soup.find('div', class_='viewpostname')
            if name_elem:
                name_text = name_elem.get_text().strip()
                # Remove "Nym:" prefix if present
                name_text = re.sub(r'^Nym:\s*', '', name_text)
                if name_text and name_text.lower() not in ['not specified', 'n/a', '']:
                    extracted_data['name'] = name_text
            
            # 3. Extract AGE - from title
            title_elem = soup.find('div', class_='viewposttitle')
            if title_elem:
                age_elem = title_elem.find('span', class_='postTitleAge')
                if age_elem:
                    age_text = age_elem.get_text().strip()
                    if age_text.isdigit():
                        extracted_data['age'] = age_text
            
            # 4. Extract PHONE - from tel links
            phone_elem = soup.find('a', href=re.compile(r'^tel:'))
            if phone_elem:
                phone_href = phone_elem.get('href', '')
                phone_number = phone_href.replace('tel:', '').strip()
                if phone_number:
                    extracted_data['phone'] = phone_number
            
            # 5. Extract DESCRIPTION - from body
            body_elem = soup.find('div', class_='viewpostbody')
            if body_elem:
                description_text = body_elem.get_text().strip()
                if description_text:
                    extracted_data['description'] = description_text
            
            # 6. Get full page text for social media and address extraction
            all_text = soup.get_text()
            extracted_data['raw_text'] = all_text
            
            # 7. Extract SOCIAL MEDIA from full text
            social_media_patterns = [
                r'instagram[:\s]*[@]?[\w\.]+',
                r'snapchat[:\s]*[@]?[\w\.]+', 
                r'twitter[:\s]*[@]?[\w\.]+',
                r'onlyfans[:\s]*[@]?[\w\.]+',
                r'@[\w\.]+',
                r'snap[:\s]*[@]?[\w\.]+',
                r'ig[:\s]*[@]?[\w\.]+',
                r'tiktok[:\s]*[@]?[\w\.]+',
                r'telegram[:\s]*[@]?[\w\.]+',
                r'kik[:\s]*[@]?[\w\.]+',
            ]
            
            social_media_found = []
            for pattern in social_media_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                social_media_found.extend(matches)
            
            if social_media_found:
                # Remove duplicates and clean up
                unique_social = list(set([s.strip() for s in social_media_found if len(s.strip()) > 2]))
                extracted_data['social_media'] = ', '.join(unique_social)
            
            # 8. Extract ADDRESS/LOCATION from full text
            address_patterns = [
                r'\b\d+\s+[A-Za-z\s]+(?:street|st|avenue|ave|road|rd|boulevard|blvd|lane|ln|drive|dr)\b',
                r'\b(?:manhattan|brooklyn|bronx|queens|staten island)\b',
                r'\b\d{5}\b',  # ZIP codes
                r'\b[A-Za-z\s]+,\s*NY\b',
                r'\b[A-Za-z\s]+,\s*New York\b',
                r'\bnyc\b',
                r'\bnew york city\b'
            ]
            
            addresses_found = []
            for pattern in address_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                addresses_found.extend(matches)
            
            if addresses_found:
                # Remove duplicates and clean up
                unique_addresses = list(set([a.strip() for a in addresses_found if len(a.strip()) > 2]))
                extracted_data['address'] = ', '.join(unique_addresses)
            
            # Mark as successful if we extracted at least some data
            if (extracted_data['name'] or extracted_data['phone'] or 
                extracted_data['description'] or extracted_data['social_media']):
                extracted_data['extraction_status'] = 'SUCCESS'
            
            return extracted_data
            
        except Exception as e:
            self.logger.warning(f"Failed to extract data from {url}: {e}")
            extracted_data['extraction_status'] = 'ERROR'
            extracted_data['raw_text'] = f"Extraction error: {str(e)}"
            return extracted_data
    
    def process_combination_manual(self, combo_key: str, combo_data: Dict, worker_id: int) -> List[Dict]:
        """Process a single combination with pure manual extraction"""
        
        borough = combo_data['borough']
        source = combo_data['source']
        urls = combo_data['urls']
        
        self.logger.info(f"Worker {worker_id} processing {borough} ({source}): {len(urls)} URLs - MANUAL EXTRACTION")
        
        if not urls:
            self.logger.info(f"Worker {worker_id} no URLs to process for {borough} ({source})")
            return []
        
        try:
            # Create scraper for this worker (no API key needed for manual extraction)
            scraper = NYCBoroughsScraper("dummy_key")  # We won't use Mistral
            
            all_extracted_data = []
            
            for i, url in enumerate(urls):
                if i > 0 and i % 100 == 0:
                    self.logger.info(f"Worker {worker_id} processed {i}/{len(urls)} URLs")
                
                # Download HTML
                html = scraper.execute_curl_request(url, scraper.dedicated_curl_template)
                
                if html:
                    # Extract all data manually
                    extracted_data = self.extract_all_data_manually(html, url)
                    
                    # Add metadata
                    extracted_data['city'] = borough
                    extracted_data['source'] = source
                    extracted_data['worker_id'] = worker_id
                    
                    all_extracted_data.append(extracted_data)
                else:
                    # Failed to download HTML
                    failed_data = {
                        'url': url,
                        'city': borough,
                        'source': source,
                        'extraction_status': 'DOWNLOAD_FAILED',
                        'is_female': False,
                        'name': None,
                        'age': None,
                        'phone': None,
                        'description': None,
                        'social_media': None,
                        'address': None,
                        'raw_text': 'Failed to download HTML',
                        'post_id': None,
                        'worker_id': worker_id,
                        'scraped_at': datetime.now().isoformat()
                    }
                    all_extracted_data.append(failed_data)
                
                # Small delay to avoid overwhelming servers
                time.sleep(0.05)
            
            self.logger.info(f"Worker {worker_id} completed {borough} ({source}): {len(all_extracted_data)} records extracted")
            
            # Save checkpoint
            checkpoint_file = f"manual_extraction_{borough}_{source}_checkpoint.xlsx"
            if all_extracted_data:
                try:
                    df = pd.DataFrame(all_extracted_data)
                    df.to_excel(checkpoint_file, index=False)
                    self.logger.info(f"Worker {worker_id} saved checkpoint: {checkpoint_file}")
                except Exception as e:
                    self.logger.warning(f"Worker {worker_id} failed to save checkpoint: {e}")
            
            return all_extracted_data
            
        except Exception as e:
            self.logger.error(f"Worker {worker_id} failed processing {borough} ({source}): {e}")
            return []
    
    def save_intermediate_results(self, force_save: bool = False):
        """Save intermediate results every 1000 URLs or when forced"""
        with self.results_lock:
            current_count = len(self.all_extracted_data)
            
            # Check if we should save (every 1000 URLs or forced)
            if force_save or (current_count >= self.last_save_count + self.save_interval):
                if current_count > 0:
                    try:
                        # Create DataFrame
                        df = pd.DataFrame(self.all_extracted_data)
                        
                        # Generate filename with timestamp and count
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"manual_extraction_{current_count}_urls_{timestamp}.xlsx"
                        
                        # Create multiple sheets
                        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                            # All data
                            df.to_excel(writer, sheet_name='ALL_DATA', index=False)
                            
                            # Successful extractions only
                            success_df = df[df['extraction_status'] == 'SUCCESS']
                            if not success_df.empty:
                                success_df.to_excel(writer, sheet_name='SUCCESS', index=False)
                            
                            # Female profiles only
                            female_df = df[df['is_female'] == True]
                            if not female_df.empty:
                                female_df.to_excel(writer, sheet_name='FEMALE_PROFILES', index=False)
                            
                            # Failed extractions for review
                            failed_df = df[df['extraction_status'].isin(['FAILED', 'ERROR', 'DOWNLOAD_FAILED'])]
                            if not failed_df.empty:
                                failed_df.to_excel(writer, sheet_name='FAILED', index=False)
                        
                        self.logger.info(f"📊 MANUAL EXTRACTION SAVE: {current_count} URLs saved to {filename}")
                        
                        # Update last save count
                        self.last_save_count = current_count
                        
                        # Summary statistics
                        success_count = len(success_df) if not success_df.empty else 0
                        female_count = len(female_df) if not female_df.empty else 0
                        aaok_count = len(df[df['source'] == 'aaok'])
                        aypapi_count = len(df[df['source'] == 'aypapi'])
                        
                        self.logger.info(f"   Success: {success_count}, Female: {female_count}")
                        self.logger.info(f"   Sources: aaok={aaok_count}, aypapi={aypapi_count}")
                        
                    except Exception as e:
                        self.logger.error(f"Failed to save intermediate results: {e}")
                        
                return True
            return False

    def run_pure_manual_extraction(self) -> bool:
        """Run pure manual extraction - no Mistral API needed"""
        self.logger.info("=" * 60)
        self.logger.info("PURE MANUAL EXTRACTION - NO MISTRAL API NEEDED")
        self.logger.info("=" * 60)

        # Load URLs data
        if not os.path.exists(self.urls_file):
            self.logger.error(f"URLs file {self.urls_file} not found. Run Phase 1 first.")
            return False

        try:
            with open(self.urls_file, 'r') as f:
                urls_data = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load URLs file: {e}")
            return False

        self.logger.info(f"Using {self.max_workers} parallel workers for manual extraction")
        self.logger.info(f"Manual extraction: Gender, Name, Age, Phone, Social Media, Address")

        # Summary of what we'll process
        total_urls = sum(data['total_urls'] for data in urls_data.values())

        self.logger.info(f"Total URLs to process: {total_urls}")
        self.logger.info(f"Expected output: 100% data capture (no API limits)")
        self.logger.info(f"Extracting: Gender, Name, Age, Phone, Description, Social Media, Address")

        # Process combinations in parallel
        successful_workers = 0
        failed_workers = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_combo = {
                executor.submit(self.process_combination_manual, combo_key, combo_data, i): (combo_key, combo_data)
                for i, (combo_key, combo_data) in enumerate(urls_data.items())
            }

            # Process completed tasks
            for future in as_completed(future_to_combo):
                combo_key, combo_data = future_to_combo[future]
                try:
                    result = future.result()
                    if result is not None:
                        successful_workers += 1

                        # Thread-safe data collection with auto-save
                        with self.results_lock:
                            self.all_extracted_data.extend(result)

                        # Check if we should save intermediate results
                        self.save_intermediate_results()

                        self.logger.info(f"✓ Completed: {combo_data['borough']} from {combo_data['source']} ({len(result)} records)")
                    else:
                        failed_workers += 1
                        self.logger.error(f"✗ Failed: {combo_data['borough']} from {combo_data['source']}")
                except Exception as e:
                    failed_workers += 1
                    self.logger.error(f"✗ Exception in {combo_data['borough']} from {combo_data['source']}: {e}")

        # Force save any remaining data
        self.save_intermediate_results(force_save=True)

        # Save final comprehensive results
        if self.all_extracted_data:
            final_file = "FINAL_manual_extraction_nyc.xlsx"
            try:
                df = pd.DataFrame(self.all_extracted_data)

                # Create comprehensive final file with multiple sheets
                with pd.ExcelWriter(final_file, engine='openpyxl') as writer:
                    # All data
                    df.to_excel(writer, sheet_name='ALL_DATA', index=False)

                    # Successful extractions only
                    success_df = df[df['extraction_status'] == 'SUCCESS']
                    success_df.to_excel(writer, sheet_name='SUCCESS', index=False)

                    # Female profiles only (main target)
                    female_df = df[df['is_female'] == True]
                    female_df.to_excel(writer, sheet_name='FEMALE_PROFILES', index=False)

                    # By source
                    aaok_df = df[df['source'] == 'aaok']
                    aaok_df.to_excel(writer, sheet_name='AAOK_DATA', index=False)

                    aypapi_df = df[df['source'] == 'aypapi']
                    aypapi_df.to_excel(writer, sheet_name='AYPAPI_DATA', index=False)

                    # Failed extractions for manual review
                    failed_df = df[df['extraction_status'].isin(['FAILED', 'ERROR', 'DOWNLOAD_FAILED'])]
                    if not failed_df.empty:
                        failed_df.to_excel(writer, sheet_name='FAILED_FOR_REVIEW', index=False)

                    # Summary statistics
                    summary_data = []

                    # Overall stats
                    total_count = len(df)
                    success_count = len(success_df)
                    female_count = len(female_df)

                    summary_data.append({'Metric': 'Total URLs Processed', 'Count': total_count})
                    summary_data.append({'Metric': 'Successful Extractions', 'Count': success_count})
                    summary_data.append({'Metric': 'Female Profiles', 'Count': female_count})
                    summary_data.append({'Metric': 'Success Rate', 'Count': f"{success_count/total_count*100:.1f}%"})
                    summary_data.append({'Metric': 'Female Rate', 'Count': f"{female_count/total_count*100:.1f}%"})

                    # By source
                    aaok_count = len(aaok_df)
                    aypapi_count = len(aypapi_df)
                    summary_data.append({'Metric': 'AAOK Records', 'Count': aaok_count})
                    summary_data.append({'Metric': 'AYPAPI Records', 'Count': aypapi_count})

                    # By borough
                    boroughs = df['city'].value_counts()
                    for borough, count in boroughs.items():
                        summary_data.append({'Metric': f'{borough} Records', 'Count': count})

                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='SUMMARY', index=False)

                self.logger.info(f"Final manual extraction results saved to: {final_file}")

                # Final summary
                self.logger.info("=" * 60)
                self.logger.info("PURE MANUAL EXTRACTION COMPLETED")
                self.logger.info("=" * 60)
                self.logger.info(f"Successful workers: {successful_workers}, Failed workers: {failed_workers}")
                self.logger.info(f"Total records processed: {len(self.all_extracted_data)}")

                # Detailed breakdown
                success_count = len(df[df['extraction_status'] == 'SUCCESS'])
                female_count = len(df[df['is_female'] == True])
                aaok_count = len(df[df['source'] == 'aaok'])
                aypapi_count = len(df[df['source'] == 'aypapi'])

                self.logger.info(f"  - Successful extractions: {success_count} ({success_count/len(df)*100:.1f}%)")
                self.logger.info(f"  - Female profiles: {female_count} ({female_count/len(df)*100:.1f}%)")
                self.logger.info(f"  - AAOK records: {aaok_count}")
                self.logger.info(f"  - AYPAPI records: {aypapi_count}")

                # Summary by borough
                boroughs = df['city'].value_counts()
                self.logger.info(f"NYC Boroughs processed: {len(boroughs)}")
                for borough, count in boroughs.items():
                    female_borough = len(df[(df['city'] == borough) & (df['is_female'] == True)])
                    self.logger.info(f"  - {borough}: {count} total, {female_borough} female")

                self.logger.info(f"Manual Extraction Benefits:")
                self.logger.info(f"  - 100% data capture (no API limits)")
                self.logger.info(f"  - No token limits or parsing failures")
                self.logger.info(f"  - Complete raw text for additional manual review")
                self.logger.info(f"  - Separate sheets for easy analysis")

            except Exception as e:
                self.logger.error(f"Failed to save final results: {e}")
                return False

        return failed_workers == 0

def main():
    """Main entry point for pure manual NYC scraper"""
    parser = argparse.ArgumentParser(description='Pure Manual NYC Boroughs Scraper - No Mistral API needed')
    parser.add_argument('--workers', type=int, default=5,
                       help='Number of parallel workers (default: 5)')

    args = parser.parse_args()

    # Validate parameters
    if args.workers < 1 or args.workers > 10:
        print("Error: Number of workers must be between 1 and 10")
        return 1

    # Create manual scraper
    scraper = PureManualNYCScraper(max_workers=args.workers)

    print("Pure Manual NYC Boroughs Scraper")
    print("=" * 50)
    print(f"Configuration: {args.workers} workers")
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Features: 100% Manual extraction, No API limits, Complete data capture")
    print("Extracts: Gender, Name, Age, Phone, Description, Social Media, Address")
    print("Benefits:")
    print("  - No Mistral API needed (no costs, no limits)")
    print("  - 100% data capture (no parsing failures)")
    print("  - Complete raw text for manual review")
    print("  - Separate Excel sheets for easy analysis")
    print()

    start_time = time.time()

    print("Starting pure manual extraction...")
    success = scraper.run_pure_manual_extraction()

    end_time = time.time()
    processing_time = end_time - start_time

    if success:
        print(f"\n✓ Pure manual extraction completed successfully!")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Total records extracted: {len(scraper.all_extracted_data)}")
        print(f"Benefits achieved:")
        print(f"  - 100% data capture (no API failures)")
        print(f"  - Complete manual extraction")
        print(f"  - Ready for additional manual review")

        return 0
    else:
        print(f"\n✗ Manual extraction completed with some failures")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Partial results collected: {len(scraper.all_extracted_data)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
