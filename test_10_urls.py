#!/usr/bin/env python3
"""
Quick Test - 10 URLs from position 50 with improved anti-bot detection
"""

import json
import os
from datetime import datetime
from targeted_city_scraper import TargetedCityScraper

def main():
    """Test 10 URLs starting from position 50"""

    print("🧪 Quick Test - 10 URLs from Working Position")
    print("=" * 50)

    # Load URLs file
    urls_file = "targeted_all_urls_20250819_001221.json"

    if not os.path.exists(urls_file):
        print(f"❌ URLs file {urls_file} not found!")
        return

    # Load just 10 URLs from position 50
    try:
        with open(urls_file, 'r', encoding='utf-8') as f:
            all_urls = json.load(f)

        # Get 10 URLs starting from position 50
        test_urls = all_urls[50:60]
        print(f"📂 Testing 10 URLs from positions 50-59")
        print(f"   Total URLs available: {len(all_urls)}")

    except Exception as e:
        print(f"❌ Error loading URLs: {e}")
        return

    # Initialize scraper with new API key
    print(f"\n🚀 Initializing scraper...")
    try:
        scraper = TargetedCityScraper(
            scrapingdog_api_keys=["68a390dbc2920968e9acce34"],
            mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso",
            max_urls_to_process=10
        )
    except Exception as e:
        print(f"❌ Failed to initialize scraper: {e}")
        return

    print(f"✅ Scraper initialized")
    print(f"🎯 Testing 10 URLs with improved anti-bot detection")
    print(f"👥 Using single worker for careful processing")
    print("-" * 50)

    # Process URLs one by one for detailed analysis
    results = []

    for i, url_data in enumerate(test_urls, 1):
        print(f"\n📋 Testing URL {i}/10")
        print(f"   URL: {url_data['url']}")

        try:
            result = scraper.scrape_profile_url(url_data)

            if result:
                results.append(result)

                # Quick analysis of the result
                desc = result.get('description', '')
                name = result.get('name', 'No name')
                phone = result.get('phone', 'No phone')

                print(f"   ✅ Success: {name} | {phone}")

                # Check if it looks like an abuse page
                if ('report abuse' in desc.lower() and
                    'escort south jersey | listing' in desc.lower() and
                    len(desc) < 300):
                    print(f"   ⚠️ Appears to be abuse page")
                else:
                    print(f"   ✅ Appears to be real content ({len(desc)} chars)")

            else:
                print(f"   ❌ Failed to scrape")

        except Exception as e:
            print(f"   ❌ Exception: {e}")

    # Results summary
    print(f"\n📊 QUICK TEST RESULTS")
    print("=" * 30)
    print(f"✅ Successful scrapes: {len(results)}/10")
    print(f"📈 Success rate: {(len(results)/10)*100:.1f}%")
    print(f"🎯 API requests used: {scraper.stats.get('api_requests', 0)}")
    print(f"🔋 API calls remaining: {900 - scraper.current_key_requests}/900")

    if results:
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"quick_test_10_urls_{timestamp}.json"

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        print(f"💾 Results saved to: {results_file}")

        # Show sample result
        print(f"\n📄 SAMPLE RESULT:")
        sample = results[0]
        print(f"   Name: {sample.get('name', 'N/A')}")
        print(f"   Phone: {sample.get('phone', 'N/A')}")
        print(f"   Age: {sample.get('age', 'N/A')}")
        print(f"   City: {sample.get('city', 'N/A')}")
        print(f"   Description: {sample.get('description', 'N/A')[:100]}...")

        # Check content quality
        abuse_count = 0
        for result in results:
            desc = result.get('description', '')
            if ('report abuse' in desc.lower() and len(desc) < 300):
                abuse_count += 1

        if abuse_count == 0:
            print(f"\n🎉 Excellent! No abuse pages detected")
            print(f"✅ Anti-bot measures appear to be working")
        else:
            print(f"\n⚠️ {abuse_count}/{len(results)} results appear to be abuse pages")
            print(f"🔧 May need further anti-bot improvements")
    else:
        print(f"\n❌ No successful results")
        print(f"🔧 Check API key and anti-bot settings")

if __name__ == "__main__":
    main()
