#!/usr/bin/env python3

import pandas as pd
import sys
import os

def add_phone_prefix(input_file, output_file=None):
    """
    Add '1' prefix to phone numbers in Excel file

    Args:
        input_file (str): Path to input Excel file
        output_file (str): Path to output Excel file (optional)
    """

    # Set default output file name if not provided
    if output_file is None:
        name, ext = os.path.splitext(input_file)
        output_file = f"{name}_updated{ext}"

    try:
        # Check if input file exists
        if not os.path.exists(input_file):
            print(f"Error: Input file '{input_file}' not found!")
            return False

        print(f"Reading Excel file: {input_file}")
        # Read the Excel file
        df = pd.read_excel(input_file)

        print(f"File loaded successfully. Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")

        # Find the phone column
        phone_col = None
        possible_phone_columns = ['phone', 'phone_number', 'phonenumber', 'mobile', 'cell', 'telephone']

        # First try exact match
        if 'phone' in df.columns:
            phone_col = 'phone'
        else:
            # Try case-insensitive search
            for col in df.columns:
                if col.lower() in possible_phone_columns:
                    phone_col = col
                    break

        if phone_col is None:
            print("Error: No phone column found!")
            print("Available columns:", list(df.columns))
            print("Please make sure your file has a column named 'phone' or similar")
            return False

        print(f"Found phone column: '{phone_col}'")

        # Count non-empty phone numbers
        non_empty_phones = df[phone_col].notna().sum()
        print(f"Non-empty phone numbers to process: {non_empty_phones}")

        # Function to add prefix
        def add_prefix(phone):
            if pd.isna(phone):
                return phone

            # Convert to string and clean
            phone_str = str(phone).strip()

            # Skip if empty or nan
            if not phone_str or phone_str.lower() == 'nan':
                return phone

            # Remove any non-digit characters for checking
            digits_only = ''.join(filter(str.isdigit, phone_str))

            # Skip if no digits found
            if not digits_only:
                return phone

            # Check if already starts with "1"
            if phone_str.startswith('1'):
                print(f"  Skipping (already has prefix): {phone_str}")
                return phone_str

            # Add "1" prefix
            new_phone = '1' + phone_str
            print(f"  {phone_str} → {new_phone}")
            return new_phone

        # Show first few phone numbers before processing
        print(f"\nFirst 5 phone numbers before processing:")
        for i, phone in enumerate(df[phone_col].head().values):
            if pd.notna(phone):
                print(f"  Row {i+1}: {phone}")

        print(f"\nProcessing phone numbers...")

        # Apply the prefix addition
        df[phone_col] = df[phone_col].apply(add_prefix)

        # Save the updated file
        print(f"\nSaving updated file to: {output_file}")
        df.to_excel(output_file, index=False)

        print(f"\n✅ Success!")
        print(f"📁 Input file: {input_file}")
        print(f"📁 Output file: {output_file}")
        print(f"📊 Processed {non_empty_phones} phone numbers")

        # Show first few phone numbers after processing
        print(f"\nFirst 5 phone numbers after processing:")
        for i, phone in enumerate(df[phone_col].head().values):
            if pd.notna(phone):
                print(f"  Row {i+1}: {phone}")

        return True

    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found!")
        return False
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        return False

def main():
    """Main function to handle command line arguments"""

    # Default input file
    input_file = "final_with_city.xlsx"
    output_file = None

    # Check command line arguments
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]

    print("=" * 50)
    print("Phone Number Prefix Adder")
    print("=" * 50)

    # Process the file
    success = add_phone_prefix(input_file, output_file)

    if success:
        print("\n🎉 Task completed successfully!")
    else:
        print("\n❌ Task failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()