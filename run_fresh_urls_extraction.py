#!/usr/bin/env python3
"""
Runner script for extracting data from fresh_all_urls_20250818_234554.json

This script runs the enhanced dedup extractor with the following features:
- Processes fresh_all_urls_20250818_234554.json
- Saves progress every 500 URLs
- Skips URLs with duplicate phone numbers
- Resumes from checkpoints if interrupted

Usage:
    python run_fresh_urls_extraction.py
"""

import os
import sys
import logging
from datetime import datetime
from enhanced_dedup_extractor import EnhancedDedupExtractor, load_fresh_urls

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if required files and dependencies exist"""
    required_file = "fresh_all_urls_20250818_234554.json"

    if not os.path.exists(required_file):
        logger.error(f"Required file not found: {required_file}")
        return False

    try:
        import requests
        import pandas as pd
        from bs4 import BeautifulSoup
        logger.info("All required dependencies are available")
        return True
    except ImportError as e:
        logger.error(f"Missing dependency: {e}")
        logger.error("Please install required packages: pip install -r requirements.txt")
        return False

def show_file_info():
    """Show information about the JSON file"""
    json_file = "fresh_all_urls_20250818_234554.json"

    try:
        size_mb = os.path.getsize(json_file) / (1024 * 1024)
        logger.info(f"File size: {size_mb:.2f} MB")

        # Load and count URLs
        urls_data = load_fresh_urls()
        logger.info(f"Total URLs to process: {len(urls_data)}")

        if urls_data:
            # Show sample
            sample = urls_data[0]
            logger.info("Sample URL data:")
            for key, value in sample.items():
                logger.info(f"  {key}: {value}")

        return len(urls_data)

    except Exception as e:
        logger.error(f"Error reading file info: {e}")
        return 0

def estimate_processing_time(url_count):
    """Estimate processing time"""
    # Assume 2 seconds per URL on average (including delays and processing)
    estimated_seconds = url_count * 2
    estimated_hours = estimated_seconds / 3600

    logger.info(f"Estimated processing time: {estimated_hours:.1f} hours")
    logger.info("Note: This is approximate and depends on network conditions")

def main():
    """Main runner function"""
    print("=" * 60)
    print("FRESH URLS DATA EXTRACTION")
    print("=" * 60)
    print("")

    # Check requirements
    if not check_requirements():
        return

    # Show file info
    url_count = show_file_info()
    if url_count == 0:
        logger.error("No URLs found to process")
        return

    # Estimate processing time
    estimate_processing_time(url_count)

    print("")
    print("EXTRACTION FEATURES:")
    print("✓ Saves progress every 500 URLs")
    print("✓ Skips duplicate phone numbers")
    print("✓ Resumes from checkpoints if interrupted")
    print("✓ Extracts: Phone, Name, Age, Location, City, Social Media, Raw Text")
    print("")

    # Confirm start
    response = input("Start extraction? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        logger.info("Extraction cancelled")
        return

    print("")
    logger.info("Starting enhanced extraction process...")
    logger.info("You can safely interrupt (Ctrl+C) and resume later")

    try:
        # Initialize and run extractor
        extractor = EnhancedDedupExtractor(
            delay=1.0,  # 1 second delay between requests
            max_workers=5,  # 5 concurrent workers
            checkpoint_interval=500  # Save every 500 URLs
        )

        # Load URLs
        urls_data = load_fresh_urls()

        # Process URLs
        results = extractor.process_urls(urls_data)

        # Save final results
        output_file = extractor.save_final_results(results)

        # Final summary
        successful = sum(1 for r in results if r['status'] == 'success')
        skipped = sum(1 for r in results if r['status'] == 'skipped_duplicate')
        failed = len(results) - successful - skipped

        print("")
        print("=" * 60)
        print("EXTRACTION COMPLETE!")
        print("=" * 60)
        print(f"Total URLs processed: {len(results):,}")
        print(f"Successful extractions: {successful:,}")
        print(f"Skipped (duplicate phones): {skipped:,}")
        print(f"Failed extractions: {failed:,}")
        print(f"Unique phone numbers: {len(extractor.seen_phones):,}")
        print(f"Success rate: {successful/len(results)*100:.1f}%")
        print(f"Output file: {output_file}")
        print(f"Processing time: {datetime.now() - extractor.start_time}")

    except KeyboardInterrupt:
        logger.info("Extraction interrupted by user")
        logger.info("Progress has been saved - you can resume by running this script again")
    except Exception as e:
        logger.error(f"Extraction error: {e}")
        logger.info("Check the logs for details")

if __name__ == '__main__':
    main()
