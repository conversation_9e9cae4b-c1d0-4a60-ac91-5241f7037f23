#!/usr/bin/env python3
"""
Test script for Playwright Enhanced Scraper
Tests basic functionality and compares with curl-based approach
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from playwright_enhanced_scraper import PlaywrightNYCScraper

async def test_single_url():
    """Test single URL extraction"""
    print("\n=== Testing Single URL Extraction ===")

    # Test URLs (replace with actual URLs from your dataset)
    test_urls = [
        # Add some actual URLs from your parallel_nyc_all_urls_deduplicated.json here
    ]

    # Load URLs from file if test_urls is empty
    if not test_urls:
        urls_file = "parallel_nyc_all_urls_deduplicated.json"
        if os.path.exists(urls_file):
            with open(urls_file, 'r') as f:
                url_data = json.load(f)

            # Extract URLs from the nested structure
            all_urls = []
            for borough_source, data in url_data.items():
                if 'urls' in data:
                    all_urls.extend(data['urls'])

            test_urls = all_urls[:3]  # Take first 3 URLs for testing
        else:
            print("❌ No test URLs available and no URLs file found")
            return False

    if not test_urls:
        print("❌ No URLs available for testing")
        return False

    scraper = PlaywrightNYCScraper(max_workers=1, headless=True)

    try:
        print(f"Testing {len(test_urls)} URLs...")

        for i, url in enumerate(test_urls):
            print(f"\nTest {i+1}: {url}")

            # Test fetching content
            content = await scraper.fetch_page_content(url, "test-worker")

            if content:
                print(f"✅ Successfully fetched {len(content)} characters")

                # Test data extraction
                extracted_data = scraper.extract_all_data_enhanced(content, url)

                if extracted_data:
                    print(f"✅ Successfully extracted data:")
                    print(f"   Name: {extracted_data.get('name', 'N/A')}")
                    print(f"   Age: {extracted_data.get('age', 'N/A')}")
                    print(f"   Phone: {extracted_data.get('phone', 'N/A')}")
                    print(f"   Website Type: {extracted_data.get('website_type', 'N/A')}")
                else:
                    print("❌ Failed to extract data")
            else:
                print("❌ Failed to fetch content")

        await scraper.cleanup()
        print("\n✅ Single URL test completed")
        return True

    except Exception as e:
        print(f"❌ Single URL test failed: {e}")
        await scraper.cleanup()
        return False

async def test_batch_processing():
    """Test batch processing with multiple URLs"""
    print("\n=== Testing Batch Processing ===")

    # Load test URLs
    urls_file = "parallel_nyc_all_urls_deduplicated.json"
    if not os.path.exists(urls_file):
        print("❌ URLs file not found for batch testing")
        return False

    with open(urls_file, 'r') as f:
        url_data = json.load(f)

    # Extract URLs from the nested structure
    all_urls = []
    for borough_source, data in url_data.items():
        if 'urls' in data:
            all_urls.extend(data['urls'])

    # Use first 10 URLs for batch testing
    test_urls = all_urls[:10]

    scraper = PlaywrightNYCScraper(max_workers=2, headless=True)

    try:
        print(f"Testing batch processing with {len(test_urls)} URLs using 2 workers...")

        # Process batch
        results = await scraper.process_urls_batch(test_urls, "batch-test-worker")

        print(f"✅ Batch processing completed")
        print(f"   Successfully processed: {len(results)}/{len(test_urls)} URLs")

        if results:
            print(f"   Sample result:")
            sample = results[0]
            print(f"      Name: {sample.get('name', 'N/A')}")
            print(f"      Age: {sample.get('age', 'N/A')}")
            print(f"      Website Type: {sample.get('website_type', 'N/A')}")

        await scraper.cleanup()
        return True

    except Exception as e:
        print(f"❌ Batch processing test failed: {e}")
        await scraper.cleanup()
        return False

async def test_anti_bot_handling():
    """Test handling of anti-bot protection"""
    print("\n=== Testing Anti-Bot Protection Handling ===")

    # This test checks if our scraper can handle JavaScript challenges
    # We'll use a URL that's known to have anti-bot protection

    scraper = PlaywrightNYCScraper(max_workers=1, headless=True)

    try:
        # Load a test URL
        urls_file = "parallel_nyc_all_urls_deduplicated.json"
        if os.path.exists(urls_file):
            with open(urls_file, 'r') as f:
                url_data = json.load(f)

            # Extract URLs from the nested structure
            all_urls = []
            for borough_source, data in url_data.items():
                if 'urls' in data:
                    all_urls.extend(data['urls'])

            test_url = all_urls[0] if all_urls else None
        else:
            test_url = None

        if not test_url:
            print("❌ No test URL available")
            return False

        print(f"Testing anti-bot handling with: {test_url}")

        # Fetch content with extended timeout for anti-bot challenges
        content = await scraper.fetch_page_content(test_url, "anti-bot-test", max_retries=2)

        if content:
            # Check for signs of anti-bot protection
            if "Just a moment" in content or "Enable JavaScript and cookies" in content:
                print("⚠️  Anti-bot protection detected but content was fetched")
            else:
                print("✅ Successfully bypassed any anti-bot protection")

            print(f"✅ Fetched {len(content)} characters of content")

            # Verify it's actual page content, not just a protection screen
            if len(content) > 1000 and any(keyword in content.lower() for keyword in ['profile', 'escort', 'age', 'phone']):
                print("✅ Content appears to be actual page data (not protection screen)")
            else:
                print("⚠️  Content may still be protection screen or incomplete")
        else:
            print("❌ Failed to fetch content (may be blocked by anti-bot protection)")

        await scraper.cleanup()
        return content is not None

    except Exception as e:
        print(f"❌ Anti-bot handling test failed: {e}")
        await scraper.cleanup()
        return False

async def test_website_type_detection():
    """Test website type detection"""
    print("\n=== Testing Website Type Detection ===")

    scraper = PlaywrightNYCScraper(max_workers=1, headless=True)

    test_cases = [
        ("https://aaok.com.listcrawler.eu/post/escorts/usa/new-york/brooklyn/123", "aaok"),
        ("https://aypapi.com.listcrawler.eu/post/escorts/usa/new-york/manhattan/456", "aypapi"),
        ("https://unknown.com/some/path", "unknown")
    ]

    try:
        for url, expected_type in test_cases:
            detected_type = scraper.detect_website_type(url, f"sample content for {expected_type}")

            if detected_type == expected_type:
                print(f"✅ Correctly detected {url} as {detected_type}")
            else:
                print(f"❌ Incorrectly detected {url} as {detected_type} (expected {expected_type})")

        return True

    except Exception as e:
        print(f"❌ Website type detection test failed: {e}")
        return False

async def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Playwright Enhanced Scraper Tests")
    print("=" * 60)

    test_results = []

    # Test website type detection first (doesn't require network)
    result = await test_website_type_detection()
    test_results.append(("Website Type Detection", result))

    # Test single URL
    result = await test_single_url()
    test_results.append(("Single URL", result))

    # Test batch processing
    result = await test_batch_processing()
    test_results.append(("Batch Processing", result))

    # Test anti-bot handling
    result = await test_anti_bot_handling()
    test_results.append(("Anti-Bot Handling", result))

    # Print results summary
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = 0
    total = len(test_results)

    for test_name, passed_test in test_results:
        status = "✅ PASSED" if passed_test else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if passed_test:
            passed += 1

    print("-" * 60)
    print(f"Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Playwright scraper is ready to use.")
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")

    return passed == total

async def main():
    """Main test function"""
    try:
        success = await run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error during testing: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
