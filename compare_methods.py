#!/usr/bin/env python3
"""
Comparison script between curl-based and Playwright-based scraping methods
Shows the difference in handling JavaScript and anti-bot protection
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nyc_boroughs_scraper import NYCBoroughsScraper
from playwright_enhanced_scraper import PlaywrightNYCScraper

class ScrapingMethodComparison:
    def __init__(self):
        self.mistral_api_key = "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G"  # Replace with your key
        self.test_urls = []
        self.results = {
            'curl_method': [],
            'playwright_method': [],
            'comparison_summary': {}
        }

    def load_test_urls(self, max_urls: int = 10) -> bool:
        """Load test URLs from the deduplicated file"""
        urls_file = "parallel_nyc_all_urls_deduplicated.json"

        if not os.path.exists(urls_file):
            print(f"❌ URLs file not found: {urls_file}")
            return False

        try:
            with open(urls_file, 'r') as f:
                all_urls = json.load(f)

            self.test_urls = all_urls[:max_urls]
            print(f"✅ Loaded {len(self.test_urls)} test URLs")
            return True

        except Exception as e:
            print(f"❌ Error loading URLs: {e}")
            return False

    def test_curl_method(self) -> Dict:
        """Test the traditional curl-based method"""
        print("\n" + "="*50)
        print("🔧 TESTING CURL-BASED METHOD")
        print("="*50)

        scraper = NYCBoroughsScraper(self.mistral_api_key)
        results = {
            'method': 'curl',
            'start_time': datetime.now(),
            'successful_extractions': 0,
            'failed_extractions': 0,
            'total_time': 0,
            'detailed_results': [],
            'common_errors': []
        }

        for i, url in enumerate(self.test_urls):
            print(f"\nCurl Test {i+1}/{len(self.test_urls)}: {url[:80]}...")

            start_time = time.time()

            try:
                # Use the existing curl method
                html = scraper.execute_curl_request(url, scraper.dedicated_curl_template)
                fetch_time = time.time() - start_time

                if html and len(html) > 100:
                    # Check for anti-bot protection
                    protection_detected = any(phrase in html for phrase in [
                        "Just a moment",
                        "Enable JavaScript and cookies",
                        "Checking your browser",
                        "Please wait while we verify"
                    ])

                    if protection_detected:
                        print("⚠️  Anti-bot protection detected")
                        results['common_errors'].append("Anti-bot protection")
                        results['failed_extractions'] += 1

                        result_detail = {
                            'url': url,
                            'success': False,
                            'fetch_time': fetch_time,
                            'content_length': len(html),
                            'error': 'Anti-bot protection detected'
                        }
                    else:
                        # Try to extract data
                        data = scraper.extract_dedicated_page_data(html, url)

                        if data and data.get('name'):  # Basic check for successful extraction
                            print(f"✅ Success - Name: {data.get('name')}, Age: {data.get('age')}")
                            results['successful_extractions'] += 1

                            result_detail = {
                                'url': url,
                                'success': True,
                                'fetch_time': fetch_time,
                                'content_length': len(html),
                                'extracted_name': data.get('name'),
                                'extracted_age': data.get('age'),
                                'extracted_phone': data.get('phone')
                            }
                        else:
                            print("❌ Failed to extract meaningful data")
                            results['failed_extractions'] += 1
                            results['common_errors'].append("Data extraction failed")

                            result_detail = {
                                'url': url,
                                'success': False,
                                'fetch_time': fetch_time,
                                'content_length': len(html),
                                'error': 'Data extraction failed'
                            }
                else:
                    print("❌ Failed to fetch content or got empty response")
                    results['failed_extractions'] += 1
                    results['common_errors'].append("Empty or no content")

                    result_detail = {
                        'url': url,
                        'success': False,
                        'fetch_time': fetch_time,
                        'content_length': len(html) if html else 0,
                        'error': 'Empty or no content'
                    }

            except Exception as e:
                print(f"❌ Exception: {str(e)[:100]}")
                results['failed_extractions'] += 1
                results['common_errors'].append(f"Exception: {type(e).__name__}")

                result_detail = {
                    'url': url,
                    'success': False,
                    'fetch_time': time.time() - start_time,
                    'content_length': 0,
                    'error': f"Exception: {str(e)[:100]}"
                }

            results['detailed_results'].append(result_detail)

        results['end_time'] = datetime.now()
        results['total_time'] = (results['end_time'] - results['start_time']).total_seconds()

        # Summary
        print(f"\n📊 CURL METHOD SUMMARY:")
        print(f"   Successful: {results['successful_extractions']}/{len(self.test_urls)}")
        print(f"   Failed: {results['failed_extractions']}/{len(self.test_urls)}")
        print(f"   Total time: {results['total_time']:.1f} seconds")
        print(f"   Average time per URL: {results['total_time']/len(self.test_urls):.1f} seconds")

        return results

    async def test_playwright_method(self) -> Dict:
        """Test the new Playwright-based method"""
        print("\n" + "="*50)
        print("🎭 TESTING PLAYWRIGHT-BASED METHOD")
        print("="*50)

        scraper = PlaywrightNYCScraper(max_workers=1, headless=True)
        results = {
            'method': 'playwright',
            'start_time': datetime.now(),
            'successful_extractions': 0,
            'failed_extractions': 0,
            'total_time': 0,
            'detailed_results': [],
            'common_errors': []
        }

        try:
            for i, url in enumerate(self.test_urls):
                print(f"\nPlaywright Test {i+1}/{len(self.test_urls)}: {url[:80]}...")

                start_time = time.time()

                try:
                    # Fetch content using Playwright
                    html = await scraper.fetch_page_content(url, f"test-worker-{i}")
                    fetch_time = time.time() - start_time

                    if html and len(html) > 100:
                        # Check for anti-bot protection
                        protection_detected = any(phrase in html for phrase in [
                            "Just a moment",
                            "Enable JavaScript and cookies",
                            "Checking your browser",
                            "Please wait while we verify"
                        ])

                        if protection_detected:
                            print("⚠️  Anti-bot protection still present (may need more time)")
                            # Don't immediately mark as failed - Playwright might have bypassed it partially

                        # Try to extract data
                        data = scraper.extract_all_data_enhanced(html, url)

                        if data and data.get('name'):  # Basic check for successful extraction
                            print(f"✅ Success - Name: {data.get('name')}, Age: {data.get('age')}")
                            results['successful_extractions'] += 1

                            result_detail = {
                                'url': url,
                                'success': True,
                                'fetch_time': fetch_time,
                                'content_length': len(html),
                                'extracted_name': data.get('name'),
                                'extracted_age': data.get('age'),
                                'extracted_phone': data.get('phone'),
                                'website_type': data.get('website_type')
                            }
                        else:
                            print("❌ Failed to extract meaningful data")
                            results['failed_extractions'] += 1
                            results['common_errors'].append("Data extraction failed")

                            result_detail = {
                                'url': url,
                                'success': False,
                                'fetch_time': fetch_time,
                                'content_length': len(html),
                                'error': 'Data extraction failed'
                            }
                    else:
                        print("❌ Failed to fetch content or got empty response")
                        results['failed_extractions'] += 1
                        results['common_errors'].append("Empty or no content")

                        result_detail = {
                            'url': url,
                            'success': False,
                            'fetch_time': fetch_time,
                            'content_length': len(html) if html else 0,
                            'error': 'Empty or no content'
                        }

                except Exception as e:
                    print(f"❌ Exception: {str(e)[:100]}")
                    results['failed_extractions'] += 1
                    results['common_errors'].append(f"Exception: {type(e).__name__}")

                    result_detail = {
                        'url': url,
                        'success': False,
                        'fetch_time': time.time() - start_time,
                        'content_length': 0,
                        'error': f"Exception: {str(e)[:100]}"
                    }

                results['detailed_results'].append(result_detail)

        finally:
            await scraper.cleanup()

        results['end_time'] = datetime.now()
        results['total_time'] = (results['end_time'] - results['start_time']).total_seconds()

        # Summary
        print(f"\n📊 PLAYWRIGHT METHOD SUMMARY:")
        print(f"   Successful: {results['successful_extractions']}/{len(self.test_urls)}")
        print(f"   Failed: {results['failed_extractions']}/{len(self.test_urls)}")
        print(f"   Total time: {results['total_time']:.1f} seconds")
        print(f"   Average time per URL: {results['total_time']/len(self.test_urls):.1f} seconds")

        return results

    def analyze_results(self, curl_results: Dict, playwright_results: Dict) -> Dict:
        """Compare the results between both methods"""
        print("\n" + "="*60)
        print("📊 DETAILED COMPARISON ANALYSIS")
        print("="*60)

        analysis = {
            'total_urls_tested': len(self.test_urls),
            'curl_success_rate': curl_results['successful_extractions'] / len(self.test_urls) * 100,
            'playwright_success_rate': playwright_results['successful_extractions'] / len(self.test_urls) * 100,
            'curl_avg_time': curl_results['total_time'] / len(self.test_urls),
            'playwright_avg_time': playwright_results['total_time'] / len(self.test_urls),
            'improvement_details': {},
            'url_by_url_comparison': []
        }

        print(f"📈 SUCCESS RATES:")
        print(f"   Curl method:       {analysis['curl_success_rate']:.1f}% ({curl_results['successful_extractions']}/{len(self.test_urls)})")
        print(f"   Playwright method: {analysis['playwright_success_rate']:.1f}% ({playwright_results['successful_extractions']}/{len(self.test_urls)})")

        improvement = analysis['playwright_success_rate'] - analysis['curl_success_rate']
        if improvement > 0:
            print(f"   🎉 Improvement: +{improvement:.1f} percentage points")
        elif improvement < 0:
            print(f"   📉 Regression: {improvement:.1f} percentage points")
        else:
            print(f"   ➡️  No change in success rate")

        print(f"\n⏱️  PERFORMANCE:")
        print(f"   Curl avg time:       {analysis['curl_avg_time']:.1f} seconds per URL")
        print(f"   Playwright avg time: {analysis['playwright_avg_time']:.1f} seconds per URL")

        time_diff = analysis['playwright_avg_time'] - analysis['curl_avg_time']
        if time_diff > 0:
            print(f"   🐌 Playwright is {time_diff:.1f}s slower (expected due to browser overhead)")
        else:
            print(f"   🏃 Playwright is {abs(time_diff):.1f}s faster")

        # URL-by-URL comparison
        print(f"\n🔍 URL-BY-URL COMPARISON:")
        playwright_fixed = 0
        curl_better = 0
        both_failed = 0
        both_succeeded = 0

        for i in range(len(self.test_urls)):
            curl_result = curl_results['detailed_results'][i]
            playwright_result = playwright_results['detailed_results'][i]

            curl_success = curl_result['success']
            playwright_success = playwright_result['success']

            comparison = {
                'url': self.test_urls[i],
                'curl_success': curl_success,
                'playwright_success': playwright_success,
                'curl_error': curl_result.get('error', ''),
                'playwright_error': playwright_result.get('error', '')
            }

            if not curl_success and playwright_success:
                print(f"   ✅ URL {i+1}: Playwright FIXED a curl failure")
                playwright_fixed += 1
            elif curl_success and not playwright_success:
                print(f"   ❌ URL {i+1}: Curl worked but Playwright FAILED")
                curl_better += 1
            elif not curl_success and not playwright_success:
                print(f"   ⚫ URL {i+1}: Both methods FAILED")
                both_failed += 1
            else:
                print(f"   🟢 URL {i+1}: Both methods SUCCEEDED")
                both_succeeded += 1

            analysis['url_by_url_comparison'].append(comparison)

        analysis['improvement_details'] = {
            'playwright_fixed': playwright_fixed,
            'curl_better': curl_better,
            'both_failed': both_failed,
            'both_succeeded': both_succeeded
        }

        print(f"\n📋 IMPROVEMENT SUMMARY:")
        print(f"   URLs fixed by Playwright: {playwright_fixed}")
        print(f"   URLs where curl was better: {curl_better}")
        print(f"   URLs where both failed: {both_failed}")
        print(f"   URLs where both succeeded: {both_succeeded}")

        # Error analysis
        print(f"\n🐛 COMMON ERRORS:")
        curl_errors = {}
        playwright_errors = {}

        for error in curl_results['common_errors']:
            curl_errors[error] = curl_errors.get(error, 0) + 1

        for error in playwright_results['common_errors']:
            playwright_errors[error] = playwright_errors.get(error, 0) + 1

        print(f"   Curl errors: {dict(curl_errors)}")
        print(f"   Playwright errors: {dict(playwright_errors)}")

        return analysis

    def save_results(self, curl_results: Dict, playwright_results: Dict, analysis: Dict):
        """Save detailed results to JSON file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scraping_method_comparison_{timestamp}.json"

        full_results = {
            'comparison_metadata': {
                'timestamp': timestamp,
                'test_urls_count': len(self.test_urls),
                'test_urls': self.test_urls
            },
            'curl_results': curl_results,
            'playwright_results': playwright_results,
            'analysis': analysis
        }

        try:
            # Convert datetime objects to strings for JSON serialization
            def convert_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                return obj

            import json

            # Convert datetime objects
            full_results_json = json.loads(
                json.dumps(full_results, default=convert_datetime)
            )

            with open(filename, 'w') as f:
                json.dump(full_results_json, f, indent=2)

            print(f"\n💾 Detailed results saved to: {filename}")

        except Exception as e:
            print(f"❌ Error saving results: {e}")

    async def run_comparison(self, max_urls: int = 10):
        """Run the complete comparison"""
        print("🔍 SCRAPING METHODS COMPARISON")
        print("="*60)
        print("This script compares curl-based vs Playwright-based scraping")
        print("to show how Playwright handles JavaScript and anti-bot protection")
        print("="*60)

        # Load test URLs
        if not self.load_test_urls(max_urls):
            return False

        # Test curl method
        curl_results = self.test_curl_method()

        # Test Playwright method
        playwright_results = await self.test_playwright_method()

        # Analyze results
        analysis = self.analyze_results(curl_results, playwright_results)

        # Save results
        self.save_results(curl_results, playwright_results, analysis)

        # Final recommendation
        print("\n" + "="*60)
        print("🎯 RECOMMENDATION")
        print("="*60)

        if analysis['playwright_success_rate'] > analysis['curl_success_rate']:
            improvement = analysis['playwright_success_rate'] - analysis['curl_success_rate']
            print(f"✅ RECOMMENDED: Use Playwright method")
            print(f"   • {improvement:.1f} percentage points higher success rate")
            print(f"   • Better handling of JavaScript and anti-bot protection")
            print(f"   • Fixed {analysis['improvement_details']['playwright_fixed']} URLs that curl couldn't handle")

            if analysis['playwright_avg_time'] > analysis['curl_avg_time']:
                overhead = analysis['playwright_avg_time'] - analysis['curl_avg_time']
                print(f"   • Trade-off: {overhead:.1f}s slower per URL (acceptable for better success rate)")

        elif analysis['curl_success_rate'] > analysis['playwright_success_rate']:
            print(f"⚠️  UNEXPECTED: Curl method performed better")
            print(f"   • This suggests the anti-bot protection may not be the main issue")
            print(f"   • Consider debugging the Playwright implementation")

        else:
            print(f"🤷 INCONCLUSIVE: Both methods performed equally")
            print(f"   • Success rates are the same")
            print(f"   • Consider other factors like stability, maintenance, etc.")

        return True

async def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(description="Compare curl vs Playwright scraping methods")
    parser.add_argument('--max-urls', type=int, default=10, help='Number of URLs to test (default: 10)')

    args = parser.parse_args()

    comparison = ScrapingMethodComparison()

    try:
        success = await comparison.run_comparison(args.max_urls)
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️  Comparison interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
