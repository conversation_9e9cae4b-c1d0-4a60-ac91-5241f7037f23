#!/usr/bin/env python3
"""
Test Working URLs - Start scraping from position 50 with working URLs
Uses the new API key and improved anti-bot detection
"""

import json
import sys
import os
from datetime import datetime
from targeted_city_scraper import TargetedCityScraper

def load_urls_from_position(filename: str, start_position: int = 50, count: int = 100):
    """Load URLs starting from a specific position"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            all_data = json.load(f)

        # Get URLs from the specified position
        selected_urls = all_data[start_position:start_position + count]

        print(f"📂 Loaded {len(selected_urls)} URLs starting from position {start_position}")
        print(f"   Total URLs in file: {len(all_data)}")
        print(f"   Processing range: {start_position} to {start_position + len(selected_urls) - 1}")

        return selected_urls
    except Exception as e:
        print(f"❌ Error loading URLs from {filename}: {e}")
        return None

def main():
    """Main function to test working URLs"""

    print("🚀 Testing Working URLs from Position 50")
    print("=" * 60)

    # Configuration
    urls_file = "targeted_all_urls_20250819_001221.json"
    start_position = 50  # Start from position 50 where we found working URLs
    batch_size = 100     # Process 100 URLs

    # Check if the URLs file exists
    if not os.path.exists(urls_file):
        print(f"❌ URLs file {urls_file} not found!")
        return

    # Load URLs from position 50
    profile_urls = load_urls_from_position(urls_file, start_position, batch_size)
    if not profile_urls:
        print("❌ No URLs loaded, exiting.")
        return

    # Initialize scraper with new API key
    scrapingdog_api_keys = [
        "68a390dbc2920968e9acce34",
    ]

    print("\n🚀 Initializing scraper with new API key")
    print(f"📊 API key: ...{scrapingdog_api_keys[0][-8:]}")
    print(f"🎯 Processing {len(profile_urls)} URLs from position {start_position}")
    print(f"👥 Using 2 workers (reduced for better success rate)")
    print(f"💾 Progress will be saved automatically")

    try:
        scraper = TargetedCityScraper(
            scrapingdog_api_keys=scrapingdog_api_keys,
            mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso",
            max_urls_to_process=len(profile_urls)
        )
    except Exception as e:
        print(f"❌ Failed to initialize scraper: {e}")
        return

    # Run phase 2 scraping
    print(f"\n🎯 Starting Phase 2 scraping from working URLs")
    print(f"📂 Processing URLs {start_position} to {start_position + len(profile_urls) - 1}")
    print("=" * 60)
    print("⚠️  Press Ctrl+C at any time to stop and save progress")
    print("💾 Using improved anti-bot detection with random delays")
    print("🔄 Will retry failed requests with different settings")
    print("-" * 60)

    try:
        results = scraper.phase2_scrape_all_profiles(
            profile_urls=profile_urls,
            max_workers=2,  # Use 2 workers for better success rate
            resume_file=None
        )

        # Save final results with position info
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"working_urls_results_pos{start_position}_{timestamp}.json"

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        # Create a detailed summary
        summary = {
            'start_position': start_position,
            'urls_processed': len(profile_urls),
            'total_profiles_scraped': len(results),
            'duplicates_skipped': scraper.stats.get('duplicates_skipped', 0),
            'failed_extractions': scraper.stats.get('failed_extractions', 0),
            'api_requests_made': scraper.stats.get('api_requests', 0),
            'success_rate': round((len(results) / len(profile_urls)) * 100, 2) if profile_urls else 0,
            'completion_time': timestamp,
            'api_key_used': f"...{scrapingdog_api_keys[0][-8:]}",
            'remaining_api_calls': 900 - scraper.current_key_requests
        }

        summary_file = f"working_urls_summary_pos{start_position}_{timestamp}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"\n🎉 SCRAPING COMPLETED!")
        print(f"📊 Final Results:")
        print(f"   📍 Start position: {start_position}")
        print(f"   🎯 URLs processed: {len(profile_urls)}")
        print(f"   ✅ Profiles scraped: {len(results)}")
        print(f"   📈 Success rate: {summary['success_rate']}%")
        print(f"   🔄 Duplicates skipped: {summary['duplicates_skipped']}")
        print(f"   ❌ Failed extractions: {summary['failed_extractions']}")
        print(f"   🎯 API requests used: {summary['api_requests_made']}")
        print(f"   🔋 API calls remaining: {summary['remaining_api_calls']}/900")
        print(f"   💾 Results saved to: {results_file}")
        print(f"   📋 Summary saved to: {summary_file}")

        # Analyze the results
        if results:
            print(f"\n🔍 CONTENT ANALYSIS:")

            # Check for abuse pages in results
            abuse_count = 0
            valid_count = 0

            for result in results[:5]:  # Check first 5 results
                desc = result.get('description', '')
                if ('report abuse' in desc.lower() and
                    'escort south jersey | listing' in desc.lower() and
                    len(desc) < 300):
                    abuse_count += 1
                else:
                    valid_count += 1

            print(f"   📄 Sample analysis (first 5 results):")
            print(f"   ✅ Apparent real content: {valid_count}")
            print(f"   ⚠️ Possible abuse pages: {abuse_count}")

            if abuse_count == 0:
                print(f"   🎉 Great! No abuse pages detected in sample")
            elif abuse_count < valid_count:
                print(f"   📈 Mostly successful - some URLs may be blocked")
            else:
                print(f"   ⚠️ Many abuse pages detected - may need further optimization")

        # Save to Excel if results exist
        if results:
            try:
                # Create a simple Excel export
                import pandas as pd

                df = pd.DataFrame(results)
                excel_file = f"working_urls_excel_pos{start_position}_{timestamp}.xlsx"
                df.to_excel(excel_file, index=False)
                print(f"   📊 Excel file saved: {excel_file}")
            except Exception as e:
                print(f"   ⚠️ Excel save failed: {e}")

    except KeyboardInterrupt:
        print("\n⚠️ Scraping interrupted by user")
        print("💾 Progress has been saved automatically")
    except Exception as e:
        print(f"\n❌ Scraping error: {e}")
        print("💾 Progress has been saved automatically")

if __name__ == "__main__":
    main()
