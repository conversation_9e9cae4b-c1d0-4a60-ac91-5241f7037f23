#!/usr/bin/env python3
"""
Integrated Scraper: Combines URL extraction with dedicated page scraping
Uses url_scraper.py for Phase 1 (URL extraction) and scraperapi_scraper.py for Phase 2 (page scraping)
"""

import sys
import os
import json
import time
import argparse
from typing import List, Dict, Optional
from datetime import datetime
import logging

from url_scraper import URLScraper
from scraperapi_scraper import ScraperAPIScraper


class IntegratedScraper:
    def __init__(self, mistral_api_key: str = None, scraperapi_key: str = None):
        """Initialize integrated scraper"""
        self.mistral_api_key = mistral_api_key or "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G"
        self.scraperapi_key = scraperapi_key
        self.urls_file = "extracted_urls.json"
        self.final_results_file = "integrated_scraper_results.xlsx"

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('integrated_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def phase1_extract_urls(self, force_refresh: bool = False) -> bool:
        """Phase 1: Extract URLs from search pages"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 1: URL EXTRACTION")
        self.logger.info("=" * 60)

        url_scraper = URLScraper(
            mistral_api_key=self.mistral_api_key,
            output_file=self.urls_file
        )

        success = url_scraper.extract_all_urls(force_refresh=force_refresh)
        if success:
            # Get and log statistics
            stats = url_scraper.get_url_stats()
            if stats:
                self.logger.info(f"Phase 1 completed: {stats['total_urls']} URLs extracted from {stats['total_pages']} pages")
            else:
                self.logger.warning("Phase 1 completed but couldn't get statistics")

        return success

    def phase2_scrape_pages(self, max_urls: Optional[int] = None) -> bool:
        """Phase 2: Scrape dedicated pages using extracted URLs"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 2: DEDICATED PAGE SCRAPING")
        self.logger.info("=" * 60)

        # Check if URLs file exists
        if not os.path.exists(self.urls_file):
            self.logger.error(f"URLs file {self.urls_file} not found. Run Phase 1 first.")
            return False

        # Load URLs
        try:
            with open(self.urls_file, 'r') as f:
                urls_data = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load URLs file: {e}")
            return False

        # Collect all URLs
        all_urls = []
        for combo_key, combo_data in urls_data.items():
            combo_urls = combo_data.get('urls', [])
            # Add metadata to each URL
            for url in combo_urls:
                all_urls.append({
                    'url': url,
                    'borough': combo_data.get('borough', 'Unknown'),
                    'source': combo_data.get('source', 'Unknown'),
                    'combo_key': combo_key
                })

        if max_urls:
            all_urls = all_urls[:max_urls]
            self.logger.info(f"Limited to first {max_urls} URLs for testing")

        self.logger.info(f"Total URLs to scrape: {len(all_urls)}")

        if not all_urls:
            self.logger.warning("No URLs found to scrape")
            return False

        # Create ScraperAPI scraper
        if not self.scraperapi_key:
            self.logger.error("ScraperAPI key is required for Phase 2")
            return False

        scraper = ScraperAPIScraper(api_key=self.scraperapi_key)

        # Extract just the URLs for the scraper
        urls_to_scrape = [item['url'] for item in all_urls]

        # Process URLs
        try:
            self.logger.info("Starting dedicated page scraping...")
            results = scraper.process_urls_parallel(
                urls=urls_to_scrape,
                max_workers=5,
                batch_size=50
            )

            # Add borough and source metadata to results
            enhanced_results = []
            for i, result in enumerate(results):
                if i < len(all_urls):
                    result['borough'] = all_urls[i]['borough']
                    result['source'] = all_urls[i]['source']
                    result['combo_key'] = all_urls[i]['combo_key']
                enhanced_results.append(result)

            # Save results
            success = scraper.save_results(
                results=enhanced_results,
                filename=self.final_results_file
            )

            if success:
                self.logger.info(f"Phase 2 completed: Results saved to {self.final_results_file}")
                return True
            else:
                self.logger.error("Failed to save Phase 2 results")
                return False

        except Exception as e:
            self.logger.error(f"Phase 2 failed: {e}")
            return False

    def get_summary_stats(self) -> Dict:
        """Get summary statistics for the integrated scraping process"""
        stats = {
            'phase1': {'completed': False, 'total_urls': 0, 'total_pages': 0},
            'phase2': {'completed': False, 'profiles_scraped': 0}
        }

        # Phase 1 stats
        if os.path.exists(self.urls_file):
            try:
                url_scraper = URLScraper(output_file=self.urls_file)
                url_stats = url_scraper.get_url_stats()
                if url_stats:
                    stats['phase1']['completed'] = True
                    stats['phase1']['total_urls'] = url_stats['total_urls']
                    stats['phase1']['total_pages'] = url_stats['total_pages']
                    stats['phase1']['by_borough'] = url_stats['by_borough']
            except Exception as e:
                self.logger.warning(f"Failed to get Phase 1 stats: {e}")

        # Phase 2 stats
        if os.path.exists(self.final_results_file):
            try:
                import pandas as pd
                df = pd.read_excel(self.final_results_file)
                stats['phase2']['completed'] = True
                stats['phase2']['profiles_scraped'] = len(df)

                # Count successful vs failed
                if 'name' in df.columns:
                    successful = df['name'].notna().sum()
                    stats['phase2']['successful_profiles'] = int(successful)
                    stats['phase2']['failed_profiles'] = len(df) - int(successful)

            except Exception as e:
                self.logger.warning(f"Failed to get Phase 2 stats: {e}")

        return stats


def main():
    """Main entry point for integrated scraper"""
    parser = argparse.ArgumentParser(description='Integrated NYC Scraper (URL Extraction + Page Scraping)')
    parser.add_argument('--phase', choices=['1', '2', 'both'], default='both',
                       help='Which phase to run: 1=extract URLs, 2=scrape pages, both=run both phases')
    parser.add_argument('--scraperapi-key', required=False,
                       help='ScraperAPI key (required for Phase 2)')
    parser.add_argument('--mistral-key',
                       help='Mistral AI API key (optional - defaults to built-in key)')
    parser.add_argument('--force-refresh', action='store_true',
                       help='Force refresh Phase 1 even if URLs file exists')
    parser.add_argument('--max-urls', type=int,
                       help='Maximum number of URLs to scrape in Phase 2 (for testing)')
    parser.add_argument('--stats', action='store_true',
                       help='Show summary statistics')
    parser.add_argument('--clean', action='store_true',
                       help='Clean up existing files and start fresh')

    args = parser.parse_args()

    # Clean files if requested
    if args.clean:
        files_to_clean = [
            "extracted_urls.json",
            "integrated_scraper_results.xlsx",
            "url_scraper.log",
            "integrated_scraper.log"
        ]

        for file in files_to_clean:
            try:
                if os.path.exists(file):
                    os.remove(file)
                    print(f"Cleaned up: {file}")
            except Exception as e:
                print(f"Failed to clean {file}: {e}")

        print("Cleanup completed. Starting fresh.")

    # Get API keys
    mistral_key = args.mistral_key or "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G"
    scraperapi_key = args.scraperapi_key

    # Validate ScraperAPI key for Phase 2
    if args.phase in ['2', 'both'] and not scraperapi_key:
        print("Error: ScraperAPI key is required for Phase 2")
        print("Please provide --scraperapi-key argument")
        return 1

    # Create scraper
    scraper = IntegratedScraper(
        mistral_api_key=mistral_key,
        scraperapi_key=scraperapi_key
    )

    print("Integrated NYC Boroughs Scraper")
    print("=" * 50)
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Phase 1: URL extraction from search pages")
    print("Phase 2: Dedicated page scraping with ScraperAPI")
    print()

    # Show stats if requested
    if args.stats:
        print("Getting summary statistics...")
        stats = scraper.get_summary_stats()

        print("\nSummary Statistics:")
        print("-" * 30)
        print(f"Phase 1 - URL Extraction:")
        if stats['phase1']['completed']:
            print(f"  ✓ Completed: {stats['phase1']['total_urls']} URLs from {stats['phase1']['total_pages']} pages")
            if 'by_borough' in stats['phase1']:
                for borough, data in stats['phase1']['by_borough'].items():
                    print(f"    {borough}: {data['urls']} URLs")
        else:
            print(f"  ✗ Not completed")

        print(f"\nPhase 2 - Page Scraping:")
        if stats['phase2']['completed']:
            print(f"  ✓ Completed: {stats['phase2']['profiles_scraped']} profiles scraped")
            if 'successful_profiles' in stats['phase2']:
                print(f"    Successful: {stats['phase2']['successful_profiles']}")
                print(f"    Failed: {stats['phase2']['failed_profiles']}")
        else:
            print(f"  ✗ Not completed")
        print()

    start_time = time.time()
    success = True

    # Run Phase 1
    if args.phase in ['1', 'both']:
        print("Starting Phase 1: URL Extraction...")
        success = scraper.phase1_extract_urls(force_refresh=args.force_refresh)
        if not success:
            print("Phase 1 failed!")
            return 1
        print("Phase 1 completed successfully!")
        print()

    # Run Phase 2
    if args.phase in ['2', 'both']:
        print("Starting Phase 2: Dedicated Page Scraping...")
        success = scraper.phase2_scrape_pages(max_urls=args.max_urls)
        if not success:
            print("Phase 2 failed!")
            return 1
        print("Phase 2 completed successfully!")

    end_time = time.time()
    processing_time = end_time - start_time

    print()
    print("=" * 50)
    print("INTEGRATED SCRAPING COMPLETED!")
    print(f"Total processing time: {processing_time/60:.1f} minutes")

    # Final stats
    final_stats = scraper.get_summary_stats()
    if final_stats['phase1']['completed']:
        print(f"URLs extracted: {final_stats['phase1']['total_urls']}")
    if final_stats['phase2']['completed']:
        print(f"Profiles scraped: {final_stats['phase2']['profiles_scraped']}")
        print(f"Results file: {scraper.final_results_file}")

    print("=" * 50)

    return 0


if __name__ == "__main__":
    sys.exit(main())
