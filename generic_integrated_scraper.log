2025-08-12 02:51:08,507 - INFO - ============================================================
2025-08-12 02:51:08,507 - INFO - PHASE 2: DEDICATED PAGE SCRAPING
2025-08-12 02:51:08,507 - INFO - ============================================================
2025-08-12 02:51:08,509 - INFO - Limited to first 5 URLs for testing
2025-08-12 02:51:08,509 - INFO - Total URLs to scrape: 5
2025-08-12 02:51:39,605 - INFO - ============================================================
2025-08-12 02:51:39,606 - INFO - PHASE 1: URL EXTRACTION
2025-08-12 02:51:39,606 - INFO - ============================================================
2025-08-12 02:51:39,607 - WARNING - Mistral AI requested but not available. Install with: pip install mistralai
2025-08-12 02:51:39,607 - INFO - ============================================================
2025-08-12 02:51:39,607 - INFO - EXTRACTING ALL URLS FROM SEARCH PAGES
2025-08-12 02:51:39,607 - INFO - ============================================================
2025-08-12 02:51:39,607 - INFO - URLs file urls_alabama_birmingham.json already exists. Loading existing data...
2025-08-12 02:51:39,608 - INFO - Loaded 2 existing URL collections
2025-08-12 02:51:39,608 - INFO - Phase 1 completed: 2589 URLs extracted from 2 combinations
2025-08-12 02:51:39,608 - INFO -   Birmingham (Alabama): 2589 URLs (sources: aaok, aypapi)
