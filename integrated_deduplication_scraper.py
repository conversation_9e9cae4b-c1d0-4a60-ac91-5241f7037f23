#!/usr/bin/env python3
"""
Integrated Deduplication Scraper: Combines URL extraction with phone number deduplication
Phase 1: Extract URLs from search pages
Phase 2: Extract phone numbers and deduplicate
Phase 3: Full scraping of unique phone numbers only
"""

import sys
import os
import json
import time
import argparse
from typing import List, Dict, Optional
from datetime import datetime
import logging

from generic_url_scraper import GenericURLScraper
from deduplication_scraper import DeduplicationScraper


class IntegratedDeduplicationScraper:
    def __init__(self, mistral_api_key: str = None, scraperapi_keys: List[str] = None):
        """Initialize integrated deduplication scraper"""
        self.mistral_api_key = mistral_api_key or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"
        self.scraperapi_keys = scraperapi_keys or []
        self.urls_file = None  # Will be set based on state/city
        self.phone_db_file = None  # Will be set based on state/city
        self.final_results_file = None  # Will be set based on state/city

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('integrated_deduplication_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_filenames(self, state_name: str, city_name: str = None):
        """Setup filenames based on state and city"""
        safe_state = state_name.lower().replace(' ', '_').replace('-', '_')

        if city_name:
            safe_city = city_name.lower().replace(' ', '_').replace('-', '_')
            self.urls_file = f"urls_{safe_state}_{safe_city}.json"
            self.phone_db_file = f"phones_{safe_state}_{safe_city}.json"
            self.final_results_file = f"deduped_{safe_state}_{safe_city}_results.xlsx"
        else:
            self.urls_file = f"urls_{safe_state}_all.json"
            self.phone_db_file = f"phones_{safe_state}_all.json"
            self.final_results_file = f"deduped_{safe_state}_all_results.xlsx"

    def phase1_extract_urls(self, state_name: str, city_name: str = None,
                           max_cities: int = None, force_refresh: bool = False) -> bool:
        """Phase 1: Extract URLs from search pages"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 1: URL EXTRACTION")
        self.logger.info("=" * 60)

        # Setup filenames
        self.setup_filenames(state_name, city_name)

        url_scraper = GenericURLScraper(
            mistral_api_key=self.mistral_api_key,
            output_file=self.urls_file
        )

        success = url_scraper.extract_all_urls(
            state_name=state_name,
            city_name=city_name,
            max_cities=max_cities,
            force_refresh=force_refresh
        )

        if success:
            # Get and log statistics
            stats = url_scraper.get_url_stats()
            if stats:
                self.logger.info(f"Phase 1 completed: {stats['total_urls']} URLs extracted from {stats['total_combinations']} combinations")

                # Log breakdown by city and source
                for city, data in stats['by_city'].items():
                    sources_str = ', '.join(data['sources'])
                    self.logger.info(f"  {city}: {data['urls']} URLs (sources: {sources_str})")
            else:
                self.logger.warning("Phase 1 completed but couldn't get statistics")

        return success

    def phase2_deduplicate_phones(self, phone_batch_size: int = 20, phone_workers: int = 5,
                                 skip_phone_extraction: bool = False) -> Dict[str, List[str]]:
        """Phase 2: Extract phone numbers and deduplicate URLs"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 2: PHONE NUMBER DEDUPLICATION")
        self.logger.info("=" * 60)

        # Check if URLs file exists
        if not self.urls_file or not os.path.exists(self.urls_file):
            self.logger.error(f"URLs file {self.urls_file} not found. Run Phase 1 first.")
            return {}

        # Load URLs
        try:
            with open(self.urls_file, 'r') as f:
                urls_data = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load URLs file: {e}")
            return {}

        # Extract all URLs from the structured data
        all_urls = []
        for combo_key, combo_data in urls_data.items():
            combo_urls = combo_data.get('urls', [])
            all_urls.extend(combo_urls)

        self.logger.info(f"Total URLs to deduplicate: {len(all_urls)}")

        if not all_urls:
            self.logger.warning("No URLs found to deduplicate")
            return {}

        # Create deduplication scraper (minimal instance just for phone extraction)
        if not self.scraperapi_keys:
            self.logger.error("ScraperAPI keys are required for Phase 2")
            return {}

        dedup_scraper = DeduplicationScraper(
            api_keys=self.scraperapi_keys,
            max_workers=3,
            enable_social_media=False,  # Disable for faster phone-only extraction
            mistral_api_key=None,
            phone_db_file=self.phone_db_file
        )

        # Set deduplication parameters
        dedup_scraper.phone_extraction_batch_size = phone_batch_size
        dedup_scraper.phone_extraction_workers = phone_workers
        dedup_scraper.skip_phone_extraction = skip_phone_extraction

        # Build phone database and get deduplicated groups
        try:
            phone_groups = dedup_scraper.build_phone_database(all_urls)

            self.logger.info("Phase 2 completed: Phone number deduplication finished")
            self.logger.info(f"  Unique URLs: {len(phone_groups.get('unique_urls', []))}")
            self.logger.info(f"  Duplicate URLs: {len(phone_groups.get('duplicate_urls', []))}")
            self.logger.info(f"  No phone URLs: {len(phone_groups.get('no_phone_urls', []))}")

            return phone_groups

        except Exception as e:
            self.logger.error(f"Phase 2 failed: {e}")
            return {}

    def phase3_scrape_deduplicated(self, phone_groups: Dict[str, List[str]],
                                  include_no_phone: bool = True, max_urls: Optional[int] = None,
                                  batch_size: int = 50, max_workers: int = 5) -> bool:
        """Phase 3: Full scraping of deduplicated URLs"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 3: FULL SCRAPING OF DEDUPLICATED URLS")
        self.logger.info("=" * 60)

        # Determine which URLs to scrape
        urls_to_scrape = phone_groups.get("unique_urls", [])

        if include_no_phone and phone_groups.get("no_phone_urls"):
            self.logger.info(f"Including {len(phone_groups['no_phone_urls'])} URLs with no phone numbers")
            urls_to_scrape.extend(phone_groups["no_phone_urls"])

        if max_urls:
            urls_to_scrape = urls_to_scrape[:max_urls]
            self.logger.info(f"Limited to first {max_urls} URLs for scraping")

        self.logger.info(f"Total URLs to scrape: {len(urls_to_scrape)}")

        if not urls_to_scrape:
            self.logger.warning("No URLs to scrape after deduplication")
            return False

        # Create full scraper for final scraping
        if not self.scraperapi_keys:
            self.logger.error("ScraperAPI keys are required for Phase 3")
            return False

        scraper = DeduplicationScraper(
            api_keys=self.scraperapi_keys,
            max_workers=max_workers,
            enable_social_media=True,  # Enable full features for final scraping
            mistral_api_key=self.mistral_api_key,
            phone_db_file=self.phone_db_file
        )

        # Skip phone extraction since we already did it
        scraper.skip_phone_extraction = True

        try:
            self.logger.info(f"Starting full scraping with {max_workers} workers, batch size {batch_size}...")
            results = scraper.process_urls_parallel(urls_to_scrape)

            # Add deduplication metadata to results
            for result in results:
                url = result.get('url', '')
                if url in phone_groups.get("unique_urls", []):
                    result['phone_deduplication'] = 'unique'
                elif url in phone_groups.get("no_phone_urls", []):
                    result['phone_deduplication'] = 'no_phone'
                else:
                    result['phone_deduplication'] = 'unknown'

            # Save results with deduplication info
            success = scraper.save_results_with_dedup_info(results, self.final_results_file)

            if success:
                self.logger.info(f"Phase 3 completed: Results saved to {self.final_results_file}")

                # Log summary statistics
                successful = len([r for r in results if r.get('name')])
                failed = len(results) - successful
                self.logger.info(f"Successfully scraped: {successful} profiles")
                self.logger.info(f"Failed to scrape: {failed} profiles")

                # Log deduplication impact
                total_original = len(phone_groups.get("unique_urls", [])) + len(phone_groups.get("duplicate_urls", [])) + len(phone_groups.get("no_phone_urls", []))
                saved_urls = len(phone_groups.get("duplicate_urls", []))
                if total_original > 0:
                    self.logger.info(f"Deduplication saved {saved_urls} URLs ({saved_urls/total_original*100:.1f}% reduction)")

                return True
            else:
                self.logger.error("Failed to save Phase 3 results")
                return False

        except Exception as e:
            self.logger.error(f"Phase 3 failed: {e}")
            return False

    def get_summary_stats(self) -> Dict:
        """Get comprehensive summary statistics"""
        stats = {
            'phase1': {'completed': False, 'total_urls': 0, 'total_combinations': 0},
            'phase2': {'completed': False, 'unique_phones': 0, 'duplicates_found': 0},
            'phase3': {'completed': False, 'profiles_scraped': 0}
        }

        # Phase 1 stats
        if self.urls_file and os.path.exists(self.urls_file):
            try:
                url_scraper = GenericURLScraper(output_file=self.urls_file)
                url_stats = url_scraper.get_url_stats()
                if url_stats:
                    stats['phase1']['completed'] = True
                    stats['phase1']['total_urls'] = url_stats['total_urls']
                    stats['phase1']['total_combinations'] = url_stats['total_combinations']
                    stats['phase1']['by_state'] = url_stats['by_state']
                    stats['phase1']['by_city'] = url_stats['by_city']
                    stats['phase1']['by_source'] = url_stats['by_source']
            except Exception as e:
                self.logger.warning(f"Failed to get Phase 1 stats: {e}")

        # Phase 2 stats (phone database)
        if self.phone_db_file and os.path.exists(self.phone_db_file):
            try:
                with open(self.phone_db_file, 'r') as f:
                    phone_data = json.load(f)
                stats['phase2']['completed'] = True
                stats['phase2']['unique_phones'] = len(phone_data.get('known_phones', []))
                stats['phase2']['phone_to_url_mappings'] = len(phone_data.get('phone_to_url', {}))
                stats['phase2']['url_to_phone_mappings'] = len(phone_data.get('url_to_phone', {}))
            except Exception as e:
                self.logger.warning(f"Failed to get Phase 2 stats: {e}")

        # Phase 3 stats (final results)
        if self.final_results_file and os.path.exists(self.final_results_file):
            try:
                import pandas as pd
                df = pd.read_excel(self.final_results_file)
                stats['phase3']['completed'] = True
                stats['phase3']['profiles_scraped'] = len(df)

                # Count successful vs failed
                if 'name' in df.columns:
                    successful = df['name'].notna().sum()
                    stats['phase3']['successful_profiles'] = int(successful)
                    stats['phase3']['failed_profiles'] = len(df) - int(successful)

                # Deduplication breakdown
                if 'phone_deduplication' in df.columns:
                    dedup_counts = df['phone_deduplication'].value_counts().to_dict()
                    stats['phase3']['dedup_breakdown'] = dedup_counts

            except Exception as e:
                self.logger.warning(f"Failed to get Phase 3 stats: {e}")

        return stats

    def list_available_states(self) -> List[str]:
        """List all available states"""
        try:
            temp_scraper = GenericURLScraper()
            return temp_scraper.list_available_states()
        except Exception as e:
            self.logger.error(f"Failed to get available states: {e}")
            return []

    def list_cities_in_state(self, state_name: str) -> List[str]:
        """List all available cities in a specific state"""
        try:
            temp_scraper = GenericURLScraper()
            return temp_scraper.list_cities_in_state(state_name)
        except Exception as e:
            self.logger.error(f"Failed to get cities for {state_name}: {e}")
            return []


def main():
    """Main entry point for integrated deduplication scraper"""
    parser = argparse.ArgumentParser(description='Integrated Deduplication Scraper (URL + Phone Dedup + Scraping)')
    parser.add_argument('state', nargs='?', help='State name to scrape (e.g., "Alabama", "New York")')
    parser.add_argument('--city', help='Specific city to scrape (optional - if not provided, scrapes all cities in state)')
    parser.add_argument('--phase', choices=['1', '2', '3', 'all'], default='all',
                       help='Which phase to run: 1=extract URLs, 2=deduplicate phones, 3=scrape deduplicated, all=run all phases')
    parser.add_argument('--scraperapi-keys', nargs='+', required=False,
                       help='ScraperAPI keys (required for Phase 2 and 3)')
    parser.add_argument('--mistral-key',
                       help='Mistral AI API key (optional - defaults to built-in key)')
    parser.add_argument('--force-refresh', action='store_true',
                       help='Force refresh Phase 1 even if URLs file exists')
    parser.add_argument('--max-cities', type=int,
                       help='Maximum number of city-source combinations to process in Phase 1 (for testing)')
    parser.add_argument('--max-urls', type=int,
                       help='Maximum number of URLs to scrape in Phase 3 (for testing)')
    parser.add_argument('--batch-size', type=int, default=50,
                       help='Batch size for Phase 3 scraping (default: 50)')
    parser.add_argument('--max-workers', type=int, default=5,
                       help='Number of parallel workers for Phase 3 (default: 5)')
    parser.add_argument('--phone-batch-size', type=int, default=20,
                       help='Batch size for phone extraction in Phase 2 (default: 20)')
    parser.add_argument('--phone-workers', type=int, default=5,
                       help='Number of workers for phone extraction in Phase 2 (default: 5)')
    parser.add_argument('--skip-phone-extraction', action='store_true',
                       help='Skip phone extraction in Phase 2 (use existing phone database)')
    parser.add_argument('--include-no-phone', action='store_true', default=True,
                       help='Include URLs with no phone numbers in Phase 3 (default: True)')
    parser.add_argument('--delay', type=float, default=0.5,
                       help='Delay between requests in Phase 1 (default: 0.5s)')
    parser.add_argument('--list-states', action='store_true',
                       help='List all available states')
    parser.add_argument('--list-cities', help='List all available cities in specified state')
    parser.add_argument('--stats', action='store_true',
                       help='Show comprehensive summary statistics')
    parser.add_argument('--clean', action='store_true',
                       help='Clean up existing files and start fresh')

    args = parser.parse_args()

    # Get API keys
    mistral_key = args.mistral_key or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"
    scraperapi_keys = args.scraperapi_keys or []

    # Create scraper
    scraper = IntegratedDeduplicationScraper(
        mistral_api_key=mistral_key,
        scraperapi_keys=scraperapi_keys
    )

    # List states if requested
    if args.list_states:
        print("Available states:")
        for state in scraper.list_available_states():
            print(f"  - {state}")
        return 0

    # List cities in state if requested
    if args.list_cities:
        print(f"Available cities in {args.list_cities}:")
        cities = scraper.list_cities_in_state(args.list_cities)
        if cities:
            for city in cities:
                print(f"  - {city}")
        else:
            print(f"No cities found for state: {args.list_cities}")
            print("\nAvailable states:")
            for state in scraper.list_available_states():
                print(f"  - {state}")
        return 0

    # Check if state provided (unless just showing stats)
    if not args.state and not args.stats:
        print("Error: Please provide a state name or use --list-states to see available states")
        print("\nUsage examples:")
        print("  python integrated_deduplication_scraper.py Alabama --scraperapi-keys YOUR_KEY1 YOUR_KEY2")
        print("  python integrated_deduplication_scraper.py 'New York' --city 'New York City' --scraperapi-keys YOUR_KEY")
        print("  python integrated_deduplication_scraper.py --list-states")
        print("  python integrated_deduplication_scraper.py --list-cities Alabama")
        print("  python integrated_deduplication_scraper.py Alabama --phase 1  # Only extract URLs")
        print("  python integrated_deduplication_scraper.py Alabama --max-cities 3 --max-urls 50 --scraperapi-keys YOUR_KEY  # Test run")
        return 1

    # Setup filenames if state provided
    if args.state:
        scraper.setup_filenames(args.state, args.city)

    # Clean files if requested
    if args.clean and args.state:
        files_to_clean = [
            scraper.urls_file,
            scraper.phone_db_file,
            scraper.final_results_file,
            "integrated_deduplication_scraper.log"
        ]

        for file in files_to_clean:
            try:
                if file and os.path.exists(file):
                    os.remove(file)
                    print(f"Cleaned up: {file}")
            except Exception as e:
                print(f"Failed to clean {file}: {e}")

        print("Cleanup completed. Starting fresh.")

    # Validate ScraperAPI keys for Phases 2 and 3
    if args.phase in ['2', '3', 'all'] and not scraperapi_keys:
        print("Error: ScraperAPI keys are required for Phase 2 and 3")
        print("Please provide --scraperapi-keys argument")
        return 1

    print("Integrated Deduplication Scraper")
    print("=" * 60)
    if args.state:
        print(f"Target state: {args.state}")
        if args.city:
            print(f"Target city: {args.city}")
        else:
            print("Target: All cities in state")
    print("Phase 1: URL extraction from search pages")
    print("Phase 2: Phone number deduplication")
    print("Phase 3: Full scraping of unique phone numbers")
    print()

    # Show stats if requested
    if args.stats:
        if not args.state:
            print("Error: Please provide a state name to show statistics")
            return 1

        print("Getting comprehensive statistics...")
        stats = scraper.get_summary_stats()

        print("\nComprehensive Statistics:")
        print("-" * 50)
        print(f"Phase 1 - URL Extraction:")
        if stats['phase1']['completed']:
            print(f"  ✓ Completed: {stats['phase1']['total_urls']} URLs from {stats['phase1']['total_combinations']} combinations")

            if 'by_state' in stats['phase1']:
                for state, data in stats['phase1']['by_state'].items():
                    print(f"    {state}: {data['urls']} URLs from {data['cities']} cities")

        else:
            print(f"  ✗ Not completed")

        print(f"\nPhase 2 - Phone Deduplication:")
        if stats['phase2']['completed']:
            print(f"  ✓ Completed: {stats['phase2']['unique_phones']} unique phone numbers")
            print(f"    Phone-to-URL mappings: {stats['phase2']['phone_to_url_mappings']}")
            print(f"    URL-to-phone mappings: {stats['phase2']['url_to_phone_mappings']}")
        else:
            print(f"  ✗ Not completed")

        print(f"\nPhase 3 - Deduplicated Scraping:")
        if stats['phase3']['completed']:
            print(f"  ✓ Completed: {stats['phase3']['profiles_scraped']} profiles scraped")
            if 'successful_profiles' in stats['phase3']:
                print(f"    Successful: {stats['phase3']['successful_profiles']}")
                print(f"    Failed: {stats['phase3']['failed_profiles']}")

            if 'dedup_breakdown' in stats['phase3']:
                print(f"    Deduplication breakdown:")
                for dedup_type, count in stats['phase3']['dedup_breakdown'].items():
                    print(f"      {dedup_type}: {count}")
        else:
            print(f"  ✗ Not completed")
        print()

        if not (args.phase in ['1', '2', '3', 'all']):
            return 0

    start_time = time.time()
    success = True
    phone_groups = {}

    # Run Phase 1
    if args.phase in ['1', 'all']:
        print("Starting Phase 1: URL Extraction...")
        success = scraper.phase1_extract_urls(
            state_name=args.state,
            city_name=args.city,
            max_cities=args.max_cities,
            force_refresh=args.force_refresh
        )
        if not success:
            print("Phase 1 failed!")
            return 1
        print("Phase 1 completed successfully!")
        print()

    # Run Phase 2
    if args.phase in ['2', 'all']:
        print("Starting Phase 2: Phone Number Deduplication...")
        phone_groups = scraper.phase2_deduplicate_phones(
            phone_batch_size=args.phone_batch_size,
            phone_workers=args.phone_workers,
            skip_phone_extraction=args.skip_phone_extraction
        )
        if not phone_groups:
            print("Phase 2 failed!")
            return 1
        print("Phase 2 completed successfully!")
        print()

    # Run Phase 3
    if args.phase in ['3', 'all']:
        # If we didn't run Phase 2, we need to load the phone groups from the database
        if not phone_groups and args.phase == '3':
            print("Phase 3 requires phone deduplication data. Loading from existing database...")
            # This would require implementing a method to reconstruct phone groups from the database
            print("Note: Direct Phase 3 execution not implemented. Please run Phase 2 first or use 'all'.")
            return 1

        print("Starting Phase 3: Deduplicated Scraping...")
        success = scraper.phase3_scrape_deduplicated(
            phone_groups=phone_groups,
            include_no_phone=args.include_no_phone,
            max_urls=args.max_urls,
            batch_size=args.batch_size,
            max_workers=args.max_workers
        )
        if not success:
            print("Phase 3 failed!")
            return 1
        print("Phase 3 completed successfully!")

    end_time = time.time()
    processing_time = end_time - start_time

    print()
    print("=" * 60)
    print("INTEGRATED DEDUPLICATION SCRAPING COMPLETED!")
    print(f"Total processing time: {processing_time/60:.1f} minutes")

    # Final comprehensive stats
    final_stats = scraper.get_summary_stats()
    if final_stats['phase1']['completed']:
        print(f"URLs extracted: {final_stats['phase1']['total_urls']}")
    if final_stats['phase2']['completed']:
        print(f"Unique phone numbers: {final_stats['phase2']['unique_phones']}")
    if final_stats['phase3']['completed']:
        print(f"Profiles scraped: {final_stats['phase3']['profiles_scraped']}")
        print(f"Results file: {scraper.final_results_file}")

    print("=" * 60)

    return 0


if __name__ == "__main__":
    sys.exit(main())
