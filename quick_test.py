#!/usr/bin/env python3
"""
Quick test script - tests just a few pages to validate the system
"""

import sys
from pathlib import Path
from web_scraper import WebScraper

def quick_validation_test():
    """Test multi-page scraping with the first city-source combination"""
    print("Enhanced Multi-Page Quick Validation Test")
    print("=" * 50)

    # Use your Mistral API key
    scraper = WebScraper(mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso")
    scraper.request_delay = 0.5  # Faster for testing
    scraper.max_pages_per_city = 2  # Test only 2 pages for quick test

    try:
        # Get first city-source combination
        cities = scraper.parse_url_list()
        if not cities:
            print("✗ No cities available for testing")
            return False

        first_city = cities[0]
        print(f"Testing with: {first_city['city']}, {first_city['state']} from {first_city['source']}")
        print(f"Will scrape up to {scraper.max_pages_per_city} pages")

        # Use the new multi-page scraping method
        city_data = scraper.scrape_city_pages(first_city)

        if city_data:
            # Save test results
            scraper.scraped_data = city_data
            scraper.save_to_excel("quick_test_output.xlsx")

            print(f"\n✓ Successfully scraped {len(city_data)} women ≤30 years")
            print("✓ Test data saved to quick_test_output.xlsx")

            # Show sample data
            print("\nSample record:")
            sample = city_data[0]
            for key, value in sample.items():
                if isinstance(value, str) and len(value) > 60:
                    value = value[:60] + "..."
                print(f"  {key}: {value}")

            return True
        else:
            print("✗ No women ≤30 years found in test pages")
            return False

    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        return False

def main():
    """Run quick test"""
    success = quick_validation_test()
    
    if success:
        print("\n" + "=" * 50)
        print("✓ Quick test PASSED! The enhanced scraper is working correctly.")
        print("\nFilters applied:")
        print("  ✓ Age ≤30 years only")
        print("  ✓ Women only")
        print("  ✓ Using aaok and aypapi sources")
        print("\nTo run the full scraper:")
        print("  python web_scraper.py --max-cities 5  # Test with 5 city-source combinations")
        print("  python web_scraper.py --mistral-key YOUR_KEY  # With Mistral AI enhancement")
        print("  python web_scraper.py                 # Full scrape (all cities from both sources)")
        return 0
    else:
        print("\n" + "=" * 50)
        print("✗ Quick test FAILED. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
