<body class="post">

<div id="dataholder" data-page="post" data-view="DEFAULT" data-static-domain="https://static.listcrawler.eu" data-domain="listcrawler.eu" data-url="https://aypapi.com.listcrawler.eu/brief/escorts/usa/bronx/1" data-hilight="noChanges" data-city="bronx" data-subdomain="aypapi" data-inner="true" data-revive="https://itransitauthority.com" data-revive-id="cffd5ce10cdaa652d1dba855eb62bf80" data-eb-provider-list="false" data-rplatform="false">
</div>

<style>
    .modal-fader {
        display: none;
        position: fixed;
        justify-content: center;
        align-items: center;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: #000000a1;
        z-index: 140000;
    }

    .report-modal {
        width: 420px;
        max-width: 90%;
        position: relative;
        background-color: #eeeeee;
        padding: 17px;
        border-radius: 4px;
    }

    #postReportWindowCloseButton {
        position: absolute;
        top: 2px;
        right: 2px;
        line-height: 20px;
        text-align: center;
        width: 21px;
        height: 21px;
        -webkit-border-radius: 22px;
        -moz-border-radius: 22px;
        border-radius: 22px;
        background-color: #000000;
        color: white;
        font-weight: bold;
        font-size: 22px;
        font-family: math;
        cursor: pointer;
    }

    span.caption {
        font-size: 16px;
        font-weight: bold;
    }

    .checkboxes-wrap {
        margin: 16px 0;
    }

    #reportPostDetails {
        resize: none;
        width: 100%;
        margin-bottom: 4px;
        border-radius: 4px;
    }

    #postReportEmailConfirmation {
        display: block;
        width: 200px;
        max-width: 90%;
        border-radius: 4px;
        margin-left: 8px;
    }

    .email-box {
        display: flex;
        align-items: baseline;
        justify-content: center;
    }

    .email-box span {
        display: none;
        margin-left: 6px;
        background-color: red;
        color: white;
        padding: 3px 7px;
    }

    .email-hint {
        text-align: center;
        font-style: italic;
        font-size: 12px;
        color: #4c1230;
        margin-bottom: 16px;
    }

    .button-wrapper {
        display: flex;
        justify-content: center;
    }

    #postReportButtonSend {
        border-radius: 4px;
    }

    .confirmation.modal-fader {
        background-color: unset;
    }

    .confirmation .confirmation-modal {
        position: relative;
        width: 320px;
        max-width: 90%;
        background-color: #eeeeee;
        box-shadow: 0 0 14px 5px rgb(0 0 0 / 50%);
        padding: 12px 24px;
        border-radius: 8px;
    }

    .confirmation .confirmation-modal .text {
        font-family: verdana;
        font-size: 15px;
    }

    .confirmation .confirmation-modal .options {
        text-align: center;
        margin: 12px 0 5px 0;
    }

    .confirmation .confirmation-modal .options > span {
        color: white;
        background-color: grey;
        padding: 6px 18px;
        font-weight: 600;
        cursor: pointer;
    }

    .loader.modal-fader {
        background-color: unset;
    }

    .loader.modal-fader .image {
        position: absolute;
        width: 296px;
        height: 346px;
        z-index: 7000;
        background: url('/static/images/spiffygif_76x76.gif');
        background-repeat: no-repeat;
        background-position: center;
    }

    .message-box {
        text-align: center;
        margin: 10px 0;
        font-size: 12px;
    }

    .confirmation-hint {
        text-align: center;
        font-style: italic;
        font-size: 12px;
        margin: 20px 0 6px 0;
    }

    .error {
        color: red;
    }
</style>

<div class="report modal-fader">
    <form class="report-modal" action="/send_report" method="post">
        <div id="postReportWindowCloseButton">×</div>
        <span class="caption">REPORT ABUSE</span>
        <div class="checkboxes-wrap form-field">
            <input type="hidden" id="reportReference" name="reportReference">
            <input type="hidden" id="from" name="from">

            <input type="hidden" id="reportObjectId" name="reportObjectId" value="191374658">
            <input type="hidden" id="phoneNumber" name="relatedPhoneNumber" value="7866910988">


            <div>
                <input type="radio" id="photos" name="type" value="CONTENT" class="reportType">
                <label for="photos">Content/Photos/Phone</label>
            </div>
            <div>
                <input type="radio" id="fake" name="type" value="SCAM" class="reportType">
                <label for="fake">Scam/Fake</label>
            </div>
            <span class="error"></span>
        </div>

        <div class="details form-field">
            <textarea name="details" rows="5" cols="20" id="reportPostDetails" class="focusToBringUp" placeholder="Provide Details" minlength="3" required="true" maxlength="1024"></textarea>
            <span class="error"></span>
        </div>

        <div class="email-box form-field">
            <label for="postReportEmailConfirmation">Email:</label>
            <input type="email" maxlength="128" name="email" id="postReportEmailConfirmation" class="focusToBringUp">
            <span>Invalid email</span>
            <span class="error"></span>
        </div>
        <div id="errors"></div>
        <div class="email-hint">must authenticate your email for confirmation</div>
        <div class="button-wrapper">
            <input type="submit" id="postReportButtonSend" value="SEND REPORT">
        </div>
    </form>
</div>
<div class="confirmation modal-fader verified">
    <div class="confirmation-modal">
        <div class="text"></div>
        <div class="options"><span>OK</span></div>
    </div>
</div>
<div class="confirmation modal-fader unverified">
    <div class="confirmation-modal">
        <div id="postReportWindowCloseButton">×</div>
        <span class="caption">REPORT ABUSE</span>
        <div class="text"></div>
        <div class="options"><span>OK</span></div>
        <div class="confirmation-hint">
            <div>If you are not getting our verification email,</div>
            <div>then send the details of your Report to:</div>
            <div><EMAIL></div>
        </div>
    </div>
</div>
<div class="validation modal-fader" id="validation-form-errors">
    <div class="validation-modal">
        <div class="reportCloseButton">×</div>
        <span class="caption">VALIDATION MESSAGE</span>
        <div class="text"></div>
        <div class="options"><span>OK</span></div>
    </div>
</div>
<div class="loader modal-fader">
    <div class="image"></div>
</div>

<script>
    var emailRegex = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
    var alertTextDefault = 'For security and to prevent abuse of the report post feature, you must confirm your post report by clicking the confirmation link we will send to the email address you have provided. If the email doesn\'t appear in your inbox, please check your spam folder. Thank You.';
    var htmlAlertTextDefault = '<div class="message-box">Authenticate your Report by clicking<br>the link in the email we have sent you.</div><div class="message-box">CHECK YOUR SPAM FOLDER</div>';
    var from = null;

    $(document).ready(function () {

        var url = new URL(location.href);
        var report_form_errors = url.searchParams.get('report_form_errors');

        if (report_form_errors !== 'undefined' && !$.isEmptyObject(report_form_errors)) {
            openModal('report');
            changeTextInValidationErrorModal(report_form_errors);
            openModal('validation');
        }

        $('#postReportButtonSend').on('click tap', function (e) {
            if (!$('#postReportEmailConfirmation').val()) {
                e.preventDefault();
                changeTextInValidationErrorModal('Email should be not empty');
                openModal('validation');
            } else if (!validateTextByLinks($('#reportPostDetails').val())) {
                e.preventDefault();
                changeTextInValidationErrorModal('Links are not allowed in the form');
                openModal('validation');
            }
        });

        $('.validation .options>span, .validation .reportCloseButton').on('click', function () {
            closeModal('validation');
        });

        var showMessage = url.searchParams.get('show_message');

        if (showMessage === 'report_success') {
            $('.confirmation-modal .text').html(htmlAlertTextDefault);
            openModal('confirmation');
        }

        $('.btn-report').click(function (e) {
            e.preventDefault();
            from = $(this).data("from");
            $(".report-modal #reportReference").val(location.href);
            $(".report-modal #from").val(from);
            openModal('report');
        })

        $('.confirmation .options>span, #postReportWindowCloseButton').on('click tap', function () {
            closeModal('confirmation');
            closeModal('report');
        });

        // $('form.report-modal').on('submit', function () {
        //     e.preventDefault();
        //     console.log("add two fields.")
        //     $('form.report-modal')
        //         .append('<input type="hidden" id="reportReference" name="reportReference" value=' + location.href + '>')
        //         .append('<input type="hidden" id="from" name="from" value=' + from + '>');
        //     $(this).submit();
        // });
    });

    function validateEmail(email) {
        return new RegExp(emailRegex).test(email);
    }

    function validateTextByLinks(text) {
        var urlRegex = /(https?:\/\/[^\s]+)/g;
        var urlsFound = text.match(urlRegex);

        return urlsFound == null;
    }

    function openModal(name/*, reportObjectId*/) {

        $('.' + name + '.modal-fader')
            .css({'display': 'flex'})
            .hide()
            .fadeIn();
    }

    function changeTextInValidationErrorModal(text) {
        $(".validation .text").text(text);
    }

    function closeModal(name) {
        $('.' + name + '.modal-fader').fadeOut();
    }
</script>

<div class="wrapper">
    <div id="aboutSourcePanel">
        <div id="clloseXButtonnAbout">
            <img src="https://static.listcrawler.eu/static/img/newCloseBtn.png" alt="Panel">
        </div>










        <div><img src="https://static.listcrawler.eu/static/img/topSourcePanelHeader.png"></div>
        <div id="tspOrangeLine"></div>
        <div id="aboutContainer">










            <div class="aboutIcon">
                <img class="aboutIconImg" alt="ListCrawler" src="https://static.listcrawler.eu/static/img/aboutIconQuestion.png">
            </div>
            <div class="aboutHeader">What is ListCrawler?</div>
            <p class="cleared"><span class="aboutPanelItalicBold">ListCrawler</span> is a Mobile Classifieds List-Viewer
                displaying daily Classified Ads from
                a variety of independent sources all over the world.
                ListCrawler allows you to view the products you desire
                from <span class="aboutPanelItalicBold">all available Lists</span>.</p>

            <p class="cleared">The Category that you are currently viewing is:<br>
                <span class="aboutPanelBlue">&nbsp;ADULT(Escorts)</span><br>
                This section gives you access to all Posts from the following Sources:<br>
                <span class="aboutPanelOrange">Cheepo's List</span><br>
                <span class="aboutPanelOrange">Escort Babylon</span><br>
                <span class="aboutPanelOrange">MegaPersonals</span><br>
                <span class="aboutPanelOrange">City Pages (TransX)</span><br>
                <span class="aboutPanelOrange">King-Dong Ent (Spazilla)</span></p>
            <div id="swicthPanel">VIEW LISTS HERE</div>
            <br>

            <div class="aboutIcon">
                <img class="aboutIconImg" alt="ListCrawler" src="https://static.listcrawler.eu/static/img/aboutIconLCPlus.png">
            </div>
            <div class="aboutHeader">
                ListCrawler-Plus
            </div>
            <p class="cleared"><span class="aboutPanelItalicBold">LC+</span> is our Premium Service allowing you to <span class="aboutPanelItalicBold">manage, track and store</span> noteworthy Posts by offering the
                following <span class="aboutPanelItalicBold">Bonus Features:</span></p>


            <div class="aboutIconSmall"><img class="aboutIconImgSmall" alt="ListCrawler" src="https://static.listcrawler.eu/static/img/aboutIconFridge.png">
            </div>
            <div class="aboutHeaderSmall">SAVE POST</div>
            <p class="clearedSmall">Lets you to <span class="aboutPanelItalicBold">Save</span> your selected Posts
                <span class="aboutPanelItalicNew">permanently &amp; anonymously.</span> The
                <span class="aboutPanelBold">Saved Posts are never stored on your device</span>,
                and <span class="aboutPanelBold">do not leave any trace</span> - <span class="aboutPanelRed">yet you can
instantly and securely access them anywhere, anytime.</span></p>

            <div class="aboutIconSmall"><img class="aboutIconImgSmall" alt="ListCrawler" src="https://static.listcrawler.eu/static/img/aboutIconPencil.png">
            </div>
            <div class="aboutHeaderSmall">ADD NOTE</div>
            <p class="clearedSmall">Gives you the ability to make <span class="aboutPanelItalicBold">Private Notes</span>
                and <span class="aboutPanelItalicBold">comments</span> on specific Posts and store them for future
                reference.</p>

            <div class="aboutIconSmall"><img class="aboutIconImgSmall" alt="ListCrawler" src="https://static.listcrawler.eu/static/img/aboutIconBulb.png"></div>
            <div class="aboutHeaderSmall">HILIGHT POST</div>
            <p class="clearedSmall">Permits you to
                <span class="aboutPanelItalicBold">temporarily mark</span> select Posts for easy and quick
                visibility. This is especially useful for comparison
                shopping while considering options.
                "<span class="aboutPanelBold">Hilight</span>"
                compiles all Marked Posts under a single "<span class="aboutPanelBold">Hilights List</span>".<span class="aboutPanelItalicNew"> (Hilighted Posts and are automatically deleted after 6 hours.)</span>
            </p>

            <p class="clearedSmall"><span class="aboutPanelItalicNewSmaller">Copyright © 2013-2016 If you have a post you'd like displayed in

	<a href="/choice/" style="font-style: italic;
								font-size: 12px;
								text-decoration: none;
								color: black;
								font-weight: normal;">ListCrawler,</a>



	 you may place it <a href="https://megapersonals.com/#!/login">
	 HERE
	 </a></span></p>
            <div class="panelCloseBottomButton" id="closeAboutPanelBottomButton"></div>
            <div class="empty_div"><p class="termslink"><a href="/terms_and_conditions">Terms &amp; Privacy</a> | <a href="#" class="showFeedbackPopup">Fan Mail</a> | <a href="#" class="btn-report" data-from="LC_ABOUT">Report
                Issue</a></p></div>
        </div>
    </div>


    <div id="menuhandle" class="pullhandle hidden-lg hidden-md">
    </div>
    <div id="menuoverlay" class="overlay"></div>
    <div id="menupanel" class="rightSidePanelWrapper">

        <div id="sources_panel" class="panel_button" data-subdomain="aypapi">







            <div id="sspHeader"><img src="https://static.listcrawler.eu/static/img/desktopSidePanelHeader.png"></div>

            <div id="sspContainerAdd"><!--whole left container starts-->


                <form id="escortBabylonPostDataHome" method="post" style="display: none" action="https://escortbabylon.net/provider_list/last_review/bronx/1" data-domain="listcrawler.eu">
                    <input type="hidden" name="phone" value="">
                    <input type="hidden" name="post_id" value="">
                    <input type="hidden" name="subdomain" value="aypapi">
                    <input type="hidden" name="cityv" value="bronx">
                    <input type="hidden" name="cid" value="371">
                    <input type="hidden" name="potential_escort" data-attr="photos" value="">
                    <!--this is to filter potential abusers or prevent escort to create reviews to themselfes-->
                </form>
                <!-- Reviewed -->
                <a href="javascript:{}" onclick="document.getElementById('escortBabylonPostDataHome').submit();" class="nobold">
                    <div class="sspListing  event_reviewed">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/reviewed.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName reviewed">REVIEWED</div>
                            <div class="sspSlogan">Escort Babylon</div>
                        </div>
                    </div>




















                    <!-- Alligator -->
                </a><a href="https://escortalligator.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListing  event_alligator">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/alligator.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName gator">ALLIGATOR</div>
                            <!-- -->
                            <div class="sspSlogan">everything</div>
                        </div>
                    </div>
                </a>
                <!--AAOK-->
                <a href="https://aaok.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListing  event_aaok">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/aaok.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName aaok">AA OK</div>
                            <div class="sspSlogan">black friendly</div>
                        </div>
                    </div>
                </a>
                <!--MAX 80-->
                <a href="https://max80.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListing  event_max80">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/max80.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName max80">MAX 80</div>
                            <div class="sspSlogan">80 or less</div>
                        </div>
                    </div>
                </a>
                <!--CARFUN-->
                <a href="https://carfun.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListing  event_carFun">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/carFun.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName carFun">CAR FUN</div>
                            <div class="sspSlogan">ride it</div>
                        </div>
                    </div>
                </a>
                <!-- Outcall -->
                <a href="https://uberover.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListing  event_outcall">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/outcall.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName outcall">UBER OVER</div>
                            <div class="sspSlogan">outcalls</div>
                        </div>
                    </div>
                </a>
                <!--TRANSX-->
                <a href="https://transx.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListing  event_transX">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/transX.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName transX">TRANS X</div>
                            <div class="sspSlogan">nice dick babe</div>
                        </div>
                    </div>
                </a>
                <!--MAN UP-->
                <a href="https://manup.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListing  event_manUp">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/manUp.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName manUp">MAN UP</div>
                            <div class="sspSlogan">it's cock time</div>
                        </div>
                    </div>
                </a>
            </div><!--whole left container ends-->
            <!--//////////////////////////////////////////////column divider////////////////////////////////////////////////-->
            <div id="sspContainerRight"><!--whole right container starts-->
                <!--YOLO-->
                <a href="https://yolo.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBoldDim">
                    <div class="sspListingRight  event_yolo">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/yolo.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName yolo">YOLO</div>
                            <div class="sspSlogan">20 somethings</div>
                        </div>
                    </div>
                </a>
                <!--MILFY-->
                <a href="https://milfy.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListingRight  event_milfy">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/milfy.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName milfy">MILFY</div>
                            <div class="sspSlogan">milfs</div>
                        </div>
                    </div>
                </a>
                <!--FORTY UP-->
                <a href="https://40up.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListingRight  event_40up">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/40Up.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName fortyup">FORTY UP</div>
                            <div class="sspSlogan">mature</div>
                        </div>
                    </div>
                </a>
                <!--Open24-->
                <a href="https://open24.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListingRight  event_open24">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/open24.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName open24">24/7 LIST</div>
                            <div class="sspSlogan">available now</div>
                        </div>
                    </div>
                </a>
                <!--CANDY-->
                <a href="https://candy.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListingRight  event_candy">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/candy2.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName candy">CANDY</div>
                            <div class="sspSlogan">bbw / curvy</div>
                        </div>
                    </div>
                </a>
                <!--AY PAPI LIST-->
                <a href="https://aypapi.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListingRight  active  event_ayPapi">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/ayPapiBig.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName aypapi">AY PAPI!</div>
                            <div class="sspSlogan">latina</div>
                        </div>
                    </div>
                </a>
                <!--Black Dynomite-->










                <!--HK BOBO-->
                <a href="https://fortunecookies.com.listcrawler.eu/brief/escorts/usa/bronx/1" class="noBold">
                    <div class="sspListingRight  event_superasian">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/fortunecookies.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName hkBobo">COOKIES</div>
                            <div class="sspSlogan">asian babes</div>
                        </div>
                    </div>
                </a>
                <!--FOLLOW-->
                <a href="/follow/brief/1" class="noBold">
                    <div class="sspListingRight ">
                        <div class="sspThumb"><img src="https://static.listcrawler.eu/static/img/newSiteIcons/followingClicklet.png?v=**********">
                        </div>
                        <div class="sspText">
                            <div class="sspSiteName transX">FOLLOWING</div>
                            <div class="sspSlogan">favorited</div>
                        </div>
                    </div>
                </a>
            </div><!--whole right container ends-->
            <div id="container">
                <div id="bottomBtnContainer">

                    <div class="bottomBtnContColumn floatLeftBottom" id="aboutPanelButton">
                        <img src="https://static.listcrawler.eu/static/img/right-panel-buttons/desk/bottomMenuBtnAbout.png" id="bottomMenuBtnAbout">
                    </div>
                    <div class="bottomBtnContColumn floatLeftBottom" id="safeImage">
                        <img src="https://static.listcrawler.eu/static/img/right-panel-buttons/desk/bottomMenuBtnLocked.png" id="bottomMenuBtnLocked">
                        <img src="https://static.listcrawler.eu/static/img/denied_second.png" id="bottomMenuDenied" style="display: none;">
                    </div>
                    <div class="bottomBtnContColumn floatLeftBottom" id="saveButton">
                        <a href="/private/1">
                            <img src="https://static.listcrawler.eu/static/img/right-panel-buttons/desk/bottomMenuBtnSaved.png" id="bottomMenuBtnSaved">
                        </a>
                    </div>
                </div>
            </div>

            <div id="menubanner_container">
                <div id="menubanner_title">
                    <!--   <img src="" />-->
                </div>

                <div class="menubanner_wrapper" align="center">

                    <!-- banner zones now loads dynamically for mobiles, see custom.js #13 #61 -->


                    <!-- Revive Adserver Asynchronous JS Tag - Generated with Revive Adserver v4.1.1 -->
                    <ins data-revive-zoneid="36" data-revive-id="cffd5ce10cdaa652d1dba855eb62bf80" data-revive-seq="0" id="revive-0-0" data-revive-loaded="1" style="text-decoration: none;"><a href="https://itransitauthority.com/www/delivery/ck.php?oaparams=2__bannerid=1293__zoneid=36__cb=967e5effb9__oadest=https%3A%2F%2Fs.zlink7.com%2Fv1%2Fd.php%3Fz%3D5688552" target="_blank"><img src="https://itransitauthority.com/www/images/9664fb9c9499c7a69d8ac3df911b8051.png" width="180" height="20" alt="" title="" border="0"></a><div id="beacon_967e5effb9" style="position: absolute; left: 0px; top: 0px; visibility: hidden;"><img src="https://itransitauthority.com/www/delivery/lg.php?bannerid=1293&amp;campaignid=400&amp;zoneid=36&amp;loc=https%3A%2F%2Faypapi.com.listcrawler.eu%2Fpost%2Fescorts%2Fusa%2Fnewyork%2Fbronx%2F191374658&amp;referer=https%3A%2F%2Faypapi.com.listcrawler.eu%2Fbrief%2Fescorts%2Fusa%2Fbronx%2F1&amp;cb=967e5effb9" width="0" height="0" alt="" style="width: 0px; height: 0px;"></div></ins>
                    <script async="" src="https://itransitauthority.com/www/delivery/asyncjs.php"></script>

                    <!-- Revive Adserver Asynchronous JS Tag - Generated with Revive Adserver v4.1.1 -->
                    <ins data-revive-zoneid="37" data-revive-id="cffd5ce10cdaa652d1dba855eb62bf80" data-revive-seq="1" id="revive-0-1" data-revive-loaded="1" style="text-decoration: none;"><a href="https://itransitauthority.com/www/delivery/ck.php?oaparams=2__bannerid=958__zoneid=37__cb=065b814c59__oadest=https%3A%2F%2Fdirserytrainema.com%2F46bfa87a-2e93-45c7-9ed6-eff5592f2573" target="_blank"><img src="https://itransitauthority.com/www/images/8cc1d7be0553368b727265d973bc9021.png" width="180" height="20" alt="" title="" border="0"></a><div id="beacon_065b814c59" style="position: absolute; left: 0px; top: 0px; visibility: hidden;"><img src="https://itransitauthority.com/www/delivery/lg.php?bannerid=958&amp;campaignid=278&amp;zoneid=37&amp;loc=https%3A%2F%2Faypapi.com.listcrawler.eu%2Fpost%2Fescorts%2Fusa%2Fnewyork%2Fbronx%2F191374658&amp;referer=https%3A%2F%2Faypapi.com.listcrawler.eu%2Fbrief%2Fescorts%2Fusa%2Fbronx%2F1&amp;cb=065b814c59" width="0" height="0" alt="" style="width: 0px; height: 0px;"></div></ins>
                    <script async="" src="https://itransitauthority.com/www/delivery/asyncjs.php"></script>

                    <!-- Revive Adserver Asynchronous JS Tag - Generated with Revive Adserver v4.1.1 -->
                    <ins data-revive-zoneid="38" data-revive-id="cffd5ce10cdaa652d1dba855eb62bf80" data-revive-seq="2" id="revive-0-2" data-revive-loaded="1" style="text-decoration: none;"><a href="https://itransitauthority.com/www/delivery/ck.php?oaparams=2__bannerid=456__zoneid=38__cb=fbd44ed9e3__oadest=https%3A%2F%2Fgo.wheelbunch.com%2F3923d113-cb4e-4b32-aa4b-87627d127635" target="_blank"><img src="https://itransitauthority.com/www/images/db7994d226dae97d0d5906177dfcf0a6.png" width="180" height="20" alt="" title="" border="0"></a><div id="beacon_fbd44ed9e3" style="position: absolute; left: 0px; top: 0px; visibility: hidden;"><img src="https://itransitauthority.com/www/delivery/lg.php?bannerid=456&amp;campaignid=154&amp;zoneid=38&amp;loc=https%3A%2F%2Faypapi.com.listcrawler.eu%2Fpost%2Fescorts%2Fusa%2Fnewyork%2Fbronx%2F191374658&amp;referer=https%3A%2F%2Faypapi.com.listcrawler.eu%2Fbrief%2Fescorts%2Fusa%2Fbronx%2F1&amp;cb=fbd44ed9e3" width="0" height="0" alt="" style="width: 0px; height: 0px;"></div></ins>
                    <script async="" src="https://itransitauthority.com/www/delivery/asyncjs.php"></script>

                    <!-- Revive Adserver Asynchronous JS Tag - Generated with Revive Adserver v4.1.1 -->
                    <ins data-revive-zoneid="41" data-revive-id="cffd5ce10cdaa652d1dba855eb62bf80" data-revive-seq="3" id="revive-0-3" data-revive-loaded="1" style="text-decoration: none;"><a href="https://itransitauthority.com/www/delivery/ck.php?oaparams=2__bannerid=1332__zoneid=41__cb=a0573f269c__oadest=https%3A%2F%2Fwww.zodertracker.com%2Fa76dec0e-8386-4d84-8bdd-931d6f5bf374" target="_blank"><img src="https://itransitauthority.com/www/images/c1775877ed915df22d5575509d07953c.png" width="180" height="20" alt="" title="" border="0"></a><div id="beacon_a0573f269c" style="position: absolute; left: 0px; top: 0px; visibility: hidden;"><img src="https://itransitauthority.com/www/delivery/lg.php?bannerid=1332&amp;campaignid=423&amp;zoneid=41&amp;loc=https%3A%2F%2Faypapi.com.listcrawler.eu%2Fpost%2Fescorts%2Fusa%2Fnewyork%2Fbronx%2F191374658&amp;referer=https%3A%2F%2Faypapi.com.listcrawler.eu%2Fbrief%2Fescorts%2Fusa%2Fbronx%2F1&amp;cb=a0573f269c" width="0" height="0" alt="" style="width: 0px; height: 0px;"></div></ins>
                    <script async="" src="https://itransitauthority.com/www/delivery/asyncjs.php"></script>

                </div>
            </div>

            <div class="hidden-lg hidden-md panelCloseBottomButtonSource" id="closeSideSourcePanelBottomButton"></div>
            <div id="clickToClosePanel"></div>
        </div>

    </div>

    <div id="ageCheckWrapper">
        <div id="ageCheckAgree" class="sidepanel-margin">
            <div class="caption">TERMS OF USE</div>
            <div class="content">
                By clicking the link below you<br>confirm that you are 21 or older<br>you understand that the site<br>may include adult content, you<br>accept our <a href="/terms_and_conditions#cookie-policy">cookie policy</a> and<br>you agree with all the <a href="/terms_and_conditions">terms of use</a>
            </div>
            <div class="button">
                I agree and I am 21+
            </div>
        </div>
    </div>










    <input type="hidden" id="uid" value="">
    <input type="hidden" id="uauth" value="">
    <input type="hidden" id="backToListUrl" data-url="//aypapi.com.listcrawler.eu/brief/escorts/usa/bronx/1" data-anchor="191374658">

    <style>
        .savePost {
            border: none;
            padding: 0;
            font-size: 13px;
            color: #01469C;
            font-weight: bold;
            text-decoration: none;
            height: 61px;
            background-color: transparent;
            padding-bottom: 1px;
            margin: 0 -1px;
            outline: none!important;
            width: auto;
            cursor: pointer;
        }
    </style>




    <style>
        #desktopTopButtons {
            height: 50px;
            width: 600px;
            margin: 0px;
            padding: 0px;
            position: fixed;
            top: 0px;
            z-index: 3330;
            box-shadow: -1px 7px 15px 0px rgba(0, 0, 0, 0.67);
        }
        #desktopTopButtons div {
            float: left;
        }
        #desktopTopButtons img {
            display: block;
            float: left;
            cursor: pointer;
        }
        .savePost {
            border: none;
            padding: 0;
            font-size: 13px;
            color: #01469C;
            font-weight: bold;
            text-decoration: none;
            height: 61px;
            background-color: transparent;
            padding-bottom: 1px;
            max-width:212px;
            width: auto;
        }
        #topMoreAdsBanner {
            position: fixed;
            top: 0px;
            z-index: 1000;
            width: 600px;
            height: 69px;
        }
        #topMoreAdsBanner:after {
            display: table;
            content: " ";
            clear: both;
        }
        #topExternalButtonReviewsDesktop {
            float: left;
            cursor: pointer;
        }
        #topExternalButtonMorePostsDesktop {
            float: left;
            cursor: pointer;
        }
    </style>






    <!--///////////////////desktop version starts if user came from internal link or external///////////////////////-->


    <div id="desktopTopButtons" class="rrr">
        <div id="top_left_post_button">
            <img src="https://static.listcrawler.eu/static/img/desktopTopLeftSpryderButtonPost.png" id="newPostHeaderLogo_Rocky">
        </div>
        <div id="desktop_top_back_button" data-pagination="{json_param|safe}" data-domain="listcrawler.eu" data-url="https://aypapi.com.listcrawler.eu/brief/escorts/usa/bronx/1" data-hilight="noChanges" data-ubonus="" data-city="bronx" data-subdomain="aypapi" data-inner="true">
            <img src="https://static.listcrawler.eu/static/img/desktopTopBackButton.png" id="newPostHeaderLogo_Rocky">
        </div>

        <div id="highlighte_post_buttonNew" pid="191374658">






            <a href="https://escortbabylon.net/review_list/7866910988?gid=b6e89a4635310ff3c32b41f99c2092c6" class="clickToGoToEscortBabylon" border="0">
                <img src="https://static.listcrawler.eu/static/img/desktopHilightUnactive2.png" style="border-style:none;">
            </a>







            <div id="save_post_button" pid="191374658" acive="1">


                <form action="/post_link" method="POST" id="link_save_form" data-action="save">
                    <input type="hidden" name="action" value="save">
                    <input type="hidden" name="pid" value="191374658">
                    <input type="hidden" name="note" class="save-note" value="">
                    <button id="savePost" class="savePost private-button" type="button">
                        <img id="newPostHeaderSave" src="https://static.listcrawler.eu/static/img/desktopSaveUnactive.png" alt="Panel">
                    </button>
                </form>



            </div>
        </div>
    </div>
    <div id="topMoreAdsBanner" style="display: none;">
        <div id="topExternalButtonReviewsDesktop">
            <img src="https://static.listcrawler.eu/static/img/topExternalButtonReviewsDesktop.png" alt="Panel">
        </div>
        <div id="topExternalButtonMorePostsDesktop">
            <img src="https://static.listcrawler.eu/static/img/topExternalButtonMorePostsDesktop.png" alt="Panel">
        </div>
    </div>



    <!--///////////////////desktop version ends ///////////////////////-->





    <div id="rocky_modal">
        <div id="fader"></div>
        <div id="modal_content">
            <div class="notePopUp">
                <div class="notePopUpButtons">
                    <div class="notePopUpBtn" id="dialogNoButton">
                        <img src="https://static.listcrawler.eu/static/img/popupPostSavedNoBtn.png">
                    </div>
                    <div class="notePopUpBtn yes" id="dialogYesButton">
                        <img src="https://static.listcrawler.eu/static/img/popupPostSavedYesBtn.png">
                    </div>
                </div>
            </div>
        </div>
    </div>





















    <form id="escortBabylonPostDataComments" method="post" style="display: none" action="https://escortbabylon.net/comments_list/7866910988?gid=b6e89a4635310ff3c32b41f99c2092c6" data-domain="{{domain}}">
        <input type="hidden" name="phone" value="7866910988">
        <input type="hidden" name="post_id" value="">
        <input type="text" name="user_id" class="userIdFieldToSend" value="">
        <input type="hidden" name="subdomain" value="aypapi">
        <input type="hidden" name="cityv" value="bronx">
        <input type="hidden" name="cid" value="page.cid">
        <input type="hidden" name="potential_escort" data-attr="photos" value="0"> <!--this is to filter potential abusers or prevent escort to create reviews to themselfes-->
    </form>

    <form id="escortBabylonPostData" method="post" style="display: none" action="https://escortbabylon.net/image_list/7866910988?gid=b6e89a4635310ff3c32b41f99c2092c6" data-domain="listcrawler.eu">
        <input type="hidden" name="phone" value="7866910988">
        <input type="hidden" name="post_id" value="">
        <input type="text" name="user_id" class="userIdFieldToSend" value="">
        <input type="hidden" name="subdomain" value="aypapi">
        <input type="hidden" name="cityv" value="bronx">
        <input type="hidden" name="cid" value="page.cid">
        <input type="hidden" name="potential_escort" data-attr="photos" value="0"> <!--this is to filter potential abusers or prevent escort to create reviews to themselfes-->
    </form>

    <form id="escortBabylonPostDataReviews" method="post" style="display: none" action="https://escortbabylon.net/review_list/7866910988?gid=b6e89a4635310ff3c32b41f99c2092c6" data-domain="listcrawler.eu">
        <input type="hidden" name="phone" value="7866910988">
        <input type="hidden" name="post_id" value="">
        <input type="text" name="user_id" class="userIdFieldToSend" id="userIdToReviews" value="">
        <input type="hidden" name="subdomain" value="aypapi">
        <input type="hidden" name="cityv" value="bronx">
        <input type="hidden" name="cid" value="371">
        <input type="hidden" name="potential_escort" data-attr="reviews" value="0}"> <!--this is to filter potential abusers or prevent escort to create reviews to themselfes-->
    </form>




    <a href="#" class="anchorToClickToSendpostData" data-phone="7866910988">
        <img id="moreInfoImagePost" src="https://static.listcrawler.eu/static/img/moreInfoButtonLarge.png" alt="popupPanel" width="250" height="45" style="bottom: 30px;">
    </a>



    <!-- Initialize analytic library -->
    <script>
        var type = '';
        var analyticServer = type == "ad"
            ? '?v='
            : 'https://analytics.appservice.tech/static/tag.js?v=**********';

        (function (d, w, s) {
            var script = d.createElement('script');

            var f = function () {
                var an_args;
                if (type == "ad") {
                    an_args = {
                        log_type: '',
                        ad_platform: '',
                        website: '',
                        zone_id: ''
                    };
                } else {
                    an_args = {
                        city_id: '371',
                        ip: '**************',
                        page: 'post',
                        phone: '7866910988',
                        post_id: '191374658',
                        site: 'listcrawler.eu',
                        user_id: '',
                        referer: 'https://aypapi.com.listcrawler.eu/brief/escorts/usa/bronx/1',
                        url: 'https://aypapi.com.listcrawler.eu/post/escorts/usa/newyork/bronx/191374658',
                        user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                        method: 'load'
                    };
                }

                LcMetrika.send(an_args);

                var da = document.querySelector('[data-analytic]');

                if (!da) return;

                da.onclick = function () {
                    an_args["method"] = 'click';
                    an_args["phone"] = this.getAttribute('href').split('tel:')[1];
                    LcMetrika.send(an_args);
                }
            }

            script.type = "text/javascript";
            script.async = true;
            script.src = s;
            script.onload = script.onreadystatechange = f;

            d.head.appendChild(script);
        })(document, window, analyticServer);
    </script>


















    <div id="contentPostView" class="aypapi ">
        <div id="mainPostDiv" class="" role="mainPost">
            <div id="viewad">



                <div id="gradientPostTitleWithIcon">
                    <div class="starBulletPost"></div>
                    <div class="viewposttitle">
                        SEXY LINDA🇨🇴🍑🔥🥵 AVAILABLE NOW- INCALL ONLY!! - <span class="postTitleAge">20</span>
                    </div>
                </div>
                <div class="viewpostdateEscort">
                    <div class="viewpostname">

                        <span>Nym:&nbsp;</span>LINDA

                    </div>
                    <a href="https://escortbabylon.net/comments_list/7866910988?gid=b6e89a4635310ff3c32b41f99c2092c6" class="goToCommentsButton">

                    </a>



                    <form action="/post_follow" method="POST" id="link_follow_form" data-action="follow">
                        <input type="hidden" name="pid" value="191374658">
                        <button class="postFollow private-button" id="postFollowBtn" type="button">
                        </button>
                    </form>


                    <div style="clear: both"></div>
                </div>





                <div class="i-am-i-see__container">

                    <div class="i-am">
                        <span class="iamisee__caption">I am:</span>&nbsp;
                        <span class="iamisee__value">A woman</span>
                    </div>


                    <div class="i-see">
                        <span class="iamisee__caption">I see:</span>&nbsp;
                        <span class="iamisee__value">

                                Men

                                 only

                        </span>
                    </div>

                </div>
                <br>

                <div class="viewpostbody">Colombiana Available Now for Incall
                    Quiet, Clean, Safe &amp; Discrete 100%
                    Call 347or text 758for 3090Rates &amp; Location </div>

                <div class="userInfoContainer">
                    <div class="viewposttelephone">
                        <a href="tel:7866910988" data-transition="slide" class="normal" data-analytic="click">
                            ************
                        </a>

                    </div>
                    <div class="postCreatedOn">

                        Sun 10 Aug 2025 21:54 PM
                    </div>
                </div>

                <div class="viewpostlocationIconBabylon">
                    <div class="starBulletPostSmallWithIcon aypapi "></div>
                    <ul>
                        <li><span class="postContentBold">Age:&nbsp;</span>20</li>

                        <li><span class="postContentBold">Location:&nbsp;</span>Fordham rd, Webster Ave, White Plains rd, Westchester ave</li>

                    </ul>
                </div>

                <div id="reportButtonContainer">
                    <div id="avoidScamsButton" class="flatButton">
                        <a href="#scamAlert" rel="modal:open">BE SAFE</a>
                    </div>

                    <div id="reportButton" class="flatButton btn-report" data-from="LC_POST">
                        REPORT
                    </div>
                </div>

                <!-- jQuery Modal -->
                <script src="/static/js/lib/jquery.modal.min.js"></script>
                <link rel="stylesheet" href="/static/css/lib/jquery.modal.min.css">
                <div id="scamAlert" class="modal myModal">
                    <div class="text-header">
                        <img src="https://static.listcrawler.eu/static/img/FlyingMoneyIcon.png">
                        <img src="https://static.listcrawler.eu/static/img/FlyingMoneyIcon.png">
                        <span style="color:red;">Avoid Scams</span>
                        <img src="https://static.listcrawler.eu/static/img/FlyingMoneyIcon.png">
                        <img src="https://static.listcrawler.eu/static/img/FlyingMoneyIcon.png">
                    </div>
                    <div class="text-content">
                        Scammers want you to send money in advance and then ghost. We hate these fuckers and do our best to keep them off the site.
                    </div>
                    <div class="text-content">
                        However, it's <strong>also common for legit entertainers to ask for deposits</strong>. They want to be sure they can count on you to show up.
                    </div>
                    <div class="text-subheader">
                        So how do you tell legits from scammers?
                    </div>
                    <div class="text-content">
                        Research. Any member on our site probably has several profiles across other sites. Search their phone number. Check around. Look for reviews, comments, active social media accounts, and personal websites. If post photos and wording is too good to be true or they offer risky fun, it’s probably a scammer.
                    </div>
                    <div class="text-header">
                        <img src="https://static.listcrawler.eu/static/img/MrRude.png">
                        <img src="https://static.listcrawler.eu/static/img/MrRude.png">
                        <span>DON'T BE A JERK</span>
                        <img src="https://static.listcrawler.eu/static/img/MrRude.png">
                        <img src="https://static.listcrawler.eu/static/img/MrRude.png">
                    </div>
                    <div class="text-content">
                        Take time to read and understand what the entertainer offers. Meeting strangers off the internet is risky and can expose one to abuse. If you chat and it doesn’t feel like you have a match, say thanks, have nice day – don’t insult them or be rude. Be nice. <strong><i>It feels good to be nice</i></strong>.
                    </div>
                    <div class="text-content">
                        If you aren’t serious don’t message or call site members. It’s a waste of your time and theirs.
                    </div>
                    <div class="text-content">
                        <strong>Most legit entertainers ask for SCREENINGS with new dates.</strong> This is for their safety, to be sure they aren’t walking into trap – so they can have some comfort about who they meet.
                    </div>
                    <div class="footer-block">
                        <a class="closeMyModal" href="#close-modal" rel="modal:close">OK</a>
                    </div>
                </div>













                <div id="viewPostNoteSave" style="display: none;">
                    <span class="privateListNoteInAd">NOTE:&nbsp;</span>
                    <div class="viewPostNoteEdit">
                        <textarea cols="40" rows="3" class="monospaceFont"></textarea>
                    </div>
                    <button class="note-save-btn">SAVE POST</button>
                </div>

                <div id="viewPostNoteId" class="viewPostNote hiddenStuffFromPostPage">

                </div>



                <div>

                </div>

                <div class="viewpostgallery">

                    <div class="viewpostimage">
                        <img width="300" src="https://img2.drome6.com/imgs/a/d/9/e/ad9e51b92387a359cbbac79eed731454_f.jpg" alt="Escort ************ Fordham rd, Webster Ave, White Plains rd, Westchester ave aypapi">
                    </div>

                    <div class="viewpostimage">
                        <img width="300" src="https://img1.drome6.com/imgs/5/1/0/8/5108c65c83bfde8ec223922a6814f3ad_f.jpg" alt="Escort ************ Fordham rd, Webster Ave, White Plains rd, Westchester ave aypapi">
                    </div>

                    <div class="viewpostimage">
                        <img width="300" src="https://img1.drome6.com/imgs/7/7/f/d/77fd85fc0813ac4df89e4971c2fd7e81_f.jpg" alt="Escort ************ Fordham rd, Webster Ave, White Plains rd, Westchester ave aypapi">
                    </div>

                    <div class="viewpostimage">
                        <img width="300" src="https://img2.drome6.com/imgs/f/a/4/b/fa4bef04319b5ef740f5ee63cee19273_f.jpg" alt="Escort ************ Fordham rd, Webster Ave, White Plains rd, Westchester ave aypapi">
                    </div>

                    <div class="toClearBoth"></div>
                    <div class="toClearBoth"></div>
                    <div class="postBottomOffset"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="auth-container confirm">
        <div class="page-content">
            <div class="modal-content">
                <h3>Create ROGUE NAME?</h3>
                <h4>When you clear your cookies, you will lose all the posts in your <b>SAVED</b> and <b>FOLLOWING</b> lists.</h4>
                <h4>You can get them back by <b>LOGGING IN</b> with your <b>Rogue Name</b>.</h4>
                <h4>Would you like to create a <b>Rogue Name</b> now?</h4>
            </div>
            <div class="button-block">
                <div class="btn-simple btn-yes">Yes, create Name</div>
                <div class="btn-simple btn-no">No, proceed without it</div>
                <div class="btn-simple btn-have">I already have one</div>
            </div>
        </div>
    </div>
    <div class="auth-container iframe">
        <div class="page-content">
            <div class="close-button">×</div>
            <iframe></iframe>
        </div>
    </div>

    <!-- Footer buttons -->
    <img id="clloseButtonnCommon" class="visible-xs-block" src="https://static.listcrawler.eu/static/img/backPanelBtn.png" alt="Panel">










</div> <!-- end wrapper -->



</body>