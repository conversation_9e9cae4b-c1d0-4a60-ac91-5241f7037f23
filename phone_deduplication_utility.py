#!/usr/bin/env python3
"""
Phone Number Deduplication Utility
A utility to add phone number deduplication to existing scrapers
"""

import pandas as pd
import re
import os
import json
from typing import Set, Optional, List
from datetime import datetime
import threading

class PhoneDeduplicationUtility:
    def __init__(self):
        self.scraped_phone_numbers: Set[str] = set()
        self.phone_lock = threading.Lock()
        self.stats = {
            'total_loaded': 0,
            'duplicates_found': 0,
            'files_processed': 0
        }

    def clean_phone_number(self, phone_str: str) -> Optional[str]:
        """Clean phone number for consistent comparison"""
        if not phone_str or pd.isna(phone_str):
            return None

        # Convert to string and remove all non-digits
        phone_clean = re.sub(r'[^\d]', '', str(phone_str))

        # Must be at least 10 digits for US phone numbers
        if len(phone_clean) < 10:
            return None

        # Normalize to 10 digits (remove country code if present)
        if len(phone_clean) == 11 and phone_clean.startswith('1'):
            phone_clean = phone_clean[1:]
        elif len(phone_clean) > 11:
            return None  # Invalid length

        return phone_clean

    def load_existing_phone_numbers(self, directory: str = ".") -> bool:
        """Load phone numbers from existing result files"""
        print("🔍 Loading existing phone numbers from previous results...")

        # Look for existing Excel files with scraped results
        result_files = []
        for f in os.listdir(directory):
            if (f.startswith(('scraperapi_results_', 'enhanced_', 'manual_extraction_', 'final_'))
                and f.endswith('.xlsx')):
                result_files.append(f)

        if not result_files:
            print("⚠️  No existing result files found")
            return False

        total_loaded = 0
        for file_path in result_files:
            try:
                print(f"📂 Checking {file_path}...")
                df = pd.read_excel(file_path)

                if 'phone' in df.columns:
                    # Get valid phone numbers (not empty, not NaN)
                    valid_phones = df[df['phone'].notna() & (df['phone'] != '') & (df['phone'] != 'nan')]['phone']
                    new_phones = set(valid_phones.astype(str))

                    # Clean phone numbers (remove formatting)
                    cleaned_phones = set()
                    for phone in new_phones:
                        cleaned_phone = self.clean_phone_number(phone)
                        if cleaned_phone:
                            cleaned_phones.add(cleaned_phone)

                    # Add to our set
                    before_count = len(self.scraped_phone_numbers)
                    self.scraped_phone_numbers.update(cleaned_phones)
                    added_count = len(self.scraped_phone_numbers) - before_count

                    print(f"   📱 Found {len(new_phones)} phones, added {added_count} new unique numbers")
                    total_loaded += added_count
                    self.stats['files_processed'] += 1

            except Exception as e:
                print(f"   ⚠️ Error loading {file_path}: {e}")
                continue

        self.stats['total_loaded'] = total_loaded
        print(f"✅ Loaded {total_loaded} existing phone numbers from {len(result_files)} files")
        print(f"📊 Total unique phone numbers to skip: {len(self.scraped_phone_numbers)}")
        return True

    def is_phone_duplicate(self, phone: str) -> bool:
        """Check if phone number is already scraped"""
        cleaned_phone = self.clean_phone_number(phone)
        if not cleaned_phone:
            return False

        with self.phone_lock:
            is_duplicate = cleaned_phone in self.scraped_phone_numbers
            if is_duplicate:
                self.stats['duplicates_found'] += 1
            return is_duplicate

    def add_phone_number(self, phone: str) -> bool:
        """Add phone number to scraped set"""
        cleaned_phone = self.clean_phone_number(phone)
        if cleaned_phone:
            with self.phone_lock:
                self.scraped_phone_numbers.add(cleaned_phone)
                return True
        return False

    def filter_urls_by_phone(self, urls: List[str], quick_phone_check_func=None) -> tuple:
        """Filter URLs by checking if their phone numbers are already scraped

        Args:
            urls: List of URLs to check
            quick_phone_check_func: Function that takes (url) and returns phone number

        Returns:
            tuple: (urls_to_process, urls_skipped)
        """
        if not quick_phone_check_func:
            print("⚠️  No phone check function provided, cannot filter URLs")
            return urls, []

        urls_to_process = []
        urls_skipped = []

        print(f"🔍 Checking {len(urls)} URLs for duplicate phone numbers...")

        for i, url in enumerate(urls):
            if i % 100 == 0:
                print(f"   Checked {i}/{len(urls)} URLs")

            try:
                phone = quick_phone_check_func(url)
                if phone and self.is_phone_duplicate(phone):
                    urls_skipped.append({
                        'url': url,
                        'duplicate_phone': self.clean_phone_number(phone),
                        'skip_reason': 'phone_already_scraped'
                    })
                else:
                    urls_to_process.append(url)
            except Exception as e:
                # If phone check fails, include URL for processing
                urls_to_process.append(url)

        print(f"✅ URL filtering complete:")
        print(f"   📋 URLs to process: {len(urls_to_process)}")
        print(f"   🚫 URLs skipped (duplicates): {len(urls_skipped)}")
        print(f"   📊 Deduplication rate: {(len(urls_skipped) / len(urls) * 100):.1f}%")

        return urls_to_process, urls_skipped

    def save_deduplication_report(self, urls_skipped: List[dict], output_file: str = None):
        """Save report of skipped URLs"""
        if not urls_skipped:
            print("ℹ️  No skipped URLs to save")
            return

        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"deduplication_report_{timestamp}.xlsx"

        try:
            df = pd.DataFrame(urls_skipped)
            df.to_excel(output_file, index=False)
            print(f"💾 Deduplication report saved: {output_file}")
        except Exception as e:
            print(f"❌ Error saving deduplication report: {e}")

    def get_stats(self) -> dict:
        """Get deduplication statistics"""
        return {
            **self.stats,
            'unique_phones_loaded': len(self.scraped_phone_numbers)
        }

    def export_phone_numbers(self, output_file: str = None):
        """Export current phone numbers to file for backup"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"phone_numbers_backup_{timestamp}.json"

        try:
            phone_data = {
                'phone_numbers': list(self.scraped_phone_numbers),
                'count': len(self.scraped_phone_numbers),
                'exported_at': datetime.now().isoformat(),
                'stats': self.get_stats()
            }

            with open(output_file, 'w') as f:
                json.dump(phone_data, f, indent=2)

            print(f"💾 Phone numbers exported: {output_file}")
            print(f"📱 Total numbers exported: {len(self.scraped_phone_numbers)}")

        except Exception as e:
            print(f"❌ Error exporting phone numbers: {e}")

    def import_phone_numbers(self, input_file: str) -> bool:
        """Import phone numbers from backup file"""
        try:
            with open(input_file, 'r') as f:
                phone_data = json.load(f)

            imported_phones = set(phone_data.get('phone_numbers', []))
            before_count = len(self.scraped_phone_numbers)

            with self.phone_lock:
                self.scraped_phone_numbers.update(imported_phones)

            added_count = len(self.scraped_phone_numbers) - before_count
            print(f"✅ Imported {added_count} new phone numbers from {input_file}")
            print(f"📱 Total unique phone numbers: {len(self.scraped_phone_numbers)}")

            return True

        except Exception as e:
            print(f"❌ Error importing phone numbers: {e}")
            return False

# Integration example for existing scrapers
class ScraperIntegration:
    """Example integration with existing scrapers"""

    def __init__(self, scraper_instance):
        self.scraper = scraper_instance
        self.dedup = PhoneDeduplicationUtility()

        # Load existing phone numbers
        self.dedup.load_existing_phone_numbers()

    def enhanced_process_url(self, url: str, original_process_func):
        """Enhanced URL processing with deduplication"""

        # Quick phone check (implement based on your scraper)
        try:
            # This would be your scraper's quick phone extraction method
            quick_phone = self.quick_extract_phone(url)

            if quick_phone and self.dedup.is_phone_duplicate(quick_phone):
                print(f"🚫 SKIPPING {url} - Phone {self.dedup.clean_phone_number(quick_phone)} already scraped")
                return {
                    'url': url,
                    'extraction_success': False,
                    'skip_reason': 'phone_already_scraped',
                    'duplicate_phone': self.dedup.clean_phone_number(quick_phone)
                }

        except Exception as e:
            # If quick check fails, proceed with normal processing
            pass

        # Process normally
        result = original_process_func(url)

        # Add phone to deduplication set if successful
        if result.get('extraction_success') and result.get('phone'):
            self.dedup.add_phone_number(result['phone'])

        return result

    def quick_extract_phone(self, url: str) -> Optional[str]:
        """Quick phone extraction - implement based on your scraper"""
        # This should be implemented based on your specific scraper
        # Example implementation:
        try:
            # Use your scraper's method to quickly fetch and extract phone
            # return self.scraper.quick_phone_check(url)
            return None
        except:
            return None

# Usage examples and main function
def example_integration():
    """Example of how to integrate with existing scrapers"""

    print("📋 Phone Deduplication Utility - Integration Examples")
    print("=" * 60)

    # Example 1: Standalone usage
    dedup = PhoneDeduplicationUtility()
    dedup.load_existing_phone_numbers()

    # Check if a phone is duplicate
    test_phone = "************"
    if dedup.is_phone_duplicate(test_phone):
        print(f"📱 {test_phone} is already scraped")
    else:
        print(f"✅ {test_phone} is new")
        dedup.add_phone_number(test_phone)

    # Example 2: URL filtering
    urls = ["http://example1.com", "http://example2.com"]  # Your URLs
    # filtered_urls, skipped = dedup.filter_urls_by_phone(urls, your_quick_phone_function)

    # Example 3: Export/Import
    dedup.export_phone_numbers()

    # Show stats
    stats = dedup.get_stats()
    print(f"\n📊 Deduplication Stats:")
    for key, value in stats.items():
        print(f"   {key}: {value}")

def main():
    """Main function for testing"""

    print("🔧 Phone Deduplication Utility")
    print("=" * 50)

    # Initialize utility
    dedup = PhoneDeduplicationUtility()

    # Load existing phone numbers
    success = dedup.load_existing_phone_numbers()

    if success:
        print(f"\n📊 Current Status:")
        print(f"   Unique phone numbers loaded: {len(dedup.scraped_phone_numbers)}")
        print(f"   Files processed: {dedup.stats['files_processed']}")

        # Export for backup
        dedup.export_phone_numbers()

        # Show sample phone numbers
        if dedup.scraped_phone_numbers:
            sample_phones = list(dedup.scraped_phone_numbers)[:10]
            print(f"\n📱 Sample phone numbers:")
            for phone in sample_phones:
                print(f"   {phone}")

    else:
        print("⚠️  No existing data found to load")

    print(f"\n✅ Utility ready for integration with your scraper!")

if __name__ == "__main__":
    main()
