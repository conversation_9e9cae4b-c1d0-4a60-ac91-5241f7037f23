# Quick Reference - Data Extraction Commands

## 🚀 Most Common Commands

### Process Phase2 Batch Files (RECOMMENDED)
```bash
python run_phase2_extraction.py
```
- Processes all 18 Phase2 batch files (8,815 URLs)
- Phone format: 1xxxxxxxxxx
- Skips duplicates across batches
- Saves after each batch
- ~4.9 hours estimated

### Process Single Large File
```bash
python start_extraction.py
```
- Processes fresh_all_urls_20250818_234554.json (2,152 URLs)
- Checkpoints every 500 URLs
- ~1.2 hours estimated

### Custom File Processing
```bash
# Single file
python run_extractor.py --file your_file.json

# Multiple files by pattern
python run_extractor.py --pattern "fresh_urls_*.json"

# All files
python run_extractor.py --all

# With custom settings
python run_extractor.py --file urls.json --delay 2.0 --workers 3 --limit 100
```

## 📊 What Gets Extracted

✅ Phone Number (1xxxxxxxxxx format)  
✅ Name  
✅ Age  
✅ Location  
✅ City  
✅ Social Media  
✅ Raw Text Description  

## 🔧 Testing Commands

```bash
# Test phone formatting
python test_phone_formatting.py

# Test extraction functionality
python test_extractor.py

# See demo examples
python demo_extractor.py
```

## 📁 Output Files

- Individual batches: `phase2_batch_XX_results_[timestamp].xlsx`
- Final combined: `phase2_all_batches_FINAL_results_[timestamp].xlsx`
- Single file: `fresh_all_urls_FINAL_extraction_[timestamp].xlsx`

## 🔄 Resume After Interruption

Just run the same command again - it will automatically resume from where it stopped.

## ⚙️ Quick Setup

```bash
pip install -r requirements.txt
python run_phase2_extraction.py
```

## 🐛 If Something Goes Wrong

1. Check log files (*.log)
2. Reduce workers: Edit `max_workers=2` in script
3. Increase delay: Edit `delay=2.0` in script
4. Start fresh: `rm -f *progress*.json checkpoint_*.xlsx`

## 📞 Phone Number Examples

Input → Output:
- ************ → 15551234567
- (************* → 15551234567
- ******-123-4567 → 15551234567

## ⏱️ Estimated Times

- Phase2 batches (8,815 URLs): ~4.9 hours
- Single file (2,152 URLs): ~1.2 hours
- Success rate: 85-95%