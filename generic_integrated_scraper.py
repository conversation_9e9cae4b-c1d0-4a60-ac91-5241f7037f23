#!/usr/bin/env python3
"""
Generic Integrated Scraper: Combines URL extraction with dedicated page scraping for any state and city
Uses generic_url_scraper.py for Phase 1 (URL extraction) and scraperapi_scraper.py for Phase 2 (page scraping)
"""

import sys
import os
import json
import time
import argparse
from typing import List, Dict, Optional
from datetime import datetime
import logging

from generic_url_scraper import GenericURLScraper
from scraperapi_scraper import ScraperAPIScraper


class GenericIntegratedScraper:
    def __init__(self, mistral_api_key: str = None, scraperapi_key: str = None):
        """Initialize generic integrated scraper"""
        self.mistral_api_key = mistral_api_key or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"
        self.scraperapi_key = scraperapi_key
        self.urls_file = None  # Will be set based on state/city
        self.final_results_file = None  # Will be set based on state/city

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('generic_integrated_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_filenames(self, state_name: str, city_name: str = None):
        """Setup filenames based on state and city"""
        safe_state = state_name.lower().replace(' ', '_').replace('-', '_')

        if city_name:
            safe_city = city_name.lower().replace(' ', '_').replace('-', '_')
            self.urls_file = f"urls_{safe_state}_{safe_city}.json"
            self.final_results_file = f"integrated_{safe_state}_{safe_city}_results.xlsx"
        else:
            self.urls_file = f"urls_{safe_state}_all.json"
            self.final_results_file = f"integrated_{safe_state}_all_results.xlsx"

    def phase1_extract_urls(self, state_name: str, city_name: str = None,
                           max_cities: int = None, force_refresh: bool = False) -> bool:
        """Phase 1: Extract URLs from search pages"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 1: URL EXTRACTION")
        self.logger.info("=" * 60)

        # Setup filenames
        self.setup_filenames(state_name, city_name)

        url_scraper = GenericURLScraper(
            mistral_api_key=self.mistral_api_key,
            output_file=self.urls_file
        )

        success = url_scraper.extract_all_urls(
            state_name=state_name,
            city_name=city_name,
            max_cities=max_cities,
            force_refresh=force_refresh
        )

        if success:
            # Get and log statistics
            stats = url_scraper.get_url_stats()
            if stats:
                self.logger.info(f"Phase 1 completed: {stats['total_urls']} URLs extracted from {stats['total_combinations']} combinations")

                # Log breakdown by city and source
                for city, data in stats['by_city'].items():
                    sources_str = ', '.join(data['sources'])
                    self.logger.info(f"  {city}: {data['urls']} URLs (sources: {sources_str})")
            else:
                self.logger.warning("Phase 1 completed but couldn't get statistics")

        return success

    def phase2_scrape_pages(self, max_urls: Optional[int] = None, batch_size: int = 50, max_workers: int = 5) -> bool:
        """Phase 2: Scrape dedicated pages using extracted URLs"""
        self.logger.info("=" * 60)
        self.logger.info("PHASE 2: DEDICATED PAGE SCRAPING")
        self.logger.info("=" * 60)

        # Check if URLs file exists
        if not self.urls_file or not os.path.exists(self.urls_file):
            self.logger.error(f"URLs file {self.urls_file} not found. Run Phase 1 first.")
            return False

        # Load URLs
        try:
            with open(self.urls_file, 'r') as f:
                urls_data = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load URLs file: {e}")
            return False

        # Collect all URLs with metadata
        all_urls = []
        for combo_key, combo_data in urls_data.items():
            combo_urls = combo_data.get('urls', [])
            # Add metadata to each URL
            for url in combo_urls:
                all_urls.append({
                    'url': url,
                    'state': combo_data.get('state', 'Unknown'),
                    'city': combo_data.get('city', 'Unknown'),
                    'source': combo_data.get('source', 'Unknown'),
                    'combo_key': combo_key
                })

        if max_urls:
            all_urls = all_urls[:max_urls]
            self.logger.info(f"Limited to first {max_urls} URLs for testing")

        self.logger.info(f"Total URLs to scrape: {len(all_urls)}")

        if not all_urls:
            self.logger.warning("No URLs found to scrape")
            return False

        # Create ScraperAPI scraper
        if not self.scraperapi_key:
            self.logger.error("ScraperAPI key is required for Phase 2")
            return False

        scraper = ScraperAPIScraper(api_keys=[self.scraperapi_key])

        # Extract just the URLs for the scraper
        urls_to_scrape = [item['url'] for item in all_urls]

        # Process URLs
        try:
            self.logger.info(f"Starting dedicated page scraping with {max_workers} workers, batch size {batch_size}...")
            results = scraper.process_urls_parallel(
                urls=urls_to_scrape,
                max_workers=max_workers,
                batch_size=batch_size
            )

            # Add state, city, and source metadata to results
            enhanced_results = []
            for i, result in enumerate(results):
                if i < len(all_urls):
                    result['state'] = all_urls[i]['state']
                    result['city'] = all_urls[i]['city']
                    result['source'] = all_urls[i]['source']
                    result['combo_key'] = all_urls[i]['combo_key']
                enhanced_results.append(result)

            # Save results
            success = scraper.save_results(
                results=enhanced_results,
                filename=self.final_results_file
            )

            if success:
                self.logger.info(f"Phase 2 completed: Results saved to {self.final_results_file}")

                # Log summary statistics
                successful = len([r for r in enhanced_results if r.get('name')])
                failed = len(enhanced_results) - successful
                self.logger.info(f"Successfully scraped: {successful} profiles")
                self.logger.info(f"Failed to scrape: {failed} profiles")

                # Log breakdown by source
                sources_summary = {}
                for result in enhanced_results:
                    source = result.get('source', 'Unknown')
                    if source not in sources_summary:
                        sources_summary[source] = {'total': 0, 'successful': 0}
                    sources_summary[source]['total'] += 1
                    if result.get('name'):
                        sources_summary[source]['successful'] += 1

                for source, stats in sources_summary.items():
                    self.logger.info(f"  {source}: {stats['successful']}/{stats['total']} successful")

                return True
            else:
                self.logger.error("Failed to save Phase 2 results")
                return False

        except Exception as e:
            self.logger.error(f"Phase 2 failed: {e}")
            return False

    def get_summary_stats(self) -> Dict:
        """Get summary statistics for the integrated scraping process"""
        stats = {
            'phase1': {'completed': False, 'total_urls': 0, 'total_combinations': 0},
            'phase2': {'completed': False, 'profiles_scraped': 0}
        }

        # Phase 1 stats
        if self.urls_file and os.path.exists(self.urls_file):
            try:
                url_scraper = GenericURLScraper(output_file=self.urls_file)
                url_stats = url_scraper.get_url_stats()
                if url_stats:
                    stats['phase1']['completed'] = True
                    stats['phase1']['total_urls'] = url_stats['total_urls']
                    stats['phase1']['total_combinations'] = url_stats['total_combinations']
                    stats['phase1']['by_state'] = url_stats['by_state']
                    stats['phase1']['by_city'] = url_stats['by_city']
                    stats['phase1']['by_source'] = url_stats['by_source']
            except Exception as e:
                self.logger.warning(f"Failed to get Phase 1 stats: {e}")

        # Phase 2 stats
        if self.final_results_file and os.path.exists(self.final_results_file):
            try:
                import pandas as pd
                df = pd.read_excel(self.final_results_file)
                stats['phase2']['completed'] = True
                stats['phase2']['profiles_scraped'] = len(df)

                # Count successful vs failed
                if 'name' in df.columns:
                    successful = df['name'].notna().sum()
                    stats['phase2']['successful_profiles'] = int(successful)
                    stats['phase2']['failed_profiles'] = len(df) - int(successful)

                # Breakdown by state, city, and source
                if 'state' in df.columns:
                    stats['phase2']['by_state'] = df.groupby('state').size().to_dict()
                if 'city' in df.columns:
                    stats['phase2']['by_city'] = df.groupby('city').size().to_dict()
                if 'source' in df.columns:
                    stats['phase2']['by_source'] = df.groupby('source').size().to_dict()

            except Exception as e:
                self.logger.warning(f"Failed to get Phase 2 stats: {e}")

        return stats

    def list_available_states(self) -> List[str]:
        """List all available states"""
        try:
            temp_scraper = GenericURLScraper()
            return temp_scraper.list_available_states()
        except Exception as e:
            self.logger.error(f"Failed to get available states: {e}")
            return []

    def list_cities_in_state(self, state_name: str) -> List[str]:
        """List all available cities in a specific state"""
        try:
            temp_scraper = GenericURLScraper()
            return temp_scraper.list_cities_in_state(state_name)
        except Exception as e:
            self.logger.error(f"Failed to get cities for {state_name}: {e}")
            return []


def main():
    """Main entry point for generic integrated scraper"""
    parser = argparse.ArgumentParser(description='Generic Integrated Scraper (URL Extraction + Page Scraping)')
    parser.add_argument('state', nargs='?', help='State name to scrape (e.g., "Alabama", "New York")')
    parser.add_argument('--city', help='Specific city to scrape (optional - if not provided, scrapes all cities in state)')
    parser.add_argument('--phase', choices=['1', '2', 'both'], default='both',
                       help='Which phase to run: 1=extract URLs, 2=scrape pages, both=run both phases')
    parser.add_argument('--scraperapi-key', required=False,
                       help='ScraperAPI key (required for Phase 2)')
    parser.add_argument('--mistral-key',
                       help='Mistral AI API key (optional - defaults to built-in key)')
    parser.add_argument('--force-refresh', action='store_true',
                       help='Force refresh Phase 1 even if URLs file exists')
    parser.add_argument('--max-cities', type=int,
                       help='Maximum number of city-source combinations to process in Phase 1 (for testing)')
    parser.add_argument('--max-urls', type=int,
                       help='Maximum number of URLs to scrape in Phase 2 (for testing)')
    parser.add_argument('--batch-size', type=int, default=50,
                       help='Batch size for Phase 2 scraping (default: 50)')
    parser.add_argument('--max-workers', type=int, default=5,
                       help='Number of parallel workers for Phase 2 (default: 5)')
    parser.add_argument('--delay', type=float, default=0.5,
                       help='Delay between requests in Phase 1 (default: 0.5s)')
    parser.add_argument('--list-states', action='store_true',
                       help='List all available states')
    parser.add_argument('--list-cities', help='List all available cities in specified state')
    parser.add_argument('--stats', action='store_true',
                       help='Show summary statistics')
    parser.add_argument('--clean', action='store_true',
                       help='Clean up existing files and start fresh')

    args = parser.parse_args()

    # Get API keys
    mistral_key = args.mistral_key or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"
    scraperapi_key = args.scraperapi_key

    # Create scraper
    scraper = GenericIntegratedScraper(
        mistral_api_key=mistral_key,
        scraperapi_key=scraperapi_key
    )

    # List states if requested
    if args.list_states:
        print("Available states:")
        for state in scraper.list_available_states():
            print(f"  - {state}")
        return 0

    # List cities in state if requested
    if args.list_cities:
        print(f"Available cities in {args.list_cities}:")
        cities = scraper.list_cities_in_state(args.list_cities)
        if cities:
            for city in cities:
                print(f"  - {city}")
        else:
            print(f"No cities found for state: {args.list_cities}")
            print("\nAvailable states:")
            for state in scraper.list_available_states():
                print(f"  - {state}")
        return 0

    # Check if state provided (unless just showing stats)
    if not args.state and not args.stats:
        print("Error: Please provide a state name or use --list-states to see available states")
        print("\nUsage examples:")
        print("  python generic_integrated_scraper.py Alabama --scraperapi-key YOUR_KEY")
        print("  python generic_integrated_scraper.py 'New York' --city 'New York City' --scraperapi-key YOUR_KEY")
        print("  python generic_integrated_scraper.py --list-states")
        print("  python generic_integrated_scraper.py --list-cities Alabama")
        print("  python generic_integrated_scraper.py Alabama --phase 1  # Only extract URLs")
        print("  python generic_integrated_scraper.py Alabama --max-cities 5 --max-urls 100 --scraperapi-key YOUR_KEY  # Test run")
        return 1

    # Setup filenames if state provided
    if args.state:
        scraper.setup_filenames(args.state, args.city)

    # Clean files if requested
    if args.clean and args.state:
        files_to_clean = [
            scraper.urls_file,
            scraper.final_results_file,
            "generic_integrated_scraper.log"
        ]

        for file in files_to_clean:
            try:
                if file and os.path.exists(file):
                    os.remove(file)
                    print(f"Cleaned up: {file}")
            except Exception as e:
                print(f"Failed to clean {file}: {e}")

        print("Cleanup completed. Starting fresh.")

    # Validate ScraperAPI key for Phase 2
    if args.phase in ['2', 'both'] and not scraperapi_key:
        print("Error: ScraperAPI key is required for Phase 2")
        print("Please provide --scraperapi-key argument")
        return 1

    print("Generic Integrated Scraper")
    print("=" * 50)
    if args.state:
        print(f"Target state: {args.state}")
        if args.city:
            print(f"Target city: {args.city}")
        else:
            print("Target: All cities in state")
    print("Phase 1: URL extraction from search pages")
    print("Phase 2: Dedicated page scraping with ScraperAPI")
    print()

    # Show stats if requested
    if args.stats:
        if not args.state:
            print("Error: Please provide a state name to show statistics")
            return 1

        print("Getting summary statistics...")
        stats = scraper.get_summary_stats()

        print("\nSummary Statistics:")
        print("-" * 30)
        print(f"Phase 1 - URL Extraction:")
        if stats['phase1']['completed']:
            print(f"  ✓ Completed: {stats['phase1']['total_urls']} URLs from {stats['phase1']['total_combinations']} combinations")

            if 'by_state' in stats['phase1']:
                for state, data in stats['phase1']['by_state'].items():
                    print(f"    {state}: {data['urls']} URLs from {data['cities']} cities")

            if 'by_source' in stats['phase1']:
                for source, data in stats['phase1']['by_source'].items():
                    print(f"    {source}: {data['urls']} URLs from {data['combinations']} combinations")
        else:
            print(f"  ✗ Not completed")

        print(f"\nPhase 2 - Page Scraping:")
        if stats['phase2']['completed']:
            print(f"  ✓ Completed: {stats['phase2']['profiles_scraped']} profiles scraped")
            if 'successful_profiles' in stats['phase2']:
                print(f"    Successful: {stats['phase2']['successful_profiles']}")
                print(f"    Failed: {stats['phase2']['failed_profiles']}")

            if 'by_source' in stats['phase2']:
                for source, count in stats['phase2']['by_source'].items():
                    print(f"    {source}: {count} profiles")
        else:
            print(f"  ✗ Not completed")
        print()

        if not (args.phase in ['1', '2', 'both']):
            return 0

    start_time = time.time()
    success = True

    # Run Phase 1
    if args.phase in ['1', 'both']:
        print("Starting Phase 1: URL Extraction...")
        success = scraper.phase1_extract_urls(
            state_name=args.state,
            city_name=args.city,
            max_cities=args.max_cities,
            force_refresh=args.force_refresh
        )
        if not success:
            print("Phase 1 failed!")
            return 1
        print("Phase 1 completed successfully!")
        print()

    # Run Phase 2
    if args.phase in ['2', 'both']:
        print("Starting Phase 2: Dedicated Page Scraping...")
        success = scraper.phase2_scrape_pages(
            max_urls=args.max_urls,
            batch_size=args.batch_size,
            max_workers=args.max_workers
        )
        if not success:
            print("Phase 2 failed!")
            return 1
        print("Phase 2 completed successfully!")

    end_time = time.time()
    processing_time = end_time - start_time

    print()
    print("=" * 50)
    print("GENERIC INTEGRATED SCRAPING COMPLETED!")
    print(f"Total processing time: {processing_time/60:.1f} minutes")

    # Final stats
    final_stats = scraper.get_summary_stats()
    if final_stats['phase1']['completed']:
        print(f"URLs extracted: {final_stats['phase1']['total_urls']}")
    if final_stats['phase2']['completed']:
        print(f"Profiles scraped: {final_stats['phase2']['profiles_scraped']}")
        print(f"Results file: {scraper.final_results_file}")

    print("=" * 50)

    return 0


if __name__ == "__main__":
    sys.exit(main())
