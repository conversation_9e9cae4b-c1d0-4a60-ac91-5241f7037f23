#!/usr/bin/env python3
"""
Optimized Markdown NYC Scraper
Extracts clean markdown immediately and processes larger batches for maximum efficiency
"""

import sys
import os
import json
import time
import threading
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import pandas as pd
import re
from pathlib import Path
from bs4 import BeautifulSoup
import html2text

from nyc_boroughs_scraper import NYCBoroughsScraper

class OptimizedMarkdownNYCScraper:
    def __init__(self, mistral_api_keys: List[str] = None, max_workers: int = 5):
        """Initialize optimized markdown NYC scraper"""
        # Default to your 5 API keys if none provided
        default_keys = [
            "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G",
            "OHUPD3rpUQBbbd9FHpwnQpdQXIckRXqv", 
            "zeUtrAhXZm7RXe2Knt0xWGb19j3vb3f4",
            "Z9G5EWlDgYq8RtkV8xPfs7hZuAYghzg0",
            "e7QxoqwJNSPjcXFVmnEVgpAInrkWlRLS"
        ]
        
        self.mistral_api_keys = mistral_api_keys or default_keys
        self.max_workers = max_workers
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results_lock = threading.Lock()
        self.all_extracted_data = []
        self.save_interval = 1000  # Save every 1000 URLs
        self.last_save_count = 0
        
        # Optimized batch configuration
        self.batch_size = 25  # Larger batches due to markdown efficiency
        self.max_tokens_per_batch = 20000  # Conservative token limit
        
        # Retry configuration for Mistral API
        self.max_retries = 3
        self.base_retry_delay = 2.0
        self.rate_limit_delay = 30.0
        
        # Global rate limiting coordination
        self.global_rate_limit_lock = threading.Lock()
        self.last_rate_limit_time = {}
        self.api_key_delays = {}
        
        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] - %(message)s',
            handlers=[
                logging.FileHandler('optimized_markdown_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"Optimized Markdown Scraper initialized with {len(self.mistral_api_keys)} API keys and {max_workers} workers")
        self.logger.info(f"Optimized batch size: {self.batch_size} pages per API call (vs 10 in previous version)")
        self.logger.info(f"Auto-save enabled: Excel file saved every {self.save_interval} scraped URLs")
        self.logger.info(f"Intelligent retry: {self.max_retries} retries with exponential backoff")
        self.logger.info(f"Rate limiting: Smart coordination between workers with adaptive delays")
        
        # Initialize markdown converter
        self.markdown_converter = html2text.HTML2Text()
        self.markdown_converter.ignore_links = False
        self.markdown_converter.ignore_images = True
        self.markdown_converter.ignore_emphasis = False
        self.markdown_converter.body_width = 0  # No line wrapping
    
    def extract_clean_markdown(self, html_content: str, url: str) -> str:
        """Extract clean, relevant data in markdown format - FIXED VERSION"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # Target specific profile content areas based on the HTML structure
            profile_data = {}

            # Extract title and age
            title_elem = soup.find('div', class_='viewposttitle')
            if title_elem:
                # Get title text
                title_text = title_elem.get_text().strip()
                profile_data['title'] = title_text

                # Extract age from title
                age_elem = title_elem.find('span', class_='postTitleAge')
                if age_elem:
                    profile_data['age'] = age_elem.get_text().strip()

            # Extract name
            name_elem = soup.find('div', class_='viewpostname')
            if name_elem:
                name_text = name_elem.get_text().strip()
                # Remove "Nym:" prefix if present
                name_text = re.sub(r'^Nym:\s*', '', name_text)
                profile_data['name'] = name_text

            # Extract gender
            gender_elem = soup.find('div', class_='i-am')
            if gender_elem:
                gender_value = gender_elem.find('span', class_='iamisee__value')
                if gender_value:
                    profile_data['gender'] = gender_value.get_text().strip()

            # Extract description/body
            body_elem = soup.find('div', class_='viewpostbody')
            if body_elem:
                profile_data['description'] = body_elem.get_text().strip()

            # Extract phone number
            phone_elem = soup.find('a', href=re.compile(r'^tel:'))
            if phone_elem:
                phone_href = phone_elem.get('href', '')
                phone_number = phone_href.replace('tel:', '')
                profile_data['phone'] = phone_number

            # Extract any social media links or additional info
            # Look for common social media patterns in the text
            all_text = soup.get_text()
            social_media_patterns = [
                r'instagram[:\s]*[@]?[\w\.]+',
                r'snapchat[:\s]*[@]?[\w\.]+',
                r'twitter[:\s]*[@]?[\w\.]+',
                r'onlyfans[:\s]*[@]?[\w\.]+',
                r'@[\w\.]+',
                r'snap[:\s]*[@]?[\w\.]+',
                r'ig[:\s]*[@]?[\w\.]+',
            ]

            social_media_found = []
            for pattern in social_media_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                social_media_found.extend(matches)

            if social_media_found:
                profile_data['social_media'] = ', '.join(set(social_media_found))

            # Extract post ID from URL
            post_id_match = re.search(r'/(\d+)$', url)
            if post_id_match:
                profile_data['post_id'] = post_id_match.group(1)

            # Create structured markdown with extracted data
            structured_markdown = f"""# Profile: {profile_data.get('title', 'Unknown')}
**URL:** {url}
**Post ID:** {profile_data.get('post_id', 'Unknown')}

## Profile Information:
**Name:** {profile_data.get('name', 'Not specified')}
**Age:** {profile_data.get('age', 'Not specified')}
**Gender:** {profile_data.get('gender', 'Not specified')}
**Phone:** {profile_data.get('phone', 'Not specified')}

## Description:
{profile_data.get('description', 'No description available')}

## Social Media:
{profile_data.get('social_media', 'None found')}

---
"""

            return structured_markdown

        except Exception as e:
            self.logger.warning(f"Failed to extract profile data from {url}: {e}")
            return f"""# Profile Page
**URL:** {url}

## Content:
[Failed to extract profile content]

---
"""
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)"""
        # Rough estimate: 1 token ≈ 4 characters for English text
        return len(text) // 4
    
    def create_optimized_batches(self, markdown_data: List[Tuple[str, str]]) -> List[List[Tuple[str, str]]]:
        """Create optimized batches based on token count rather than fixed size"""
        batches = []
        current_batch = []
        current_tokens = 0
        
        for url, markdown in markdown_data:
            estimated_tokens = self.estimate_tokens(markdown)
            
            # If adding this item would exceed token limit, start new batch
            if current_tokens + estimated_tokens > self.max_tokens_per_batch and current_batch:
                batches.append(current_batch)
                current_batch = [(url, markdown)]
                current_tokens = estimated_tokens
            else:
                current_batch.append((url, markdown))
                current_tokens += estimated_tokens
            
            # Safety check: if batch gets too large by count, force new batch
            if len(current_batch) >= self.batch_size:
                batches.append(current_batch)
                current_batch = []
                current_tokens = 0
        
        # Add final batch if not empty
        if current_batch:
            batches.append(current_batch)
        
        return batches
    
    def call_mistral_with_retry(self, mistral_client, prompt: str, worker_id: int, api_key: str, max_tokens: int = 4000) -> Optional[str]:
        """Call Mistral API with intelligent retry logic and rate limiting handling"""
        # Apply intelligent rate limiting before making the call
        self.handle_rate_limiting(api_key, worker_id)
        
        for attempt in range(self.max_retries + 1):
            try:
                response = mistral_client.chat.complete(
                    model="mistral-large-latest",
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1,
                    max_tokens=max_tokens
                )
                
                # Success - record success and return the response
                result_text = response.choices[0].message.content.strip()
                self.record_success(api_key, worker_id)
                if attempt > 0:
                    self.logger.info(f"Worker {worker_id} Mistral API succeeded on attempt {attempt + 1}")
                return result_text
                
            except Exception as e:
                error_str = str(e).lower()
                
                # Check if it's a rate limiting error (429)
                if "429" in error_str or "too many requests" in error_str or "rate limit" in error_str:
                    # Record the rate limit for intelligent coordination
                    self.record_rate_limit(api_key, worker_id)
                    
                    if attempt < self.max_retries:
                        delay = self.rate_limit_delay * (1.5 ** attempt)
                        self.logger.warning(f"Worker {worker_id} hit rate limit (attempt {attempt + 1}/{self.max_retries + 1}). Waiting {delay:.1f}s...")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"Worker {worker_id} rate limit exceeded after {self.max_retries + 1} attempts")
                        return None
                
                # Check if it's a server error (5xx) or connection error
                elif any(code in error_str for code in ["500", "502", "503", "504", "connection", "timeout"]):
                    if attempt < self.max_retries:
                        delay = self.base_retry_delay * (2 ** attempt)
                        self.logger.warning(f"Worker {worker_id} server error (attempt {attempt + 1}/{self.max_retries + 1}). Waiting {delay:.1f}s... Error: {e}")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"Worker {worker_id} server error after {self.max_retries + 1} attempts: {e}")
                        return None
                
                # Check if it's a token limit error
                elif "token" in error_str and ("limit" in error_str or "exceeded" in error_str):
                    self.logger.error(f"Worker {worker_id} token limit exceeded. Batch too large: {e}")
                    return None
                
                # Other errors - retry with shorter delay
                else:
                    if attempt < self.max_retries:
                        delay = self.base_retry_delay * (1.2 ** attempt)
                        self.logger.warning(f"Worker {worker_id} API error (attempt {attempt + 1}/{self.max_retries + 1}). Waiting {delay:.1f}s... Error: {e}")
                        time.sleep(delay)
                        continue
                    else:
                        self.logger.error(f"Worker {worker_id} API error after {self.max_retries + 1} attempts: {e}")
                        return None
        
        return None

    def handle_rate_limiting(self, api_key: str, worker_id: int):
        """Intelligent rate limiting coordination between workers"""
        with self.global_rate_limit_lock:
            current_time = time.time()
            key_preview = api_key[:8] + "..." + api_key[-4:]

            # Check if this API key recently hit rate limits
            if api_key in self.last_rate_limit_time:
                time_since_limit = current_time - self.last_rate_limit_time[api_key]

                # If recent rate limit, apply progressive delay
                if time_since_limit < 60:  # Within last minute
                    delay = max(1.0, 10.0 - time_since_limit)
                    self.logger.info(f"Worker {worker_id} applying rate limit delay {delay:.1f}s for key {key_preview}")
                    time.sleep(delay)

            # Update API key usage tracking
            if api_key not in self.api_key_delays:
                self.api_key_delays[api_key] = 0.3  # Faster base delay due to markdown efficiency

            # Apply current delay for this API key
            current_delay = self.api_key_delays[api_key]
            if current_delay > 0.1:
                time.sleep(current_delay)

    def record_rate_limit(self, api_key: str, worker_id: int):
        """Record when an API key hits rate limits"""
        with self.global_rate_limit_lock:
            self.last_rate_limit_time[api_key] = time.time()

            # Increase delay for this API key
            if api_key in self.api_key_delays:
                self.api_key_delays[api_key] = min(3.0, self.api_key_delays[api_key] * 1.3)
            else:
                self.api_key_delays[api_key] = 1.5

            key_preview = api_key[:8] + "..." + api_key[-4:]
            self.logger.warning(f"Worker {worker_id} recorded rate limit for key {key_preview}, new delay: {self.api_key_delays[api_key]:.1f}s")

    def record_success(self, api_key: str, worker_id: int):
        """Record successful API call to gradually reduce delays"""
        with self.global_rate_limit_lock:
            if api_key in self.api_key_delays and self.api_key_delays[api_key] > 0.3:
                self.api_key_delays[api_key] = max(0.3, self.api_key_delays[api_key] * 0.95)

    def save_intermediate_results(self, force_save: bool = False):
        """Save intermediate results every 1000 URLs or when forced"""
        with self.results_lock:
            current_count = len(self.all_extracted_data)

            # Check if we should save (every 1000 URLs or forced)
            if force_save or (current_count >= self.last_save_count + self.save_interval):
                if current_count > 0:
                    try:
                        # Create DataFrame
                        df = pd.DataFrame(self.all_extracted_data)

                        # Generate filename with timestamp and count
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"optimized_markdown_{current_count}_urls_{timestamp}.xlsx"

                        # Reorder columns for better readability
                        column_order = [
                            'state', 'city', 'source', 'title', 'name', 'age', 'phone',
                            'description', 'social_media', 'email', 'website', 'posted_date',
                            'post_id', 'url', 'search_url', 'scraped_at', 'worker_id'
                        ]

                        existing_columns = [col for col in column_order if col in df.columns]
                        remaining_columns = [col for col in df.columns if col not in existing_columns]
                        final_columns = existing_columns + remaining_columns
                        df = df[final_columns]

                        # Sort data
                        df = df.sort_values(['state', 'city', 'source', 'scraped_at'])

                        # Save to Excel
                        df.to_excel(filename, index=False)

                        self.logger.info(f"📊 OPTIMIZED SAVE: {current_count} URLs saved to {filename}")

                        # Update last save count
                        self.last_save_count = current_count

                        # Summary by source
                        aaok_count = len([r for r in self.all_extracted_data if r.get('source') == 'aaok'])
                        aypapi_count = len([r for r in self.all_extracted_data if r.get('source') == 'aypapi'])
                        self.logger.info(f"   Current totals: aaok={aaok_count}, aypapi={aypapi_count}")

                    except Exception as e:
                        self.logger.error(f"Failed to save intermediate results: {e}")

                return True
            return False

    def process_combination_optimized(self, combo_key: str, combo_data: Dict, worker_id: int) -> List[Dict]:
        """Process a single combination with optimized markdown extraction and larger batches"""
        borough = combo_data['borough']
        source = combo_data['source']
        urls = combo_data['urls']
        base_url = combo_data['base_url']

        # Assign API key to worker (round-robin)
        worker_api_key = self.mistral_api_keys[worker_id % len(self.mistral_api_keys)]
        key_preview = worker_api_key[:8] + "..." + worker_api_key[-4:]

        self.logger.info(f"Worker {worker_id} processing {borough} ({source}): {len(urls)} URLs (API key: {key_preview})")

        if not urls:
            self.logger.info(f"Worker {worker_id} no URLs to process for {borough} ({source})")
            return []

        try:
            # Create scraper for this worker
            scraper = NYCBoroughsScraper(worker_api_key)

            # Step 1: Download HTML and immediately convert to markdown
            self.logger.info(f"Worker {worker_id} downloading and converting {len(urls)} pages to markdown...")
            markdown_data = []

            for i, url in enumerate(urls):
                if i > 0 and i % 200 == 0:
                    self.logger.info(f"Worker {worker_id} processed {i}/{len(urls)} pages to markdown")

                html = scraper.execute_curl_request(url, scraper.dedicated_curl_template)
                if html:
                    # Immediately convert to clean markdown
                    markdown = self.extract_clean_markdown(html, url)
                    markdown_data.append((url, markdown))
                else:
                    self.logger.warning(f"Worker {worker_id} failed to get HTML for {url}")

                time.sleep(0.05)  # Small delay

            self.logger.info(f"Worker {worker_id} successfully converted {len(markdown_data)} pages to markdown")

            # Step 2: Create optimized batches based on token count
            batches = self.create_optimized_batches(markdown_data)
            total_batches = len(batches)

            self.logger.info(f"Worker {worker_id} created {total_batches} optimized batches (avg {len(markdown_data)/total_batches:.1f} pages per batch)")

            # Step 3: Process batches with Mistral
            all_extracted_data = []

            for batch_num, batch in enumerate(batches):
                self.logger.info(f"Worker {worker_id} processing batch {batch_num + 1}/{total_batches} ({len(batch)} pages) for {borough}")

                # Process batch with Mistral
                batch_results = self.process_markdown_batch_with_mistral(batch, scraper, borough, source, base_url, worker_id)
                all_extracted_data.extend(batch_results)

                # Rate limiting between batches (reduced due to markdown efficiency)
                time.sleep(0.5)

            self.logger.info(f"Worker {worker_id} completed {borough} ({source}): {len(all_extracted_data)} records from {total_batches} optimized batches")

            # Save checkpoint
            checkpoint_file = f"optimized_markdown_{borough}_{source}_checkpoint.xlsx"
            if all_extracted_data:
                try:
                    df = pd.DataFrame(all_extracted_data)
                    df.to_excel(checkpoint_file, index=False)
                    self.logger.info(f"Worker {worker_id} saved checkpoint: {checkpoint_file}")
                except Exception as e:
                    self.logger.warning(f"Worker {worker_id} failed to save checkpoint: {e}")

            return all_extracted_data

        except Exception as e:
            self.logger.error(f"Worker {worker_id} failed processing {borough} ({source}): {e}")
            return []

    def process_markdown_batch_with_mistral(self, markdown_batch: List[Tuple[str, str]], scraper, borough: str, source: str, base_url: str, worker_id: int) -> List[Dict]:
        """Process a batch of markdown content with Mistral AI"""
        if not markdown_batch:
            return []

        try:
            # Create batch prompt with clean markdown content
            prompt = f"""
            You are extracting data from {len(markdown_batch)} escort profile pages in clean markdown format. For each page, extract the following information:

            1. title: Profile title or headline
            2. name: Person's name
            3. age: Age (must be ≤30, skip if >30)
            4. phone: Phone number
            5. description: Profile description
            6. social_media: Social media handles/links (Instagram, Twitter, Snapchat, OnlyFans, etc.)
            7. email: Email address
            8. website: Website links
            9. posted_date: When the post was created
            10. post_id: Unique post identifier

            IMPORTANT: Only include profiles where age ≤30 AND gender is woman. Skip any profiles with age >30 or not women.

            Pages to process (in clean markdown format):
            """

            for i, (url, markdown_content) in enumerate(markdown_batch):
                prompt += f"\n--- PAGE {i} ---\n"
                prompt += markdown_content
                prompt += "\n"

            prompt += """

            Return a JSON array with one object per valid page (only for women with age ≤30). Each object should have:
            {
                "page_index": <index>,
                "url": "<url>",
                "title": "<title>",
                "name": "<name>",
                "age": "<age>",
                "phone": "<phone>",
                "description": "<description>",
                "social_media": "<social_media>",
                "email": "<email>",
                "website": "<website>",
                "posted_date": "<posted_date>",
                "post_id": "<post_id>"
            }

            If any field is not found, use null. Only return valid JSON array.
            """

            # Make Mistral API call with retry logic
            result_text = self.call_mistral_with_retry(
                mistral_client=scraper.mistral_client,
                prompt=prompt,
                worker_id=worker_id,
                api_key=self.mistral_api_keys[worker_id % len(self.mistral_api_keys)],
                max_tokens=4000
            )

            if result_text is None:
                self.logger.error(f"Worker {worker_id} failed to get Mistral response after all retries")
                return []

            # Parse JSON response
            try:
                batch_results = json.loads(result_text)
                if not isinstance(batch_results, list):
                    batch_results = [batch_results]

                # Add metadata to each result
                for result in batch_results:
                    if result and isinstance(result, dict):
                        result['city'] = borough
                        result['state'] = 'New York'
                        result['source'] = source
                        result['search_url'] = base_url
                        result['scraped_at'] = datetime.now().isoformat()
                        result['worker_id'] = worker_id

                self.logger.info(f"Worker {worker_id} optimized batch processed {len(markdown_batch)} pages, extracted {len(batch_results)} valid profiles")
                return batch_results

            except json.JSONDecodeError:
                # Try to extract JSON array from the response
                json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
                if json_match:
                    batch_results = json.loads(json_match.group())

                    # Add metadata
                    for result in batch_results:
                        if result and isinstance(result, dict):
                            result['city'] = borough
                            result['state'] = 'New York'
                            result['source'] = source
                            result['search_url'] = base_url
                            result['scraped_at'] = datetime.now().isoformat()
                            result['worker_id'] = worker_id

                    self.logger.info(f"Worker {worker_id} optimized batch processed {len(markdown_batch)} pages, extracted {len(batch_results)} valid profiles (fallback parsing)")
                    return batch_results

                self.logger.warning(f"Worker {worker_id} failed to parse optimized batch Mistral response")
                return []

        except Exception as e:
            self.logger.error(f"Worker {worker_id} optimized batch Mistral extraction failed: {e}")
            return []

    def run_optimized_scraping(self) -> bool:
        """Run optimized markdown scraping with larger batches"""
        self.logger.info("=" * 60)
        self.logger.info("OPTIMIZED MARKDOWN SCRAPING - LARGER BATCHES")
        self.logger.info("=" * 60)

        # Load URLs data
        if not os.path.exists(self.urls_file):
            self.logger.error(f"URLs file {self.urls_file} not found. Run Phase 1 first.")
            return False

        try:
            with open(self.urls_file, 'r') as f:
                urls_data = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load URLs file: {e}")
            return False

        self.logger.info(f"Using {len(self.mistral_api_keys)} API keys with {self.max_workers} parallel workers")
        self.logger.info(f"Optimized batch processing: up to {self.batch_size} pages per Mistral API call")
        self.logger.info(f"Token-based batching: max {self.max_tokens_per_batch} tokens per batch")

        # Summary of what we'll process
        total_urls = sum(data['total_urls'] for data in urls_data.values())
        estimated_batches = total_urls // self.batch_size  # Conservative estimate

        self.logger.info(f"Total URLs to process: {total_urls}")
        self.logger.info(f"Estimated batches: ~{estimated_batches}")
        self.logger.info(f"Expected API call reduction: ~{((total_urls - estimated_batches) / total_urls * 100):.1f}%")

        # Process combinations in parallel
        successful_workers = 0
        failed_workers = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_combo = {
                executor.submit(self.process_combination_optimized, combo_key, combo_data, i): (combo_key, combo_data)
                for i, (combo_key, combo_data) in enumerate(urls_data.items())
            }

            # Process completed tasks
            for future in as_completed(future_to_combo):
                combo_key, combo_data = future_to_combo[future]
                try:
                    result = future.result()
                    if result is not None:
                        successful_workers += 1

                        # Thread-safe data collection with auto-save
                        with self.results_lock:
                            self.all_extracted_data.extend(result)

                        # Check if we should save intermediate results
                        self.save_intermediate_results()

                        self.logger.info(f"✓ Completed: {combo_data['borough']} from {combo_data['source']} ({len(result)} records)")
                    else:
                        failed_workers += 1
                        self.logger.error(f"✗ Failed: {combo_data['borough']} from {combo_data['source']}")
                except Exception as e:
                    failed_workers += 1
                    self.logger.error(f"✗ Exception in {combo_data['borough']} from {combo_data['source']}: {e}")

        # Force save any remaining data
        self.save_intermediate_results(force_save=True)

        # Save final results
        if self.all_extracted_data:
            final_file = "optimized_markdown_nyc_final.xlsx"
            try:
                df = pd.DataFrame(self.all_extracted_data)

                # Reorder columns
                column_order = [
                    'state', 'city', 'source', 'title', 'name', 'age', 'phone',
                    'description', 'social_media', 'email', 'website', 'posted_date',
                    'post_id', 'url', 'search_url', 'scraped_at', 'worker_id'
                ]

                existing_columns = [col for col in column_order if col in df.columns]
                remaining_columns = [col for col in df.columns if col not in existing_columns]
                final_columns = existing_columns + remaining_columns
                df = df[final_columns]

                # Sort data
                df = df.sort_values(['state', 'city', 'source', 'scraped_at'])

                df.to_excel(final_file, index=False)
                self.logger.info(f"Final optimized results saved to: {final_file}")

                # Summary
                self.logger.info("=" * 60)
                self.logger.info("OPTIMIZED MARKDOWN SCRAPING COMPLETED")
                self.logger.info("=" * 60)
                self.logger.info(f"Successful workers: {successful_workers}, Failed workers: {failed_workers}")
                self.logger.info(f"Total records extracted: {len(self.all_extracted_data)}")

                # Summary by source
                aaok_count = len([r for r in self.all_extracted_data if r.get('source') == 'aaok'])
                aypapi_count = len([r for r in self.all_extracted_data if r.get('source') == 'aypapi'])
                self.logger.info(f"  - aaok.com: {aaok_count} records")
                self.logger.info(f"  - aypapi.com: {aypapi_count} records")

                # Summary by borough
                boroughs = list(set(r.get('city') for r in self.all_extracted_data))
                self.logger.info(f"NYC Boroughs processed: {len(boroughs)}")
                for borough in sorted(boroughs):
                    borough_count = len([r for r in self.all_extracted_data if r.get('city') == borough])
                    self.logger.info(f"  - {borough}: {borough_count} records")

                # Efficiency metrics
                actual_batches = estimated_batches  # Rough estimate
                self.logger.info(f"Optimized Processing Efficiency:")
                self.logger.info(f"  - Estimated API calls made: ~{actual_batches}")
                self.logger.info(f"  - vs Individual calls: {len(self.all_extracted_data)}")
                self.logger.info(f"  - Efficiency gain: ~{((len(self.all_extracted_data) - actual_batches) / len(self.all_extracted_data) * 100):.1f}%")
                self.logger.info(f"  - Larger batch sizes: up to {self.batch_size} pages per call")
                self.logger.info(f"  - Markdown optimization: 80-90% token reduction")

            except Exception as e:
                self.logger.error(f"Failed to save final results: {e}")
                return False

        return failed_workers == 0

def main():
    """Main entry point for optimized markdown NYC scraper"""
    parser = argparse.ArgumentParser(description='Optimized Markdown NYC Boroughs Scraper')
    parser.add_argument('--workers', type=int, default=5,
                       help='Number of parallel workers (default: 5)')
    parser.add_argument('--batch-size', type=int, default=25,
                       help='Maximum pages per Mistral API batch (default: 25)')
    parser.add_argument('--mistral-keys',
                       help='Comma-separated Mistral AI API keys (optional - defaults to built-in keys)')

    args = parser.parse_args()

    # Validate parameters
    if args.workers < 1 or args.workers > 10:
        print("Error: Number of workers must be between 1 and 10")
        return 1

    if args.batch_size < 5 or args.batch_size > 50:
        print("Error: Batch size must be between 5 and 50")
        return 1

    # Parse Mistral API keys
    mistral_keys = None
    if args.mistral_keys:
        mistral_keys = [key.strip() for key in args.mistral_keys.split(',')]
        print(f"Using {len(mistral_keys)} provided API keys")
    else:
        print("Using built-in API keys")

    # Create optimized scraper
    scraper = OptimizedMarkdownNYCScraper(mistral_api_keys=mistral_keys, max_workers=args.workers)
    scraper.batch_size = args.batch_size  # Override batch size if specified

    print("Optimized Markdown NYC Boroughs Scraper")
    print("=" * 50)
    print(f"Configuration: {args.workers} workers, {len(scraper.mistral_api_keys)} API keys")
    print(f"Optimized batch size: up to {args.batch_size} pages per API call")
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Features: Age ≤30, Women only, Markdown optimization, Larger batches")
    print("Optimizations:")
    print("  - Immediate markdown conversion (80-90% token reduction)")
    print("  - Token-based intelligent batching")
    print("  - Larger batch sizes for better efficiency")
    print("  - Smart retry logic and rate limiting")
    print()

    start_time = time.time()

    print("Starting optimized markdown scraping...")
    success = scraper.run_optimized_scraping()

    end_time = time.time()
    processing_time = end_time - start_time

    if success:
        print(f"\n✓ Optimized markdown scraping completed successfully!")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Total records extracted: {len(scraper.all_extracted_data)}")
        print(f"Performance improvements:")
        print(f"  - Markdown optimization: 80-90% token reduction")
        print(f"  - Larger batches: up to {args.batch_size} pages per API call")
        print(f"  - Intelligent retry and rate limiting")
        print(f"  - Auto-save every 1,000 records")

        return 0
    else:
        print(f"\n✗ Optimized scraping completed with some failures")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Partial results collected: {len(scraper.all_extracted_data)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
