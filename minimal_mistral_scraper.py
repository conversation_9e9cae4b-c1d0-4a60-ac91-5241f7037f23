#!/usr/bin/env python3
"""
Minimal Mistral Scraper - Download all pages, minimal Mistral usage, maximum manual extraction
"""

import sys
import os
import json
import time
import threading
import argparse
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import pandas as pd
import re
from pathlib import Path
from bs4 import BeautifulSoup

from nyc_boroughs_scraper import NYCBoroughsScraper

class MinimalMistralNYCScraper:
    def __init__(self, mistral_api_keys: List[str] = None, max_workers: int = 5):
        """Initialize minimal Mistral NYC scraper"""
        # Default to your 5 API keys if none provided
        default_keys = [
            "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G",
            "OHUPD3rpUQBbbd9FHpwnQpdQXIckRXqv", 
            "zeUtrAhXZm7RXe2Knt0xWGb19j3vb3f4",
            "Z9G5EWlDgYq8RtkV8xPfs7hZuAYghzg0",
            "e7QxoqwJNSPjcXFVmnEVgpAInrkWlRLS"
        ]
        
        self.mistral_api_keys = mistral_api_keys or default_keys
        self.max_workers = max_workers
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results_lock = threading.Lock()
        self.all_extracted_data = []
        self.save_interval = 1000  # Save every 1000 URLs
        self.last_save_count = 0
        
        # Minimal Mistral configuration
        self.batch_size = 30  # Larger batches since we're doing minimal extraction
        
        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] - %(message)s',
            handlers=[
                logging.FileHandler('minimal_mistral_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"Minimal Mistral Scraper initialized with {len(self.mistral_api_keys)} API keys and {max_workers} workers")
        self.logger.info(f"Approach: Download all pages → Minimal Mistral (gender + basic) → Manual extraction")
        self.logger.info(f"Batch size: {self.batch_size} pages per API call")
    
    def extract_profile_markdown(self, html_content: str, url: str) -> str:
        """Extract clean markdown from profile page - ENHANCED VERSION"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Target specific profile content areas based on the HTML structure
            profile_sections = {}
            
            # Extract title and age
            title_elem = soup.find('div', class_='viewposttitle')
            if title_elem:
                profile_sections['title'] = title_elem.get_text().strip()
                
                # Extract age from title
                age_elem = title_elem.find('span', class_='postTitleAge')
                if age_elem:
                    profile_sections['age'] = age_elem.get_text().strip()
            
            # Extract name
            name_elem = soup.find('div', class_='viewpostname')
            if name_elem:
                name_text = name_elem.get_text().strip()
                name_text = re.sub(r'^Nym:\s*', '', name_text)
                profile_sections['name'] = name_text
            
            # Extract gender
            gender_elem = soup.find('div', class_='i-am')
            if gender_elem:
                gender_value = gender_elem.find('span', class_='iamisee__value')
                if gender_value:
                    profile_sections['gender'] = gender_value.get_text().strip()
            
            # Extract description/body
            body_elem = soup.find('div', class_='viewpostbody')
            if body_elem:
                profile_sections['description'] = body_elem.get_text().strip()
            
            # Extract phone number
            phone_elem = soup.find('a', href=re.compile(r'^tel:'))
            if phone_elem:
                phone_href = phone_elem.get('href', '')
                phone_number = phone_href.replace('tel:', '')
                profile_sections['phone'] = phone_number
            
            # Extract location/address information
            location_elems = soup.find_all(text=re.compile(r'(location|address|area|neighborhood)', re.I))
            location_info = []
            for elem in location_elems:
                if elem.strip():
                    location_info.append(elem.strip())
            if location_info:
                profile_sections['location_info'] = ' | '.join(location_info[:3])
            
            # Get all text for social media and manual extraction
            all_text = soup.get_text()
            
            # Extract post ID from URL
            post_id_match = re.search(r'/(\d+)$', url)
            if post_id_match:
                profile_sections['post_id'] = post_id_match.group(1)
            
            # Create comprehensive markdown with ALL extracted data
            markdown_content = f"""# Profile Page
**URL:** {url}
**Post ID:** {profile_sections.get('post_id', 'Unknown')}

## Structured Data:
**Title:** {profile_sections.get('title', 'Not found')}
**Name:** {profile_sections.get('name', 'Not found')}
**Age:** {profile_sections.get('age', 'Not found')}
**Gender:** {profile_sections.get('gender', 'Not found')}
**Phone:** {profile_sections.get('phone', 'Not found')}
**Location Info:** {profile_sections.get('location_info', 'Not found')}

## Description:
{profile_sections.get('description', 'No description found')}

## Full Page Text (for manual extraction):
{all_text}

---
"""
            
            return markdown_content
            
        except Exception as e:
            self.logger.warning(f"Failed to extract profile data from {url}: {e}")
            # Fallback to simple text extraction
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                text = soup.get_text()
                return f"""# Profile Page
**URL:** {url}

## Full Page Text:
{text}

---
"""
            except:
                return f"""# Profile Page
**URL:** {url}

## Content:
[Failed to extract content]

---
"""
    
    def call_minimal_mistral(self, mistral_client, markdown_batch: List[Tuple[str, str]], worker_id: int) -> List[Dict]:
        """Call Mistral for minimal extraction: gender check + basic data only"""
        
        if not markdown_batch:
            return []
        
        # Create minimal prompt focused only on gender and basic extraction
        prompt = f"""
        You are checking {len(markdown_batch)} profile pages. For each page, extract ONLY these 4 fields:

        1. is_female: true/false (is this profile for a woman/female?)
        2. name: Person's name
        3. social_media: Any social media handles/usernames found
        4. address: Any address or location information

        IMPORTANT: Only extract these 4 fields. Do not extract phone, age, or other details.

        Pages to check:
        """
        
        for i, (url, markdown_content) in enumerate(markdown_batch):
            prompt += f"\n--- PAGE {i} ---\n"
            prompt += f"URL: {url}\n"
            prompt += markdown_content[:1500]  # Limit content to avoid token overflow
            prompt += "\n"
        
        prompt += """
        
        Return a JSON array with one object per page:
        {
            "page_index": <index>,
            "url": "<url>",
            "is_female": <true/false>,
            "name": "<name or null>",
            "social_media": "<social_media or null>",
            "address": "<address or null>"
        }
        
        Only return valid JSON array. If unsure about gender, set is_female to false.
        """
        
        try:
            response = mistral_client.chat.complete(
                model="mistral-large-latest",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=2000
            )
            
            result_text = response.choices[0].message.content.strip()
            
            # Parse JSON response
            try:
                # Try direct parsing
                batch_results = json.loads(result_text)
                if not isinstance(batch_results, list):
                    batch_results = [batch_results]
                
                self.logger.info(f"Worker {worker_id} minimal Mistral processed {len(markdown_batch)} pages, got {len(batch_results)} results")
                return batch_results
                
            except json.JSONDecodeError:
                # Try fallback parsing
                json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
                if json_match:
                    try:
                        batch_results = json.loads(json_match.group())
                        self.logger.info(f"Worker {worker_id} minimal Mistral processed {len(markdown_batch)} pages, got {len(batch_results)} results (fallback)")
                        return batch_results
                    except json.JSONDecodeError:
                        self.logger.warning(f"Worker {worker_id} failed to parse minimal Mistral response")
                        return []
                else:
                    self.logger.warning(f"Worker {worker_id} no JSON found in minimal Mistral response")
                    return []
        
        except Exception as e:
            self.logger.error(f"Worker {worker_id} minimal Mistral API call failed: {e}")
            return []
    
    def manual_extract_from_markdown(self, markdown_content: str, url: str) -> Dict:
        """Manual extraction of additional data from markdown"""
        
        manual_data = {
            'phone_manual': None,
            'detailed_social_manual': None,
            'full_address_manual': None,
            'additional_info_manual': None
        }
        
        # Extract phone numbers using regex
        phone_patterns = [
            r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',  # ************ or 5551234567
            r'\b\(\d{3}\)\s*\d{3}[-.]?\d{4}\b',  # (*************
            r'\btel:\s*\d+\b',  # tel: 5551234567
            r'\bcall\s*:?\s*\d+\b',  # call: 5551234567
            r'\btext\s*:?\s*\d+\b'  # text: 5551234567
        ]
        
        phones_found = []
        for pattern in phone_patterns:
            matches = re.findall(pattern, markdown_content, re.IGNORECASE)
            phones_found.extend(matches)
        
        if phones_found:
            manual_data['phone_manual'] = ', '.join(set(phones_found))
        
        # Extract detailed social media
        social_patterns = [
            r'instagram[:\s]*[@]?[\w\.]+',
            r'snapchat[:\s]*[@]?[\w\.]+',
            r'twitter[:\s]*[@]?[\w\.]+',
            r'onlyfans[:\s]*[@]?[\w\.]+',
            r'@[\w\.]+',
            r'snap[:\s]*[@]?[\w\.]+',
            r'ig[:\s]*[@]?[\w\.]+',
            r'tiktok[:\s]*[@]?[\w\.]+',
            r'telegram[:\s]*[@]?[\w\.]+',
        ]
        
        social_found = []
        for pattern in social_patterns:
            matches = re.findall(pattern, markdown_content, re.IGNORECASE)
            social_found.extend(matches)
        
        if social_found:
            manual_data['detailed_social_manual'] = ', '.join(set(social_found))
        
        # Extract address/location information
        address_patterns = [
            r'\b\d+\s+[A-Za-z\s]+(?:street|st|avenue|ave|road|rd|boulevard|blvd|lane|ln)\b',
            r'\b(?:manhattan|brooklyn|bronx|queens|staten island)\b',
            r'\b\d{5}\b',  # ZIP codes
            r'\b[A-Za-z\s]+,\s*NY\b',
        ]
        
        addresses_found = []
        for pattern in address_patterns:
            matches = re.findall(pattern, markdown_content, re.IGNORECASE)
            addresses_found.extend(matches)
        
        if addresses_found:
            manual_data['full_address_manual'] = ', '.join(set(addresses_found))
        
        return manual_data
