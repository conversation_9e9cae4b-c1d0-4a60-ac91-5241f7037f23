#!/usr/bin/env python3
"""
URL Deduplication Filter
Pre-filters URLs to remove those with already scraped phone numbers
"""

import requests
import time
import random
import pandas as pd
import re
import json
import os
from typing import List, Set, Optional, Dict
from bs4 import BeautifulSoup
from datetime import datetime

class URLDeduplicationFilter:
    def __init__(self, scraperapi_key: str = None):
        self.scraperapi_key = scraperapi_key
        self.scraperapi_url = "https://api.scraperapi.com/"
        self.scraped_phone_numbers: Set[str] = set()
        self.load_existing_phone_numbers()

    def load_existing_phone_numbers(self):
        """Load phone numbers from existing result files"""
        print("🔍 Loading existing phone numbers from previous results...")

        # Look for existing Excel files with scraped results
        result_files = []
        for f in os.listdir('.'):
            if (f.startswith(('scraperapi_results_', 'enhanced_', 'manual_extraction_', 'final_'))
                and f.endswith('.xlsx')):
                result_files.append(f)

        total_loaded = 0
        for file_path in result_files:
            try:
                print(f"📂 Checking {file_path}...")
                df = pd.read_excel(file_path)

                if 'phone' in df.columns:
                    valid_phones = df[df['phone'].notna() & (df['phone'] != '') & (df['phone'] != 'nan')]['phone']

                    for phone in valid_phones:
                        cleaned_phone = self.clean_phone_number(str(phone))
                        if cleaned_phone:
                            self.scraped_phone_numbers.add(cleaned_phone)
                            total_loaded += 1

                    print(f"   📱 Added {len(valid_phones)} phone numbers")

            except Exception as e:
                print(f"   ⚠️ Error loading {file_path}: {e}")
                continue

        print(f"✅ Loaded {len(self.scraped_phone_numbers)} unique phone numbers from {len(result_files)} files")

    def clean_phone_number(self, phone_str: str) -> Optional[str]:
        """Clean phone number for consistent comparison"""
        if not phone_str or pd.isna(phone_str):
            return None

        # Remove all non-digits
        phone_clean = re.sub(r'[^\d]', '', str(phone_str))

        # Must be at least 10 digits
        if len(phone_clean) < 10:
            return None

        # Normalize to 10 digits (remove country code if present)
        if len(phone_clean) == 11 and phone_clean.startswith('1'):
            phone_clean = phone_clean[1:]
        elif len(phone_clean) > 11:
            return None

        return phone_clean

    def quick_phone_check_scraperapi(self, url: str) -> Optional[str]:
        """Quickly extract phone number using ScraperAPI"""
        if not self.scraperapi_key:
            print("⚠️ No ScraperAPI key provided")
            return None

        try:
            payload = {
                'api_key': self.scraperapi_key,
                'url': url,
                'render': 'false',
                'country_code': 'us',
                'session_number': random.randint(1, 1000)
            }

            response = requests.get(
                self.scraperapi_url,
                params=payload,
                timeout=30
            )

            if response.status_code == 200:
                html = response.text
                soup = BeautifulSoup(html, 'html.parser')

                # Extract phone from viewposttelephone
                phone_elem = soup.select_one('.viewposttelephone')
                if phone_elem:
                    phone_text = phone_elem.get_text(strip=True)
                    phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                    return phone_clean.strip() if phone_clean.strip() else None

            return None

        except Exception as e:
            print(f"⚠️ Quick phone check failed for {url}: {e}")
            return None

    def filter_urls(self, urls: List[str], output_file: str = None, use_scraperapi: bool = False) -> Dict:
        """Filter URLs by checking for duplicate phone numbers

        Args:
            urls: List of URLs to filter
            output_file: Output file for results
            use_scraperapi: Whether to use ScraperAPI for phone checking (costs money)

        Returns:
            Dict with filtered URLs and statistics
        """

        print(f"🔍 Starting URL deduplication filter")
        print(f"📋 Total URLs to check: {len(urls)}")
        print(f"📱 Existing phone numbers: {len(self.scraped_phone_numbers)}")
        print(f"💰 Using ScraperAPI: {use_scraperapi}")
        print("=" * 60)

        urls_to_process = []
        urls_skipped = []
        urls_failed = []

        for i, url in enumerate(urls):
            if i % 50 == 0:
                print(f"🔄 Progress: {i}/{len(urls)} URLs checked")

            try:
                phone = None

                if use_scraperapi and self.scraperapi_key:
                    phone = self.quick_phone_check_scraperapi(url)
                    # Add small delay to avoid rate limits
                    time.sleep(random.uniform(0.5, 1.0))

                if phone:
                    cleaned_phone = self.clean_phone_number(phone)
                    if cleaned_phone and cleaned_phone in self.scraped_phone_numbers:
                        urls_skipped.append({
                            'url': url,
                            'phone': phone,
                            'cleaned_phone': cleaned_phone,
                            'skip_reason': 'phone_already_scraped',
                            'checked_at': datetime.now().isoformat()
                        })
                        print(f"🚫 SKIP: {url} (phone: {cleaned_phone})")
                    else:
                        urls_to_process.append(url)
                        if cleaned_phone:
                            # Add to our set to avoid checking again
                            self.scraped_phone_numbers.add(cleaned_phone)
                        print(f"✅ NEW: {url} (phone: {phone})")
                else:
                    # Couldn't extract phone, include for processing
                    urls_to_process.append(url)
                    print(f"❓ UNKNOWN: {url} (no phone found)")

            except Exception as e:
                urls_failed.append({
                    'url': url,
                    'error': str(e),
                    'checked_at': datetime.now().isoformat()
                })
                # Include failed URLs for processing
                urls_to_process.append(url)
                print(f"❌ ERROR: {url} ({e})")

        # Results summary
        results = {
            'urls_to_process': urls_to_process,
            'urls_skipped': urls_skipped,
            'urls_failed': urls_failed,
            'stats': {
                'total_urls': len(urls),
                'urls_to_process': len(urls_to_process),
                'urls_skipped': len(urls_skipped),
                'urls_failed': len(urls_failed),
                'deduplication_rate': (len(urls_skipped) / len(urls)) * 100 if len(urls) > 0 else 0,
                'estimated_api_savings': len(urls_skipped) * 0.001,  # Assuming $0.001 per request
                'processed_at': datetime.now().isoformat()
            }
        }

        # Save results
        if output_file:
            self.save_filter_results(results, output_file)

        # Print summary
        print(f"\n🎉 URL FILTERING COMPLETE")
        print(f"📊 RESULTS SUMMARY:")
        print(f"   Total URLs: {results['stats']['total_urls']}")
        print(f"   URLs to process: {results['stats']['urls_to_process']}")
        print(f"   URLs skipped (duplicates): {results['stats']['urls_skipped']} ({results['stats']['deduplication_rate']:.1f}%)")
        print(f"   URLs failed check: {results['stats']['urls_failed']}")
        print(f"   💰 Estimated API savings: ${results['stats']['estimated_api_savings']:.3f}")

        return results

    def save_filter_results(self, results: Dict, output_file: str):
        """Save filtering results to Excel file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            excel_file = f"url_filter_results_{timestamp}.xlsx"
            json_file = f"filtered_urls_{timestamp}.json"

            # Save to Excel with multiple sheets
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                # URLs to process
                if results['urls_to_process']:
                    urls_df = pd.DataFrame({'url': results['urls_to_process']})
                    urls_df.to_excel(writer, sheet_name='URLs_To_Process', index=False)

                # Skipped URLs
                if results['urls_skipped']:
                    skipped_df = pd.DataFrame(results['urls_skipped'])
                    skipped_df.to_excel(writer, sheet_name='Skipped_Duplicates', index=False)

                # Failed URLs
                if results['urls_failed']:
                    failed_df = pd.DataFrame(results['urls_failed'])
                    failed_df.to_excel(writer, sheet_name='Failed_Checks', index=False)

                # Statistics
                stats_df = pd.DataFrame([results['stats']])
                stats_df.to_excel(writer, sheet_name='Statistics', index=False)

            # Save filtered URLs as JSON for easy loading
            filtered_data = {
                'urls_to_process': results['urls_to_process'],
                'stats': results['stats']
            }

            with open(json_file, 'w') as f:
                json.dump(filtered_data, f, indent=2)

            print(f"💾 Results saved:")
            print(f"   📊 Excel report: {excel_file}")
            print(f"   📋 Filtered URLs: {json_file}")

        except Exception as e:
            print(f"❌ Error saving results: {e}")

def main():
    """Main function for URL deduplication filtering"""

    # Configuration
    scraperapi_key = "your_scraperapi_key_here"  # Replace with your actual key
    urls_file = "parallel_nyc_all_urls_deduplicated.json"  # Your URLs file

    print("🔧 URL Deduplication Filter")
    print("=" * 50)

    # Load URLs
    if not os.path.exists(urls_file):
        print(f"❌ URLs file not found: {urls_file}")
        return

    with open(urls_file, 'r') as f:
        urls_data = json.load(f)

    urls = urls_data if isinstance(urls_data, list) else []
    print(f"📂 Loaded {len(urls)} URLs from {urls_file}")

    if not urls:
        print("❌ No URLs found to process")
        return

    # Initialize filter
    filter_tool = URLDeduplicationFilter(scraperapi_key=scraperapi_key)

    # Choose filtering mode
    print("\n🔧 Choose filtering mode:")
    print("1. Fast mode (no API calls, only check against existing data)")
    print("2. Thorough mode (use ScraperAPI to check phone numbers - costs money)")

    # For demo, use fast mode. Change to use_scraperapi=True for thorough mode
    use_scraperapi = False  # Set to True if you want to use ScraperAPI

    if use_scraperapi and not scraperapi_key:
        print("⚠️ ScraperAPI key not provided, switching to fast mode")
        use_scraperapi = False

    # Filter URLs (limit to first 100 for testing)
    test_urls = urls[:100]  # Remove this limit for full processing

    results = filter_tool.filter_urls(
        urls=test_urls,
        output_file="url_filter_results",
        use_scraperapi=use_scraperapi
    )

    # Show some examples
    if results['urls_skipped']:
        print(f"\n🚫 SAMPLE SKIPPED URLS (duplicates):")
        for skip in results['urls_skipped'][:5]:
            print(f"   • {skip['url']} (phone: {skip['cleaned_phone']})")

    if results['urls_to_process']:
        print(f"\n✅ SAMPLE URLS TO PROCESS:")
        for url in results['urls_to_process'][:5]:
            print(f"   • {url}")

    print(f"\n🎯 NEXT STEPS:")
    print(f"   1. Use the filtered URLs for scraping to avoid duplicates")
    print(f"   2. Estimated API call savings: {results['stats']['urls_skipped']} requests")
    print(f"   3. Focus scraping on {results['stats']['urls_to_process']} unique URLs")

if __name__ == "__main__":
    main()
