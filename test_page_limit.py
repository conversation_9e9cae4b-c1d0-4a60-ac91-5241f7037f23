#!/usr/bin/env python3
"""
Test script to verify the enhanced page scraping capabilities
Tests that the scraper can handle at least 25 pages per city-source combination
"""

import sys
from web_scraper import WebScraper

def test_page_scraping_limits():
    """Test the page scraping limits and settings"""
    print("Testing Enhanced Page Scraping Capabilities")
    print("=" * 60)
    
    # Use your Mistral API key
    scraper = WebScraper(mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso")
    scraper.request_delay = 0.5  # Faster for testing
    
    # Display current settings
    print("Current Page Scraping Settings:")
    print(f"  Maximum pages per city: {scraper.max_pages_per_city}")
    print(f"  Minimum pages to scrape: {scraper.min_pages_to_scrape}")
    print(f"  Max consecutive empty pages: {scraper.max_consecutive_empty_pages}")
    print()
    
    try:
        # Get first city-source combination
        cities = scraper.parse_url_list()
        if not cities:
            print("✗ No cities available for testing")
            return False
        
        # Find a city from aaok source (typically has more pages)
        test_city = None
        for city in cities:
            if city['source'] == 'aaok':
                test_city = city
                break
        
        if not test_city:
            test_city = cities[0]
        
        print(f"Testing with: {test_city['city']}, {test_city['state']} from {test_city['source']}")
        print(f"Base URL: {test_city['url']}")
        print()
        
        # Test page scraping with detailed logging
        print("Starting multi-page scraping test...")
        print("Will attempt to scrape pages until:")
        print(f"  - Minimum {scraper.min_pages_to_scrape} pages reached AND")
        print(f"  - {scraper.max_consecutive_empty_pages} consecutive empty pages found")
        print(f"  - OR maximum {scraper.max_pages_per_city} pages reached")
        print()
        
        # Manually test the page scraping logic
        all_dedicated_urls = []
        page_num = 1
        empty_page_count = 0
        pages_with_content = 0
        
        while True:
            # Generate URL for this page
            import re
            page_url = re.sub(r'/\d+$', f'/{page_num}', test_city['url'])
            
            print(f"Testing page {page_num}: {page_url}")
            
            # Get search page HTML
            search_html = scraper.execute_curl_request(page_url, scraper.search_curl_template)
            if not search_html:
                print(f"  ✗ Failed to get page {page_num}")
                empty_page_count += 1
            else:
                # Extract dedicated page URLs
                page_dedicated_urls = scraper.extract_dedicated_urls(search_html)
                if not page_dedicated_urls:
                    empty_page_count += 1
                    print(f"  ✗ No URLs found on page {page_num} (empty page {empty_page_count}/{scraper.max_consecutive_empty_pages})")
                    
                    # Check stopping conditions
                    if page_num > scraper.min_pages_to_scrape and empty_page_count >= scraper.max_consecutive_empty_pages:
                        print(f"  → Stopping: {scraper.max_consecutive_empty_pages} consecutive empty pages after minimum {scraper.min_pages_to_scrape} pages")
                        break
                    elif page_num <= scraper.min_pages_to_scrape:
                        print(f"  → Continuing: Need to reach minimum {scraper.min_pages_to_scrape} pages (currently at {page_num})")
                else:
                    empty_page_count = 0  # Reset counter
                    pages_with_content += 1
                    all_dedicated_urls.extend(page_dedicated_urls)
                    print(f"  ✓ Found {len(page_dedicated_urls)} URLs on page {page_num} (total: {len(all_dedicated_urls)})")
            
            page_num += 1
            
            # Safety check
            if page_num > scraper.max_pages_per_city:
                print(f"  → Stopping: Reached maximum page limit ({scraper.max_pages_per_city})")
                break
            
            # Stop test after reasonable number for demo
            if page_num > 30:  # Limit test to 30 pages for demo
                print(f"  → Stopping test at page 30 for demo purposes")
                break
            
            # Rate limiting
            import time
            time.sleep(scraper.request_delay)
        
        print()
        print("Test Results:")
        print(f"  Pages tested: {page_num - 1}")
        print(f"  Pages with content: {pages_with_content}")
        print(f"  Total URLs found: {len(all_dedicated_urls)}")
        print(f"  Final empty page count: {empty_page_count}")
        
        # Validate results
        pages_tested = page_num - 1
        if pages_tested >= scraper.min_pages_to_scrape:
            print(f"  ✓ Successfully tested at least {scraper.min_pages_to_scrape} pages ({pages_tested} pages tested)")
            return True
        else:
            print(f"  ✗ Only tested {pages_tested} pages, less than minimum {scraper.min_pages_to_scrape}")
            return False
            
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        return False

def test_settings_validation():
    """Test that the settings are properly configured"""
    print("\nValidating Page Scraping Settings")
    print("=" * 40)
    
    scraper = WebScraper()
    
    # Check settings
    settings_valid = True
    
    if scraper.max_pages_per_city < 25:
        print(f"✗ max_pages_per_city ({scraper.max_pages_per_city}) should be at least 25")
        settings_valid = False
    else:
        print(f"✓ max_pages_per_city: {scraper.max_pages_per_city}")
    
    if scraper.min_pages_to_scrape < 25:
        print(f"✗ min_pages_to_scrape ({scraper.min_pages_to_scrape}) should be at least 25")
        settings_valid = False
    else:
        print(f"✓ min_pages_to_scrape: {scraper.min_pages_to_scrape}")
    
    if scraper.max_consecutive_empty_pages < 3:
        print(f"✗ max_consecutive_empty_pages ({scraper.max_consecutive_empty_pages}) should be at least 3")
        settings_valid = False
    else:
        print(f"✓ max_consecutive_empty_pages: {scraper.max_consecutive_empty_pages}")
    
    return settings_valid

def main():
    """Run page limit tests"""
    print("Page Scraping Capability Test Suite")
    print("=" * 70)
    
    tests = [
        test_settings_validation,
        test_page_scraping_limits
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✓ PASSED\n")
            else:
                print("✗ FAILED\n")
        except Exception as e:
            print(f"✗ FAILED with exception: {e}\n")
    
    print("=" * 70)
    print(f"Page Limit Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All page scraping tests passed!")
        print("\nPage scraping capabilities confirmed:")
        print("  ✓ Can scrape at least 25 pages per city-source combination")
        print("  ✓ Safety limits in place to prevent infinite loops")
        print("  ✓ Intelligent stopping based on content availability")
        print("  ✓ Enhanced settings for comprehensive coverage")
        print("\nThe scraper is ready for production with enhanced page coverage!")
        return 0
    else:
        print("✗ Some page scraping tests failed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
