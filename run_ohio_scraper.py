#!/usr/bin/env python3
"""
Ohio Scraper Runner
Complete workflow for scraping Ohio cities (Columbus, Cleveland, Dayton, Toledo, Cincinnati)

Usage:
    python run_ohio_scraper.py                    # Interactive mode
    python run_ohio_scraper.py --complete         # Run Phase 1 + Phase 2
    python run_ohio_scraper.py --phase1-only      # Only collect URLs
    python run_ohio_scraper.py --phase2-only      # Only extract data from existing URLs
    python run_ohio_scraper.py --test-mode        # Limited test run
"""

import os
import sys
import json
import argparse
import logging
import glob
from datetime import datetime
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OhioScraperRunner:
    """Runner for complete Ohio cities scraping workflow"""

    def __init__(self):
        self.ohio_cities = ['Columbus', 'Cleveland', 'Dayton', 'Toledo', 'Cincinnati']
        self.state = 'Ohio'

    def run_phase1_url_collection(self, cities=None, max_pages=50, test_mode=False):
        """Run Phase 1: URL collection from search pages"""
        logger.info("=" * 80)
        logger.info("PHASE 1: COLLECTING URLS FROM SEARCH PAGES")
        logger.info("=" * 80)

        # Import and run direct URL scraper
        try:
            from direct_url_scraper import DirectURLScraper
        except ImportError:
            logger.error("direct_url_scraper.py not found!")
            return None

        # Initialize scraper
        delay = 3.0 if test_mode else 2.0
        max_pages = 3 if test_mode else max_pages

        scraper = DirectURLScraper(delay=delay, max_pages_per_source=max_pages)

        # Run scraping
        result = scraper.scrape_state_cities(
            self.state,
            cities,
            max_pages,
            test_mode
        )

        if result:
            logger.info(f"✅ Phase 1 completed: {result}")
            return result
        else:
            logger.error("❌ Phase 1 failed")
            return None

    def find_latest_url_file(self):
        """Find the most recent Ohio URL file"""
        # Look for Ohio URL files
        patterns = [
            'phase1_all_urls_Ohio_*.json',
            'phase1_urls_*_Ohio.json',
            '*Ohio*.json'
        ]

        found_files = []
        for pattern in patterns:
            found_files.extend(glob.glob(pattern))

        if not found_files:
            return None

        # Sort by modification time (newest first)
        found_files.sort(key=os.path.getmtime, reverse=True)
        return found_files[0]

    def run_phase2_data_extraction(self, json_file=None, delay=1.0, workers=5, limit=None):
        """Run Phase 2: Extract data from profile URLs"""
        logger.info("=" * 80)
        logger.info("PHASE 2: EXTRACTING DATA FROM PROFILE PAGES")
        logger.info("=" * 80)

        # Find URL file if not specified
        if not json_file:
            json_file = self.find_latest_url_file()
            if not json_file:
                logger.error("No URL file found for Phase 2!")
                logger.info("Run Phase 1 first or specify a JSON file")
                return None
            logger.info(f"Using URL file: {json_file}")

        if not os.path.exists(json_file):
            logger.error(f"URL file not found: {json_file}")
            return None

        # Import and run extractor
        try:
            from comprehensive_data_extractor import ProfileDataExtractor, load_urls_from_json
        except ImportError:
            logger.error("comprehensive_data_extractor.py not found!")
            return None

        # Load URLs
        urls_data = load_urls_from_json(json_file)
        if not urls_data:
            logger.error(f"No URLs found in {json_file}")
            return None

        logger.info(f"Loaded {len(urls_data)} URLs from {json_file}")

        # Apply limit if specified
        if limit:
            urls_data = urls_data[:limit]
            logger.info(f"Limited to first {limit} URLs for testing")

        # Create output filename
        base_name = os.path.splitext(os.path.basename(json_file))[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"ohio_extraction_results_{timestamp}.xlsx"

        # Initialize extractor
        extractor = ProfileDataExtractor(delay=delay, max_workers=workers)

        # Process URLs
        logger.info(f"Starting extraction with {workers} workers, {delay}s delay...")
        results = extractor.process_urls(urls_data)

        # Save results
        extractor.save_to_excel(results, output_file)

        # Print summary
        successful = sum(1 for r in results if r['status'] == 'success')
        failed = len(results) - successful
        unique_phones = len(set(r['phone_number'] for r in results if r.get('phone_number')))

        logger.info("=" * 80)
        logger.info("PHASE 2 COMPLETE")
        logger.info("=" * 80)
        logger.info(f"Total URLs processed: {len(results)}")
        logger.info(f"Successful extractions: {successful}")
        logger.info(f"Failed extractions: {failed}")
        logger.info(f"Success rate: {successful/len(results)*100:.1f}%")
        logger.info(f"Unique phone numbers: {unique_phones}")
        logger.info(f"Results saved to: {output_file}")

        return output_file

    def run_complete_workflow(self, cities=None, max_pages=15, delay=1.0, workers=5, test_mode=False):
        """Run both Phase 1 and Phase 2"""
        logger.info("🚀 STARTING COMPLETE OHIO SCRAPING WORKFLOW")
        logger.info("=" * 80)

        if cities:
            logger.info(f"Cities: {', '.join(cities)}")
        else:
            logger.info(f"Cities: All Ohio cities ({', '.join(self.ohio_cities)})")

        if test_mode:
            logger.info("🧪 TEST MODE: Limited scope")

        # Phase 1: URL Collection
        urls_file = self.run_phase1_url_collection(cities, max_pages, test_mode)
        if not urls_file:
            logger.error("Phase 1 failed - cannot proceed to Phase 2")
            return None

        # Phase 2: Data Extraction
        limit = 50 if test_mode else None
        results_file = self.run_phase2_data_extraction(urls_file, delay, workers, limit)

        if results_file:
            logger.info("🎉 COMPLETE WORKFLOW FINISHED SUCCESSFULLY!")
            logger.info(f"📁 Final results: {results_file}")
            return results_file
        else:
            logger.error("Phase 2 failed")
            return None

    def show_available_files(self):
        """Show available URL files for Phase 2"""
        logger.info("📁 Available URL files for Phase 2:")

        patterns = ['*Ohio*.json', 'phase1_*.json', '*urls*.json']
        found_files = []

        for pattern in patterns:
            found_files.extend(glob.glob(pattern))

        if not found_files:
            logger.info("  No URL files found")
            return []

        # Remove duplicates and sort by modification time
        found_files = list(set(found_files))
        found_files.sort(key=os.path.getmtime, reverse=True)

        for i, file in enumerate(found_files, 1):
            try:
                size_mb = os.path.getsize(file) / (1024 * 1024)
                mtime = datetime.fromtimestamp(os.path.getmtime(file))

                # Count URLs
                try:
                    with open(file, 'r') as f:
                        data = json.load(f)
                        url_count = len(data) if isinstance(data, list) else 0
                except:
                    url_count = "Unknown"

                logger.info(f"  {i:2d}. {file}")
                logger.info(f"      📊 {url_count} URLs, {size_mb:.2f} MB")
                logger.info(f"      📅 {mtime.strftime('%Y-%m-%d %H:%M:%S')}")

            except Exception as e:
                logger.info(f"  {i:2d}. {file} (error reading details)")

        return found_files

    def interactive_mode(self):
        """Interactive mode for user input"""
        print("=" * 80)
        print("🏙️ OHIO SCRAPER RUNNER - INTERACTIVE MODE")
        print("=" * 80)

        print(f"\nAvailable Ohio cities: {', '.join(self.ohio_cities)}")

        # Choose workflow type
        print("\n1️⃣ Choose workflow:")
        print("  1. Complete workflow (Phase 1 + Phase 2)")
        print("  2. Phase 1 only (collect URLs)")
        print("  3. Phase 2 only (extract data from existing URLs)")
        print("  4. Test mode (limited scope)")

        while True:
            choice = input("\nEnter choice (1-4): ").strip()
            if choice in ['1', '2', '3', '4']:
                break
            print("❌ Invalid choice. Please enter 1, 2, 3, or 4.")

        workflow_type = int(choice)

        # City selection
        cities_input = input(f"\nEnter cities (comma-separated, or press Enter for all): ").strip()
        custom_cities = None
        if cities_input:
            custom_cities = [city.strip() for city in cities_input.split(',')]
            # Validate cities
            valid_cities = [city for city in custom_cities if city in self.ohio_cities]
            if valid_cities != custom_cities:
                invalid = [city for city in custom_cities if city not in self.ohio_cities]
                print(f"⚠️ Invalid cities ignored: {invalid}")
            custom_cities = valid_cities if valid_cities else None

        # Settings based on workflow type
        if workflow_type == 4:  # Test mode
            print("\n🧪 TEST MODE SETTINGS:")
            max_pages = 50
            delay = 2.0
            workers = 3
            test_mode = True
            print(f"   Max pages: {max_pages}")
            print(f"   Delay: {delay}s")
            print(f"   Workers: {workers}")
        else:
            # Get settings
            max_pages_input = input(f"\nMax pages per city/source (default 15): ").strip()
            max_pages = int(max_pages_input) if max_pages_input else 15

            delay_input = input(f"Delay between requests (default 1.0s): ").strip()
            delay = float(delay_input) if delay_input else 1.0

            workers_input = input(f"Number of workers (default 5): ").strip()
            workers = int(workers_input) if workers_input else 5

            test_mode = False

        # Show configuration
        print(f"\n📋 CONFIGURATION:")
        print(f"   Workflow: {['', 'Complete', 'Phase 1 only', 'Phase 2 only', 'Test mode'][workflow_type]}")
        print(f"   Cities: {custom_cities if custom_cities else 'All Ohio cities'}")
        print(f"   Max pages: {max_pages}")
        print(f"   Delay: {delay}s")
        print(f"   Workers: {workers}")

        # Confirm
        confirm = input(f"\nStart scraping? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ Operation cancelled.")
            return

        # Execute based on workflow type
        try:
            if workflow_type == 1 or workflow_type == 4:  # Complete or test
                result = self.run_complete_workflow(custom_cities, max_pages, delay, workers, test_mode)
            elif workflow_type == 2:  # Phase 1 only
                result = self.run_phase1_url_collection(custom_cities, max_pages, test_mode)
            elif workflow_type == 3:  # Phase 2 only
                available_files = self.show_available_files()
                if not available_files:
                    print("No URL files found for Phase 2")
                    return

                file_choice = input(f"\nChoose file number (1-{len(available_files)}): ").strip()
                try:
                    file_idx = int(file_choice) - 1
                    if 0 <= file_idx < len(available_files):
                        json_file = available_files[file_idx]
                        limit = 50 if test_mode else None
                        result = self.run_phase2_data_extraction(json_file, delay, workers, limit)
                    else:
                        print("Invalid file selection")
                        return
                except ValueError:
                    print("Invalid input")
                    return

            if result:
                print(f"\n🎉 SUCCESS! Results saved to: {result}")
            else:
                print(f"\n❌ Operation failed")

        except KeyboardInterrupt:
            print(f"\n🛑 Operation interrupted by user")
        except Exception as e:
            print(f"\n❌ Error: {e}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Ohio Scraper Runner - Complete workflow for Ohio cities',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    # Workflow options
    workflow_group = parser.add_mutually_exclusive_group()
    workflow_group.add_argument('--complete', action='store_true', help='Run complete workflow (Phase 1 + Phase 2)')
    workflow_group.add_argument('--phase1-only', action='store_true', help='Run Phase 1 only (URL collection)')
    workflow_group.add_argument('--phase2-only', action='store_true', help='Run Phase 2 only (data extraction)')
    workflow_group.add_argument('--test-mode', action='store_true', help='Test mode (limited scope)')
    workflow_group.add_argument('--interactive', '-i', action='store_true', help='Interactive mode')

    # Configuration options
    parser.add_argument('--cities', type=str, help='Comma-separated list of Ohio cities')
    parser.add_argument('--max-pages', type=int, default=15, help='Max pages per city/source')
    parser.add_argument('--delay', type=float, default=1.0, help='Delay between requests (seconds)')
    parser.add_argument('--workers', type=int, default=5, help='Number of worker threads')
    parser.add_argument('--json-file', type=str, help='Specific JSON file for Phase 2')
    parser.add_argument('--limit', type=int, help='Limit number of URLs (for testing)')

    # Utility options
    parser.add_argument('--list-files', action='store_true', help='List available URL files')

    args = parser.parse_args()

    # Initialize runner
    runner = OhioScraperRunner()

    # Handle utility options
    if args.list_files:
        runner.show_available_files()
        return

    # Parse cities
    custom_cities = None
    if args.cities:
        custom_cities = [city.strip() for city in args.cities.split(',')]
        # Validate against Ohio cities
        valid_cities = [city for city in custom_cities if city in runner.ohio_cities]
        if valid_cities != custom_cities:
            invalid = [city for city in custom_cities if city not in runner.ohio_cities]
            logger.warning(f"Invalid Ohio cities ignored: {invalid}")
        custom_cities = valid_cities if valid_cities else None

    # Interactive mode (default if no args)
    if args.interactive or len(sys.argv) == 1:
        runner.interactive_mode()
        return

    try:
        # Execute based on selected workflow
        if args.complete:
            result = runner.run_complete_workflow(custom_cities, args.max_pages, args.delay, args.workers)
        elif args.phase1_only:
            result = runner.run_phase1_url_collection(custom_cities, args.max_pages)
        elif args.phase2_only:
            json_file = args.json_file or runner.find_latest_url_file()
            if not json_file:
                logger.error("No URL file found. Use --json-file to specify one or run Phase 1 first")
                return
            result = runner.run_phase2_data_extraction(json_file, args.delay, args.workers, args.limit)
        elif args.test_mode:
            result = runner.run_complete_workflow(custom_cities, 3, 2.0, 3, test_mode=True)
        else:
            logger.error("No workflow specified. Use --complete, --phase1-only, --phase2-only, or --interactive")
            return

        if result:
            logger.info(f"🎉 SUCCESS! Results: {result}")
        else:
            logger.error(f"❌ Operation failed")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info(f"🛑 Operation interrupted by user")
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
