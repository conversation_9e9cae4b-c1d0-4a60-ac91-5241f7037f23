#!/usr/bin/env python3
"""
Debug script to check what <PERSON><PERSON><PERSON> is actually returning
"""

import json
import re
from mistralai import Mi<PERSON><PERSON>

def test_mistral_response():
    """Test what <PERSON>stra<PERSON> is returning for a simple batch"""
    
    # Use your API key
    api_key = "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G"
    client = Mistral(api_key=api_key)
    
    # Simple test prompt
    prompt = """
    You are extracting data from 2 escort profile pages in clean markdown format. For each page, extract the following information:

    1. title: Profile title or headline
    2. name: Person's name
    3. age: Age (must be ≤30, skip if >30)
    4. phone: Phone number
    5. description: Profile description
    6. social_media: Social media handles/links (Instagram, Twitter, Snapchat, OnlyFans, etc.)
    7. email: Email address
    8. website: Website links
    9. posted_date: When the post was created
    10. post_id: Unique post identifier

    IMPORTANT: Only include profiles where age ≤30 AND gender is woman. Skip any profiles with age >30 or not women.

    Pages to process (in clean markdown format):

    --- <PERSON><PERSON> 0 ---
    # Profile: Sexy Latina 22
    **URL:** https://example.com/profile1

    ## Profile Content:
    Hi I'm <PERSON>, 22 years old latina woman
    Call me: 555-1234
    Instagram: @maria_sexy22
    Available 24/7

    ---

    --- PAGE 1 ---
    # Profile: Hot Blonde 25
    **URL:** https://example.com/profile2

    ## Profile Content:
    Hey I'm Sarah, 25 year old blonde
    Text me: 555-5678
    Snapchat: sarah_hot25
    Available evenings

    ---

    
    Return a JSON array with one object per valid page (only for women with age ≤30). Each object should have:
    {
        "page_index": <index>,
        "url": "<url>",
        "title": "<title>",
        "name": "<name>", 
        "age": "<age>",
        "phone": "<phone>",
        "description": "<description>",
        "social_media": "<social_media>",
        "email": "<email>",
        "website": "<website>",
        "posted_date": "<posted_date>",
        "post_id": "<post_id>"
    }
    
    If any field is not found, use null. Only return valid JSON array.
    """
    
    try:
        print("Sending test prompt to Mistral...")
        response = client.chat.complete(
            model="mistral-large-latest",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=2000
        )
        
        result_text = response.choices[0].message.content.strip()
        
        print("=" * 60)
        print("RAW MISTRAL RESPONSE:")
        print("=" * 60)
        print(result_text)
        print("=" * 60)
        
        # Try to parse as JSON
        try:
            parsed_json = json.loads(result_text)
            print("✓ JSON PARSING SUCCESSFUL!")
            print("Parsed result:")
            print(json.dumps(parsed_json, indent=2))
            
            if isinstance(parsed_json, list):
                print(f"Found {len(parsed_json)} profiles")
            else:
                print("Result is not a list")
                
        except json.JSONDecodeError as e:
            print("✗ JSON PARSING FAILED!")
            print(f"Error: {e}")
            
            # Try fallback parsing
            json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
            if json_match:
                try:
                    fallback_result = json.loads(json_match.group())
                    print("✓ FALLBACK PARSING SUCCESSFUL!")
                    print("Fallback result:")
                    print(json.dumps(fallback_result, indent=2))
                    
                    if isinstance(fallback_result, list):
                        print(f"Found {len(fallback_result)} profiles via fallback")
                    
                except json.JSONDecodeError as e2:
                    print("✗ FALLBACK PARSING ALSO FAILED!")
                    print(f"Fallback error: {e2}")
            else:
                print("✗ NO JSON ARRAY FOUND IN RESPONSE!")
        
    except Exception as e:
        print(f"API call failed: {e}")

if __name__ == "__main__":
    test_mistral_response()
