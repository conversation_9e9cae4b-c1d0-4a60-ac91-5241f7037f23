# Enhanced Web Scraping System with AI and Advanced Filtering

A comprehensive web scraping system that extracts data from search pages and dedicated pages with advanced filtering and AI-powered text extraction.

## Features

- **Advanced Filtering**:
  - Age filter: Only profiles ≤30 years old
  - Gender filter: Only women profiles
  - Multiple sources: aaok.com and aypapi.com instead of escortalligator
- **AI-Powered Extraction**: Mistral Large model for enhanced text data extraction
- **Systematic Processing**: Processes all states and cities from multiple sources
- **cURL-based Requests**: Uses the exact cURL commands from reference files for authentic requests
- **Comprehensive Data Extraction**: Extracts title, age, name, phone, description, posting date, and more
- **Excel Output**: Organizes data by city and source with proper formatting
- **Error Handling**: Robust error handling with logging and retry mechanisms
- **Rate Limiting**: Configurable delays between requests to avoid overwhelming servers
- **Progress Tracking**: Periodic backups and resume functionality

## Files Structure

```
├── web_scraper.py          # Main scraping system
├── test_scraper.py         # Test suite for validation
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── url_list.md            # List of all states and cities with URLs
├── search page_1.html     # Reference search page with cURL command
├── search page_2.html     # Additional search page reference
├── dedicated page.html    # Reference dedicated page with cURL command
└── dedicate page sample 2.html  # Additional dedicated page reference
```

## Installation

1. **Install Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up Mistral AI (Optional but Recommended)**:
   - Get API key from [Mistral AI](https://mistral.ai/)
   - Set environment variable: `export MISTRAL_API_KEY=your_key_here`
   - Or pass via command line: `--mistral-key your_key_here`

3. **Ensure Reference Files Are Present**:
   - `url_list.md` - Contains all states and cities with their URLs
   - `search page_1.html` - Contains the cURL command for search pages
   - `dedicated page.html` - Contains the cURL command for dedicated pages

## Usage

### Testing the System

Before running the full scrape, test the system:

```bash
python test_scraper.py
```

This will:
- Test URL list parsing
- Test cURL command loading
- Test HTML parsing with sample files
- Run a small scrape with the first city
- Generate `test_output.xlsx` with sample results

### Running the Full Scraper

**Test with Limited City-Source Combinations**:
```bash
python web_scraper.py --max-cities 5
```

**Full Scrape with AI Enhancement**:
```bash
python web_scraper.py --mistral-key YOUR_API_KEY
```

**Full Scrape (All Cities from Both Sources)**:
```bash
python web_scraper.py
```

**Custom Options**:
```bash
python web_scraper.py --max-cities 10 --output custom_output.xlsx --delay 3.0 --mistral-key YOUR_KEY
```

### Command Line Options

- `--max-cities N`: Limit processing to first N city-source combinations (useful for testing)
- `--output FILE`: Specify output Excel file name (default: scraped_data.xlsx)
- `--delay SECONDS`: Set delay between requests in seconds (default: 2.0)
- `--mistral-key KEY`: Mistral AI API key for enhanced text extraction

## Workflow

The enhanced scraper follows this systematic workflow:

1. **Parse URL List**: Extracts all states and cities, generates URLs for both aaok and aypapi sources
2. **For Each City-Source Combination**:
   - Use cURL command to fetch the search page
   - Extract dedicated page URLs from search results (age ≤30 only)
   - For each dedicated page URL:
     - Use cURL command to fetch the individual page
     - Check if profile is for a woman (skip if not)
     - Extract comprehensive data using Mistral AI + traditional parsing
3. **Save Results**: Organize all data by city and source in Excel format

## Data Fields Extracted

From each dedicated page, the system extracts:

- **Basic Info**: Title, Age, Name
- **Contact**: Phone number
- **Content**: Description/body text
- **Metadata**: Posting date, Post ID, URL
- **Location**: City, State
- **Tracking**: Scrape timestamp, Search page URL

## Output Format

The Excel file contains columns:
- `state`: State name
- `city`: City name
- `source`: Source website (aaok or aypapi)
- `title`: Post title
- `name`: Person's name
- `age`: Age (≤30 only)
- `phone`: Phone number
- `description`: Full description text
- `posted_date`: When the post was made
- `post_id`: Unique post identifier
- `url`: Direct URL to the dedicated page
- `search_url`: URL of the search page where this was found
- `scraped_at`: Timestamp when data was scraped

## Error Handling

The system includes:
- **Request Timeouts**: 30-second timeout for cURL requests
- **Retry Logic**: Continues processing even if individual pages fail
- **Progress Backups**: Saves progress every 10 cities
- **Comprehensive Logging**: Detailed logs in `scraper.log`
- **Resume Capability**: Can skip already processed cities

## Rate Limiting

- Default 2-second delay between requests
- Configurable via `--delay` parameter
- Separate delays between dedicated pages and between cities

## Monitoring Progress

- Real-time logging to console and `scraper.log`
- Progress backups every 10 cities (`progress_backup_N.xlsx`)
- Final output in `final_scraped_data.xlsx`

## Troubleshooting

**Common Issues**:

1. **cURL Command Not Found**: Ensure the reference HTML files contain valid cURL commands at the top
2. **No Data Extracted**: Check if the HTML structure matches the expected format
3. **Request Failures**: Increase delay with `--delay` parameter
4. **Memory Issues**: The system handles large datasets by organizing data efficiently

**Debug Steps**:

1. Run the test suite: `python test_scraper.py`
2. Check `scraper.log` for detailed error messages
3. Test with a small number of cities first: `--max-cities 1`
4. Verify reference files are properly formatted

## Performance

- Processes approximately 30 cities per hour (with 2-second delays)
- Memory efficient - handles thousands of records
- Disk space: ~1MB per 1000 records in Excel format

## Legal and Ethical Considerations

- Respects rate limiting to avoid overwhelming servers
- Uses authentic cURL commands from reference files
- Includes proper error handling and logging
- Designed for research and data analysis purposes

## Support

For issues or questions:
1. Check the test suite output
2. Review the log file (`scraper.log`)
3. Verify all reference files are present and properly formatted
