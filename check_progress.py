#!/usr/bin/env python3
"""
Progress Checker and Manual Saver
Check current progress and manually save if needed
"""

import json
import os
import glob
from datetime import datetime

def find_latest_progress_file():
    """Find the most recent progress file"""
    progress_files = glob.glob("scraping_progress_*.json")
    if not progress_files:
        return None

    # Sort by modification time, most recent first
    progress_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    return progress_files[0]

def check_progress(progress_file):
    """Check and display current progress"""
    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)

        scraped_count = len(progress_data.get('scraped_profiles', []))
        stats = progress_data.get('stats', {})
        current_key = progress_data.get('current_key_index', 0) + 1
        key_requests = progress_data.get('current_key_requests', 0)

        print(f"📊 CURRENT PROGRESS REPORT")
        print("=" * 50)
        print(f"📂 Progress file: {progress_file}")
        print(f"📅 File modified: {datetime.fromtimestamp(os.path.getmtime(progress_file))}")
        print(f"✅ Profiles scraped: {scraped_count}")
        print(f"🔄 Duplicates skipped: {stats.get('duplicates_skipped', 0)}")
        print(f"❌ Failed extractions: {stats.get('failed_extractions', 0)}")
        print(f"🎯 API requests made: {stats.get('api_requests', 0)}")
        print(f"🔑 Current API key: {current_key}")
        print(f"📈 Requests on current key: {key_requests}/900")

        return progress_data

    except Exception as e:
        print(f"❌ Error reading progress file: {e}")
        return None

def save_backup_progress(progress_data):
    """Save a backup copy of current progress"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = f"progress_backup_{timestamp}.json"

    try:
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)

        print(f"💾 Backup saved to: {backup_file}")
        return backup_file

    except Exception as e:
        print(f"❌ Error saving backup: {e}")
        return None

def extract_results_from_progress(progress_data):
    """Extract and save just the results from progress"""
    scraped_profiles = progress_data.get('scraped_profiles', [])

    if scraped_profiles:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"extracted_results_{timestamp}.json"

        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(scraped_profiles, f, indent=2, ensure_ascii=False)

            print(f"📄 Results extracted to: {results_file}")
            print(f"✅ {len(scraped_profiles)} profiles saved")
            return results_file

        except Exception as e:
            print(f"❌ Error extracting results: {e}")
            return None
    else:
        print("⚠️ No scraped profiles found in progress")
        return None

def main():
    """Main function"""
    print("🔍 Progress Checker and Manual Saver")
    print("=" * 60)

    # Find latest progress file
    progress_file = find_latest_progress_file()

    if not progress_file:
        print("❌ No progress files found in current directory")
        print("Looking for files matching pattern: scraping_progress_*.json")
        return

    print(f"📂 Found latest progress file: {progress_file}")

    # Check current progress
    progress_data = check_progress(progress_file)

    if not progress_data:
        print("❌ Could not read progress data")
        return

    print(f"\n🛠️ AVAILABLE ACTIONS")
    print("-" * 30)
    print("1. Create backup copy")
    print("2. Extract results only")
    print("3. Show file list")
    print("4. Exit")

    while True:
        try:
            choice = input("\nSelect action (1-4): ").strip()

            if choice == '1':
                backup_file = save_backup_progress(progress_data)
                if backup_file:
                    print("✅ Backup created successfully")

            elif choice == '2':
                results_file = extract_results_from_progress(progress_data)
                if results_file:
                    print("✅ Results extracted successfully")

            elif choice == '3':
                print(f"\n📁 FILES IN CURRENT DIRECTORY:")
                progress_files = glob.glob("scraping_progress_*.json")
                backup_files = glob.glob("progress_backup_*.json")
                result_files = glob.glob("extracted_results_*.json")

                print(f"📊 Progress files ({len(progress_files)}):")
                for f in sorted(progress_files, reverse=True):
                    print(f"   - {f}")

                print(f"💾 Backup files ({len(backup_files)}):")
                for f in sorted(backup_files, reverse=True):
                    print(f"   - {f}")

                print(f"📄 Result files ({len(result_files)}):")
                for f in sorted(result_files, reverse=True):
                    print(f"   - {f}")

            elif choice == '4':
                print("👋 Goodbye!")
                break

            else:
                print("❌ Invalid choice. Please select 1-4.")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
