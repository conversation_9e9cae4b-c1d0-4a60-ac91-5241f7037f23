#!/usr/bin/env python3
"""
Batch Parallel NYC Boroughs Scraper with Multiple Mistral API Keys
Multi-worker parallel processing with batch Mistral API calls for efficiency
"""

import sys
import os
import json
import time
import threading
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import pandas as pd
from pathlib import Path
import re
from bs4 import BeautifulSoup

from nyc_boroughs_scraper import NYCBoroughsScraper

class BatchParallelNYCScraper:
    def __init__(self, mistral_api_keys: List[str] = None, max_workers: int = 5, batch_size: int = 10):
        """Initialize batch parallel NYC scraper with multiple API keys"""
        # Default to your 5 API keys if none provided
        default_keys = [
            "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G",
            "OHUPD3rpUQBbbd9FHpwnQpdQXIckRXqv", 
            "zeUtrAhXZm7RXe2Knt0xWGb19j3vb3f4",
            "Z9G5EWlDgYq8RtkV8xPfs7hZuAYghzg0",
            "e7QxoqwJNSPjcXFVmnEVgpAInrkWlRLS"
        ]
        
        self.mistral_api_keys = mistral_api_keys or default_keys
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.progress_file = "batch_parallel_nyc_progress.json"
        self.results_lock = threading.Lock()
        self.all_scraped_data = []
        self.completed_combinations = set()
        
        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] - %(message)s',
            handlers=[
                logging.FileHandler('batch_parallel_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # Log configuration
        self.logger.info(f"Initialized with {len(self.mistral_api_keys)} Mistral API keys for {max_workers} workers")
        self.logger.info(f"Batch processing: {batch_size} pages per Mistral API call")
        for i in range(min(max_workers, len(self.mistral_api_keys))):
            key_preview = self.mistral_api_keys[i][:8] + "..." + self.mistral_api_keys[i][-4:]
            self.logger.info(f"Worker {i} will use API key: {key_preview}")
    
    def extract_batch_with_mistral(self, html_batch: List[Tuple[str, str]], mistral_client, worker_id: int) -> List[Dict]:
        """Extract data from multiple HTML pages in a single Mistral API call"""
        if not mistral_client or not html_batch:
            return []
        
        try:
            # Prepare batch content for Mistral
            batch_content = []
            for i, (url, html_content) in enumerate(html_batch):
                # Extract text content from HTML
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # Get text content
                text_content = soup.get_text()
                
                # Clean up text
                lines = (line.strip() for line in text_content.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                text_content = ' '.join(chunk for chunk in chunks if chunk)
                
                # Limit text length to prevent token overflow
                if len(text_content) > 3000:
                    text_content = text_content[:3000] + "..."
                
                batch_content.append({
                    'page_index': i,
                    'url': url,
                    'content': text_content
                })
            
            # Create batch prompt
            prompt = f"""
            You are extracting data from {len(batch_content)} escort profile pages. For each page, extract the following information:

            1. title: Profile title or headline
            2. name: Person's name
            3. age: Age (must be ≤30, skip if >30)
            4. phone: Phone number
            5. description: Profile description
            6. social_media: Social media handles/links (Instagram, Twitter, Snapchat, OnlyFans, etc.)
            7. email: Email address
            8. website: Website links
            9. posted_date: When the post was created
            10. post_id: Unique post identifier

            IMPORTANT: Only include profiles where age ≤30. Skip any profiles with age >30.

            Pages to process:
            """
            
            for page_data in batch_content:
                prompt += f"\n--- PAGE {page_data['page_index']} (URL: {page_data['url']}) ---\n"
                prompt += page_data['content'][:2000]  # Limit content per page
                prompt += "\n"
            
            prompt += """
            
            Return a JSON array with one object per page (only for ages ≤30). Each object should have:
            {
                "page_index": <index>,
                "url": "<url>",
                "title": "<title>",
                "name": "<name>", 
                "age": "<age>",
                "phone": "<phone>",
                "description": "<description>",
                "social_media": "<social_media>",
                "email": "<email>",
                "website": "<website>",
                "posted_date": "<posted_date>",
                "post_id": "<post_id>"
            }
            
            If any field is not found, use null. Only return valid JSON array.
            """
            
            # Make Mistral API call
            from mistralai import Mistral
            response = mistral_client.chat.complete(
                model="mistral-large-latest",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=4000  # Increased for batch processing
            )
            
            result_text = response.choices[0].message.content.strip()
            
            # Parse JSON response
            try:
                batch_results = json.loads(result_text)
                if not isinstance(batch_results, list):
                    batch_results = [batch_results]
                
                self.logger.info(f"Worker {worker_id} batch processed {len(html_batch)} pages, extracted {len(batch_results)} valid profiles")
                return batch_results
                
            except json.JSONDecodeError:
                # Try to extract JSON array from the response
                json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
                if json_match:
                    batch_results = json.loads(json_match.group())
                    self.logger.info(f"Worker {worker_id} batch processed {len(html_batch)} pages, extracted {len(batch_results)} valid profiles (fallback parsing)")
                    return batch_results
                
                self.logger.warning(f"Worker {worker_id} failed to parse batch Mistral response")
                return []
        
        except Exception as e:
            self.logger.error(f"Worker {worker_id} batch Mistral extraction failed: {e}")
            return []
    
    def scrape_single_combination_batch(self, combination: Dict[str, str], worker_id: int) -> Optional[List[Dict]]:
        """Scrape a single borough-source combination with batch Mistral processing"""
        borough_name = combination['city']
        source = combination['source']
        combo_key = f"{combination['state']}_{borough_name}_{source}"
        
        # Assign API key to worker (round-robin distribution)
        worker_api_key = self.mistral_api_keys[worker_id % len(self.mistral_api_keys)]
        key_preview = worker_api_key[:8] + "..." + worker_api_key[-4:]
        
        self.logger.info(f"Worker {worker_id} starting: {borough_name} from {source} (API key: {key_preview}, batch size: {self.batch_size})")
        
        try:
            # Create a dedicated scraper instance for this worker
            worker_scraper = NYCBoroughsScraper(worker_api_key)
            worker_scraper.request_delay = 0.3  # Faster since we're using batch processing
            
            # Initialize Mistral client for batch processing
            from mistralai import Mistral
            mistral_client = Mistral(api_key=worker_api_key)
            
            # Get all dedicated URLs for this combination (using existing method)
            city_name = combination['city']
            state_name = combination['state']
            base_url = combination['url']
            
            self.logger.info(f"Worker {worker_id} collecting all pages for {borough_name} from {source}")
            
            # Step 1: Collect all dedicated URLs (reuse existing logic)
            all_dedicated_urls = []
            page_num = 1
            empty_page_count = 0
            
            while True:
                page_url = re.sub(r'/\d+$', f'/{page_num}', base_url)
                
                # Get search page HTML
                search_html = worker_scraper.execute_curl_request(page_url, worker_scraper.search_curl_template)
                if not search_html:
                    self.logger.warning(f"Worker {worker_id} failed to get page {page_num} for {borough_name}")
                    empty_page_count += 1
                else:
                    # Extract dedicated page URLs
                    page_dedicated_urls = worker_scraper.extract_dedicated_urls(search_html)
                    if not page_dedicated_urls:
                        empty_page_count += 1
                        self.logger.info(f"Worker {worker_id} no URLs on page {page_num} for {borough_name} (empty {empty_page_count}/{worker_scraper.max_consecutive_empty_pages})")
                    else:
                        empty_page_count = 0
                        all_dedicated_urls.extend(page_dedicated_urls)
                        self.logger.info(f"Worker {worker_id} found {len(page_dedicated_urls)} URLs on page {page_num} for {borough_name}")
                
                # Check stopping conditions
                if page_num > worker_scraper.min_pages_to_scrape and empty_page_count >= worker_scraper.max_consecutive_empty_pages:
                    self.logger.info(f"Worker {worker_id} stopping after {worker_scraper.max_consecutive_empty_pages} consecutive empty pages (scraped {page_num} pages)")
                    break
                elif page_num <= worker_scraper.min_pages_to_scrape:
                    self.logger.info(f"Worker {worker_id} continuing - need minimum {worker_scraper.min_pages_to_scrape} pages (currently at {page_num})")
                
                time.sleep(worker_scraper.request_delay)
                page_num += 1
                
                if page_num > worker_scraper.max_pages_per_city:
                    self.logger.info(f"Worker {worker_id} reached maximum page limit ({worker_scraper.max_pages_per_city})")
                    break
            
            # Remove duplicates
            unique_urls = list(dict.fromkeys(all_dedicated_urls))
            self.logger.info(f"Worker {worker_id} collected {len(unique_urls)} unique URLs for {borough_name} from {source}")
            
            if not unique_urls:
                return []
            
            # Step 2: Collect all HTML content
            self.logger.info(f"Worker {worker_id} downloading HTML for {len(unique_urls)} pages")
            html_data = []
            
            for i, url in enumerate(unique_urls):
                if i > 0 and i % 50 == 0:
                    self.logger.info(f"Worker {worker_id} downloaded {i}/{len(unique_urls)} pages for {borough_name}")
                
                html = worker_scraper.execute_curl_request(url, worker_scraper.dedicated_curl_template)
                if html:
                    html_data.append((url, html))
                else:
                    self.logger.warning(f"Worker {worker_id} failed to get HTML for {url}")
                
                time.sleep(0.1)  # Small delay between downloads
            
            self.logger.info(f"Worker {worker_id} successfully downloaded {len(html_data)} HTML pages for {borough_name}")
            
            # Step 3: Process in batches with Mistral
            all_extracted_data = []
            total_batches = (len(html_data) + self.batch_size - 1) // self.batch_size
            
            for batch_num in range(total_batches):
                start_idx = batch_num * self.batch_size
                end_idx = min(start_idx + self.batch_size, len(html_data))
                batch = html_data[start_idx:end_idx]
                
                self.logger.info(f"Worker {worker_id} processing batch {batch_num + 1}/{total_batches} ({len(batch)} pages) for {borough_name}")
                
                # Process batch with Mistral
                batch_results = self.extract_batch_with_mistral(batch, mistral_client, worker_id)
                
                # Add metadata to each result
                for result in batch_results:
                    if result and isinstance(result, dict):
                        result['city'] = city_name
                        result['state'] = state_name
                        result['source'] = source
                        result['search_url'] = base_url
                        result['scraped_at'] = datetime.now().isoformat()
                        all_extracted_data.append(result)
                
                # Rate limiting between batches
                time.sleep(1.0)
            
            self.logger.info(f"Worker {worker_id} completed {borough_name} ({source}): {len(all_extracted_data)} records from {total_batches} batches")
            
            # Thread-safe data collection
            with self.results_lock:
                self.all_scraped_data.extend(all_extracted_data)
            
            # Save individual checkpoint
            checkpoint_name = f"batch_parallel_{borough_name}_{source}_checkpoint.xlsx"
            try:
                if all_extracted_data:
                    df = pd.DataFrame(all_extracted_data)
                    df.to_excel(checkpoint_name, index=False)
                    self.logger.info(f"Worker {worker_id} saved checkpoint: {checkpoint_name}")
            except Exception as e:
                self.logger.warning(f"Worker {worker_id} failed to save checkpoint: {e}")
            
            return all_extracted_data
            
        except Exception as e:
            self.logger.error(f"Worker {worker_id} failed on {borough_name} ({source}): {e}")
            return None
    
    def get_remaining_combinations(self) -> List[Dict[str, str]]:
        """Get remaining borough-source combinations to process"""
        # Create a temporary scraper to get all combinations (use first key)
        temp_scraper = NYCBoroughsScraper(self.mistral_api_keys[0])
        all_combinations = temp_scraper.get_nyc_boroughs()
        
        # Load existing progress
        completed = set()
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r') as f:
                    progress_data = json.load(f)
                    completed = set(progress_data.get('completed_combinations', []))
                    self.logger.info(f"Loaded {len(completed)} completed combinations from progress file")
            except Exception as e:
                self.logger.warning(f"Could not load progress file: {e}")
        
        # Filter out completed combinations
        remaining = []
        for combo in all_combinations:
            combo_key = f"{combo['state']}_{combo['city']}_{combo['source']}"
            if combo_key not in completed:
                remaining.append(combo)
        
        self.logger.info(f"Found {len(remaining)} remaining combinations to process")
        return remaining
    
    def save_progress(self, completed_combination: str = None):
        """Thread-safe progress saving"""
        with self.results_lock:
            if completed_combination:
                self.completed_combinations.add(completed_combination)
            
            progress_data = {
                'timestamp': datetime.now().isoformat(),
                'completed_combinations': list(self.completed_combinations),
                'total_records': len(self.all_scraped_data),
                'max_workers': self.max_workers,
                'batch_size': self.batch_size,
                'api_keys_count': len(self.mistral_api_keys)
            }
            
            try:
                with open(self.progress_file, 'w') as f:
                    json.dump(progress_data, f, indent=2)
                self.logger.info(f"Progress saved: {len(self.completed_combinations)} completed, {len(self.all_scraped_data)} total records")
            except Exception as e:
                self.logger.error(f"Failed to save progress: {e}")
    
    def run_batch_parallel_scraping(self) -> bool:
        """Run batch parallel scraping of remaining combinations"""
        self.logger.info(f"Starting batch parallel NYC boroughs scraping")
        self.logger.info(f"Configuration: {self.max_workers} workers, {len(self.mistral_api_keys)} API keys, batch size {self.batch_size}")
        
        # Get remaining combinations
        remaining_combinations = self.get_remaining_combinations()
        
        if not remaining_combinations:
            self.logger.info("No remaining combinations to process")
            return True
        
        self.logger.info(f"Processing {len(remaining_combinations)} combinations with batch processing:")
        for combo in remaining_combinations:
            self.logger.info(f"  - {combo['city']} from {combo['source']}")
        
        # Process combinations in parallel
        successful_workers = 0
        failed_workers = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_combo = {
                executor.submit(self.scrape_single_combination_batch, combo, i): combo 
                for i, combo in enumerate(remaining_combinations)
            }
            
            # Process completed tasks
            for future in as_completed(future_to_combo):
                combo = future_to_combo[future]
                try:
                    result = future.result()
                    if result is not None:
                        successful_workers += 1
                        combo_key = f"{combo['state']}_{combo['city']}_{combo['source']}"
                        self.save_progress(combo_key)
                        self.logger.info(f"✓ Completed: {combo['city']} from {combo['source']} ({len(result)} records)")
                    else:
                        failed_workers += 1
                        self.logger.error(f"✗ Failed: {combo['city']} from {combo['source']}")
                except Exception as e:
                    failed_workers += 1
                    self.logger.error(f"✗ Exception in {combo['city']} from {combo['source']}: {e}")
        
        self.logger.info(f"Batch parallel processing completed: {successful_workers} successful, {failed_workers} failed")
        return failed_workers == 0

    def save_final_results(self) -> str:
        """Save final consolidated results"""
        if not self.all_scraped_data:
            self.logger.warning("No data to save")
            return None

        try:
            # Create DataFrame
            df = pd.DataFrame(self.all_scraped_data)

            # Reorder columns for better readability
            column_order = [
                'state', 'city', 'source', 'title', 'name', 'age', 'phone',
                'description', 'social_media', 'email', 'website', 'posted_date',
                'post_id', 'url', 'search_url', 'scraped_at'
            ]

            # Reorder columns (only include existing columns)
            existing_columns = [col for col in column_order if col in df.columns]
            remaining_columns = [col for col in df.columns if col not in existing_columns]
            final_columns = existing_columns + remaining_columns
            df = df[final_columns]

            # Sort by state, city, source, then scraped_at
            df = df.sort_values(['state', 'city', 'source', 'scraped_at'])

            # Save to Excel
            output_file = "batch_parallel_nyc_final.xlsx"
            df.to_excel(output_file, index=False)

            self.logger.info(f"Final results saved to {output_file}: {len(df)} records")

            # Clean up progress file on successful completion
            try:
                if os.path.exists(self.progress_file):
                    os.remove(self.progress_file)
                    self.logger.info("Progress file cleaned up after successful completion")
            except Exception as e:
                self.logger.warning(f"Could not clean up progress file: {e}")

            return output_file

        except Exception as e:
            self.logger.error(f"Failed to save final results: {e}")
            return None

def main():
    """Main entry point for batch parallel NYC boroughs scraper"""
    parser = argparse.ArgumentParser(description='Batch Parallel NYC Boroughs Web Scraper with Multiple Mistral API Keys')
    parser.add_argument('--workers', type=int, default=5, help='Number of parallel workers (default: 5)')
    parser.add_argument('--batch-size', type=int, default=10, help='Number of pages per Mistral API batch (default: 10)')
    parser.add_argument('--mistral-keys', help='Comma-separated Mistral AI API keys (optional - defaults to built-in keys)')
    parser.add_argument('--clean-progress', action='store_true', help='Clean up progress files and start fresh')

    args = parser.parse_args()

    # Validate parameters
    if args.workers < 1 or args.workers > 10:
        print("Error: Number of workers must be between 1 and 10")
        return 1

    if args.batch_size < 1 or args.batch_size > 50:
        print("Error: Batch size must be between 1 and 50")
        return 1

    # Parse Mistral API keys
    mistral_keys = None
    if args.mistral_keys:
        mistral_keys = [key.strip() for key in args.mistral_keys.split(',')]
        print(f"Using {len(mistral_keys)} provided API keys")
    else:
        print("Using built-in API keys")

    # Clean progress files if requested
    if args.clean_progress:
        progress_file = "batch_parallel_nyc_progress.json"
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print(f"Cleaned up progress file: {progress_file}")

        # Also clean checkpoint files
        import glob
        checkpoint_pattern = "batch_parallel_*_checkpoint.xlsx"
        checkpoint_files = glob.glob(checkpoint_pattern)
        for cf in checkpoint_files:
            os.remove(cf)
            print(f"Cleaned up checkpoint file: {cf}")

        print("Progress files cleaned. Starting fresh.")

    # Create and run batch parallel scraper
    scraper = BatchParallelNYCScraper(
        mistral_api_keys=mistral_keys,
        max_workers=args.workers,
        batch_size=args.batch_size
    )

    print(f"Starting batch parallel NYC boroughs scraping...")
    print(f"Configuration: {args.workers} workers, {len(scraper.mistral_api_keys)} API keys, batch size {args.batch_size}")
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Features: Age ≤30, Women only, Batch Mistral AI processing, Min 25 pages per combination")
    print(f"Expected improvement: {args.batch_size}x fewer API calls, much faster processing")
    print()

    start_time = time.time()

    success = scraper.run_batch_parallel_scraping()

    end_time = time.time()
    processing_time = end_time - start_time

    if success:
        output_file = scraper.save_final_results()

        print(f"\n✓ Batch parallel scraping completed successfully!")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Total records collected: {len(scraper.all_scraped_data)}")

        if output_file:
            print(f"Final results saved to: {output_file}")

        # Show summary by source
        aaok_count = len([r for r in scraper.all_scraped_data if r.get('source') == 'aaok'])
        aypapi_count = len([r for r in scraper.all_scraped_data if r.get('source') == 'aypapi'])
        print(f"  - aaok.com: {aaok_count} records")
        print(f"  - aypapi.com: {aypapi_count} records")

        # Show summary by borough
        boroughs = list(set(r.get('city') for r in scraper.all_scraped_data))
        print(f"NYC Boroughs processed: {len(boroughs)}")
        for borough in sorted(boroughs):
            borough_count = len([r for r in scraper.all_scraped_data if r.get('city') == borough])
            print(f"  - {borough}: {borough_count} records")

        # Show batch processing efficiency
        total_pages = sum(1 for r in scraper.all_scraped_data)
        estimated_api_calls = (total_pages + args.batch_size - 1) // args.batch_size
        print(f"\nBatch Processing Efficiency:")
        print(f"  - Total pages processed: {total_pages}")
        print(f"  - Estimated API calls: {estimated_api_calls} (vs {total_pages} individual calls)")
        print(f"  - API call reduction: {((total_pages - estimated_api_calls) / total_pages * 100):.1f}%")

        return 0
    else:
        print(f"\n✗ Batch parallel scraping completed with some failures")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Partial results collected: {len(scraper.all_scraped_data)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
