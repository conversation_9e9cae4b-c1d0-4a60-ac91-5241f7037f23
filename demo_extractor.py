#!/usr/bin/env python3
"""
Demo script showing how to use the comprehensive data extractor

This script demonstrates different ways to use the data extractor:
1. Process a single URL
2. Process a small batch of URLs
3. Process a JSON file
4. Show different output formats

Usage:
    python demo_extractor.py
"""

import json
import os
from datetime import datetime
from comprehensive_data_extractor import ProfileDataExtractor, load_urls_from_json

def demo_single_url():
    """Demo: Extract data from a single URL"""
    print("=== Demo 1: Single URL Extraction ===")

    # Initialize extractor
    extractor = ProfileDataExtractor(delay=1.0, max_workers=1)

    # Example URL (you can replace with any URL from your JSON files)
    test_url = "https://aaok.com.listcrawler.eu/post/escorts/usa/maryland/baltimore/191781472"

    print(f"Extracting data from: {test_url}")

    # Extract data
    result = extractor.extract_profile_data(test_url, city="Baltimore", state="Maryland")

    # Display results
    print(f"\nResults:")
    print(f"  Status: {result['status']}")
    print(f"  Phone Number: {result['phone_number']}")
    print(f"  Name: {result['name']}")
    print(f"  Age: {result['age']}")
    print(f"  Location: {result['location']}")
    print(f"  City: {result['city']}")
    print(f"  State: {result['state']}")
    print(f"  Social Media: {result['social_media']}")

    # Show first 200 characters of raw text
    raw_text = result['raw_text']
    if raw_text:
        print(f"  Raw Text (first 200 chars): {raw_text[:200]}...")
    else:
        print(f"  Raw Text: None")

    if result['error']:
        print(f"  Error: {result['error']}")

    print("-" * 60)
    return result

def demo_batch_urls():
    """Demo: Extract data from a batch of URLs"""
    print("\n=== Demo 2: Batch URL Extraction ===")

    # Sample URLs - replace these with URLs from your JSON files
    sample_urls = [
        {
            "url": "https://aaok.com.listcrawler.eu/post/escorts/usa/maryland/baltimore/191781472",
            "city": "Baltimore",
            "state": "Maryland",
            "estimated_age": 27,
            "source": "aaok"
        },
        {
            "url": "https://aaok.com.listcrawler.eu/post/escorts/usa/maryland/baltimore/191778287",
            "city": "Baltimore",
            "state": "Maryland",
            "estimated_age": 30,
            "source": "aaok"
        }
    ]

    print(f"Processing {len(sample_urls)} URLs...")

    # Initialize extractor with multiple workers for faster processing
    extractor = ProfileDataExtractor(delay=0.5, max_workers=2)

    # Process URLs
    results = extractor.process_urls(sample_urls)

    # Show summary
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] == 'failed']

    print(f"\nBatch Results Summary:")
    print(f"  Total processed: {len(results)}")
    print(f"  Successful: {len(successful)}")
    print(f"  Failed: {len(failed)}")

    # Show details for successful extractions
    if successful:
        print(f"\nSuccessful Extractions:")
        for i, result in enumerate(successful, 1):
            print(f"  {i}. Phone: {result.get('phone_number', 'N/A')}")
            print(f"     Name: {result.get('name', 'N/A')}")
            print(f"     Age: {result.get('age', 'N/A')}")
            print(f"     Location: {result.get('location', 'N/A')}")
            if result.get('social_media'):
                print(f"     Social: {result['social_media']}")

    # Save results to Excel
    if results:
        output_file = f"demo_batch_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        extractor.save_to_excel(results, output_file)
        print(f"\nResults saved to: {output_file}")

    print("-" * 60)
    return results

def demo_json_file():
    """Demo: Process a JSON file"""
    print("\n=== Demo 3: JSON File Processing ===")

    # Find available JSON files
    json_files = [f for f in os.listdir('.') if f.endswith('.json') and any(keyword in f.lower() for keyword in ['urls', 'fresh', 'batch', 'phase'])]

    if not json_files:
        print("No JSON files found with URL data.")
        print("Expected files with names containing: 'urls', 'fresh', 'batch', or 'phase'")
        return None

    # Use the first JSON file found
    json_file = json_files[0]
    print(f"Processing JSON file: {json_file}")

    try:
        # Load URLs from JSON
        urls_data = load_urls_from_json(json_file)

        if not urls_data:
            print("No URLs found in the JSON file")
            return None

        print(f"Loaded {len(urls_data)} URLs from {json_file}")

        # Limit to first 5 URLs for demo
        limited_urls = urls_data[:5]
        print(f"Processing first {len(limited_urls)} URLs for demo...")

        # Initialize extractor
        extractor = ProfileDataExtractor(delay=0.8, max_workers=3)

        # Process URLs
        results = extractor.process_urls(limited_urls)

        # Show results
        successful = [r for r in results if r['status'] == 'success']
        failed = [r for r in results if r['status'] == 'failed']

        print(f"\nJSON Processing Results:")
        print(f"  Total processed: {len(results)}")
        print(f"  Successful: {len(successful)}")
        print(f"  Failed: {len(failed)}")

        if successful:
            print(f"\nSample Successful Extractions:")
            for i, result in enumerate(successful[:3], 1):
                print(f"  {i}. URL: {result['url']}")
                print(f"     Phone: {result.get('phone_number', 'N/A')}")
                print(f"     Name: {result.get('name', 'N/A')}")
                print(f"     Age: {result.get('age', 'N/A')}")
                print(f"     City: {result.get('city', 'N/A')}")
                print(f"     Location: {result.get('location', 'N/A')}")
                print("")

        # Save results
        output_file = f"demo_json_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        extractor.save_to_excel(results, output_file)
        print(f"Results saved to: {output_file}")

        return results

    except Exception as e:
        print(f"Error processing JSON file: {e}")
        return None

def demo_field_examples():
    """Demo: Show examples of the extracted fields"""
    print("\n=== Demo 4: Field Examples ===")
    print("The extractor looks for these data fields in each profile:")
    print("")

    fields_info = {
        "phone_number": {
            "description": "Phone number in various formats",
            "examples": ["************", "************", "(*************", "5551234567"],
            "sources": ["<a href='tel:...'> links", "viewposttelephone div", "text patterns"]
        },
        "name": {
            "description": "Person's name or working name",
            "examples": ["Sarah", "Jessica", "Nym: Maria"],
            "sources": ["Nym: fields", "viewpostname div", "post titles"]
        },
        "age": {
            "description": "Age in years",
            "examples": [28, 25, 30],
            "sources": ["Age: fields", "postTitleAge spans", "profile sections"]
        },
        "location": {
            "description": "Specific location/area",
            "examples": ["Columbia", "Downtown", "North Baltimore"],
            "sources": ["Location: fields", "profile location sections"]
        },
        "city": {
            "description": "City (from JSON metadata)",
            "examples": ["Baltimore", "Philadelphia", "New York"],
            "sources": ["JSON file metadata"]
        },
        "state": {
            "description": "State (from JSON metadata)",
            "examples": ["Maryland", "Pennsylvania", "New York"],
            "sources": ["JSON file metadata"]
        },
        "social_media": {
            "description": "Social media accounts",
            "examples": ["Instagram: user123", "OnlyFans: model456", "Instagram: abc; Twitter: xyz"],
            "sources": ["Text patterns in descriptions"]
        },
        "raw_text": {
            "description": "Full profile description text",
            "examples": ["My name is Sarah, I am a white and Latina..."],
            "sources": ["viewpostbody div", "main content areas"]
        }
    }

    for field, info in fields_info.items():
        print(f"{field.upper()}:")
        print(f"  Description: {info['description']}")
        print(f"  Examples: {', '.join(map(str, info['examples']))}")
        print(f"  Sources: {', '.join(info['sources'])}")
        print("")

    print("-" * 60)

def main():
    """Run all demos"""
    print("=== COMPREHENSIVE DATA EXTRACTOR DEMO ===")
    print("This demo shows how to extract data from escort profile URLs")
    print("Fields extracted: Phone Number, Name, Location, City, Social Media, Age, Raw Text")
    print("")

    try:
        # Demo 1: Single URL
        demo_single_url()

        # Demo 2: Batch processing
        demo_batch_urls()

        # Demo 3: JSON file processing
        demo_json_file()

        # Demo 4: Field examples
        demo_field_examples()

        print("\n=== DEMO COMPLETE ===")
        print("\nHow to use the extractor:")
        print("1. For single files: python run_extractor.py --file your_urls.json")
        print("2. For multiple files: python run_extractor.py --pattern 'fresh_urls_*.json'")
        print("3. For all files: python run_extractor.py --all")
        print("4. Interactive mode: python run_extractor.py")
        print("")
        print("Command line options:")
        print("  --delay 2.0        # 2 second delay between requests")
        print("  --workers 3        # Use 3 worker threads")
        print("  --limit 100        # Process only first 100 URLs")
        print("  --output file.xlsx # Custom output filename")

    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"\nDemo error: {e}")

if __name__ == '__main__':
    main()
