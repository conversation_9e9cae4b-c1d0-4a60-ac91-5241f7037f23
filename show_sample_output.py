#!/usr/bin/env python3
"""
Show sample of what the final saved data format looks like
"""

import pandas as pd
from datetime import datetime

def create_sample_output():
    """Create a sample of what the final Excel output will look like"""
    
    # Sample data that represents what Mistral extracts from the fixed markdown
    sample_data = [
        {
            'state': 'New York',
            'city': 'Manhattan',
            'source': 'aaok',
            'title': 'Adventurous Spirit Looking for Fun - 25',
            'name': 'Ciara',
            'age': '25',
            'phone': '3475665137',
            'description': 'Curvy in all the right places, I am a fun-loving adventurous spirit who loves to explore new experiences. Available for outcalls and incalls. Serious inquiries only.',
            'social_media': 'Instagram: @ciara_nyc, Snapchat: ciara_fun',
            'email': None,
            'website': None,
            'posted_date': None,
            'post_id': '191403342',
            'url': 'https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/manhattanny/191403342',
            'search_url': 'https://aaok.com.listcrawler.eu/brief/escorts/usa/newyork/manhattanny/1',
            'scraped_at': '2025-08-11T06:45:00.000000',
            'worker_id': 0
        },
        {
            'state': 'New York',
            'city': 'Brooklyn',
            'source': 'aypapi',
            'title': 'Sweet & Sexy Latina - 22',
            'name': 'Maria',
            'age': '22',
            'phone': '9175551234',
            'description': 'Young, beautiful Latina available for companionship. Very discreet and professional. Text or call for rates and availability.',
            'social_media': 'OnlyFans: @maria_sweet22, ig: maria_bk',
            'email': '<EMAIL>',
            'website': None,
            'posted_date': '2025-08-10',
            'post_id': '191405678',
            'url': 'https://aypapi.com.listcrawler.eu/post/escorts/usa/newyork/brooklyn/191405678',
            'search_url': 'https://aypapi.com.listcrawler.eu/brief/escorts/usa/newyork/brooklyn/1',
            'scraped_at': '2025-08-11T06:47:00.000000',
            'worker_id': 2
        },
        {
            'state': 'New York',
            'city': 'Bronx',
            'source': 'aaok',
            'title': 'Blonde Beauty Available Now - 28',
            'name': 'Ashley',
            'age': '28',
            'phone': '6465559876',
            'description': 'Stunning blonde with amazing personality. Available for dinner dates and companionship. Upscale gentlemen only.',
            'social_media': 'Twitter: @ashley_bronx, Snapchat: ash_beauty',
            'email': None,
            'website': 'www.ashley-companion.com',
            'posted_date': None,
            'post_id': '191407890',
            'url': 'https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/bronx/191407890',
            'search_url': 'https://aaok.com.listcrawler.eu/brief/escorts/usa/newyork/bronx/1',
            'scraped_at': '2025-08-11T06:48:00.000000',
            'worker_id': 4
        },
        {
            'state': 'New York',
            'city': 'Queens',
            'source': 'aypapi',
            'title': 'Exotic Asian Beauty - 24',
            'name': 'Lily',
            'age': '24',
            'phone': '7185552468',
            'description': 'Petite Asian beauty with silky smooth skin. Very friendly and accommodating. Available for incalls in safe, clean location.',
            'social_media': 'Instagram: @lily_queens24',
            'email': None,
            'website': None,
            'posted_date': '2025-08-11',
            'post_id': '191409123',
            'url': 'https://aypapi.com.listcrawler.eu/post/escorts/usa/newyork/queens/191409123',
            'search_url': 'https://aypapi.com.listcrawler.eu/brief/escorts/usa/newyork/queens/1',
            'scraped_at': '2025-08-11T06:49:00.000000',
            'worker_id': 1
        },
        {
            'state': 'New York',
            'city': 'Staten Island',
            'source': 'aaok',
            'title': 'Curvy Redhead Ready to Play - 26',
            'name': 'Jessica',
            'age': '26',
            'phone': '3475553691',
            'description': 'Curvy redhead with green eyes and freckles. Love to have fun and make new friends. Available evenings and weekends.',
            'social_media': 'Snapchat: jess_si26, OnlyFans: @jessica_red',
            'email': '<EMAIL>',
            'website': None,
            'posted_date': None,
            'post_id': '191410456',
            'url': 'https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/statenisland/191410456',
            'search_url': 'https://aaok.com.listcrawler.eu/brief/escorts/usa/newyork/statenisland/1',
            'scraped_at': '2025-08-11T06:50:00.000000',
            'worker_id': 3
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(sample_data)
    
    # Save to Excel
    output_file = "SAMPLE_final_output_format.xlsx"
    df.to_excel(output_file, index=False)
    
    print("Sample Final Output Format")
    print("=" * 50)
    print(f"Saved sample to: {output_file}")
    print(f"Total sample records: {len(sample_data)}")
    print()
    
    # Show column structure
    print("Column Structure:")
    print("-" * 30)
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    print()
    
    # Show data preview
    print("Data Preview:")
    print("-" * 30)
    for i, row in df.iterrows():
        print(f"\nRecord {i+1}:")
        print(f"  Name: {row['name']} (Age: {row['age']})")
        print(f"  City: {row['city']}, Source: {row['source']}")
        print(f"  Phone: {row['phone']}")
        print(f"  Social Media: {row['social_media']}")
        print(f"  Description: {row['description'][:80]}...")
        print(f"  URL: {row['url']}")
    
    print()
    
    # Show summary statistics
    print("Summary Statistics:")
    print("-" * 30)
    print(f"Cities covered: {df['city'].nunique()} ({', '.join(df['city'].unique())})")
    print(f"Sources: {df['source'].nunique()} ({', '.join(df['source'].unique())})")
    print(f"Age range: {df['age'].min()}-{df['age'].max()} years")
    print(f"Records with phone: {df['phone'].notna().sum()}/{len(df)}")
    print(f"Records with social media: {df['social_media'].notna().sum()}/{len(df)}")
    print(f"Records with email: {df['email'].notna().sum()}/{len(df)}")
    print(f"Records with website: {df['website'].notna().sum()}/{len(df)}")
    
    print()
    print("🎯 This is the format of your final dataset!")
    print("📊 Expected final size: 7,500-10,500 records")
    print("✅ All records: Women ≤30 with complete profile data")
    print("🔗 Includes: Social media, contact info, descriptions")

if __name__ == "__main__":
    create_sample_output()
