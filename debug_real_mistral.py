#!/usr/bin/env python3
"""
Debug script to check what <PERSON><PERSON><PERSON> is returning for real NYC data
"""

import json
import re
from mistralai import Mistra<PERSON>
from nyc_boroughs_scraper import NYCBoroughsScraper
import html2text

def test_real_mistral_response():
    """Test what Mistra<PERSON> returns for real NYC profile data"""
    
    # Use your API key and scraper
    api_key = "TPQI07nNKVpAeH01G2NiTrjUuY9xKk1G"
    client = Mistral(api_key=api_key)
    scraper = NYCBoroughsScraper(api_key)
    
    # Setup markdown converter
    markdown_converter = html2text.HTML2Text()
    markdown_converter.ignore_links = False
    markdown_converter.ignore_images = True
    markdown_converter.ignore_emphasis = False
    markdown_converter.body_width = 0
    
    # Test with a confirmed working URL
    test_url = "https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/manhattanny/191403342"
    
    print("Testing Real NYC Profile Data")
    print("=" * 50)
    print(f"Test URL: {test_url}")
    
    # Get real HTML
    print("\n1. Downloading HTML...")
    html = scraper.execute_curl_request(test_url, scraper.dedicated_curl_template)
    
    if not html:
        print("❌ Failed to get HTML")
        return
    
    print(f"✓ Downloaded {len(html)} characters of HTML")
    
    # Convert to markdown
    print("\n2. Converting to markdown...")
    try:
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(html, 'html.parser')
        
        # Remove unnecessary elements
        for element in soup(['script', 'style', 'nav', 'footer', 'header', 'aside', 
                           'noscript', 'iframe', 'form', 'input', 'button']):
            element.decompose()
        
        # Convert to markdown
        markdown_content = markdown_converter.handle(str(soup))
        
        # Clean up
        lines = markdown_content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if (line and 
                not line.startswith('*') and 
                len(line) > 3 and
                not re.match(r'^[\s\-_=]+$', line)):
                cleaned_lines.append(line)
        
        cleaned_content = '\n'.join(cleaned_lines)
        if len(cleaned_content) > 2000:
            cleaned_content = cleaned_content[:2000] + "\n[Content truncated...]"
        
        structured_markdown = f"""# Profile
**URL:** {test_url}

## Profile Content:
{cleaned_content}

---
"""
        
        print(f"✓ Converted to {len(structured_markdown)} characters of markdown")
        print("\nMarkdown Preview (first 500 chars):")
        print("-" * 30)
        print(structured_markdown[:500])
        print("-" * 30)
        
    except Exception as e:
        print(f"❌ Markdown conversion failed: {e}")
        return
    
    # Test with Mistral
    print("\n3. Testing with Mistral...")
    
    prompt = f"""
    You are extracting data from 1 escort profile page in clean markdown format. For each page, extract the following information:

    1. title: Profile title or headline
    2. name: Person's name
    3. age: Age (must be ≤30, skip if >30)
    4. phone: Phone number
    5. description: Profile description
    6. social_media: Social media handles/links (Instagram, Twitter, Snapchat, OnlyFans, etc.)
    7. email: Email address
    8. website: Website links
    9. posted_date: When the post was created
    10. post_id: Unique post identifier

    IMPORTANT: Only include profiles where age ≤30 AND gender is woman. Skip any profiles with age >30 or not women.

    Pages to process (in clean markdown format):

    --- PAGE 0 ---
    {structured_markdown}

    
    Return a JSON array with one object per valid page (only for women with age ≤30). Each object should have:
    {{
        "page_index": <index>,
        "url": "<url>",
        "title": "<title>",
        "name": "<name>", 
        "age": "<age>",
        "phone": "<phone>",
        "description": "<description>",
        "social_media": "<social_media>",
        "email": "<email>",
        "website": "<website>",
        "posted_date": "<posted_date>",
        "post_id": "<post_id>"
    }}
    
    If any field is not found, use null. Only return valid JSON array.
    """
    
    try:
        response = client.chat.complete(
            model="mistral-large-latest",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=2000
        )
        
        result_text = response.choices[0].message.content.strip()
        
        print("=" * 60)
        print("RAW MISTRAL RESPONSE:")
        print("=" * 60)
        print(result_text)
        print("=" * 60)
        
        # Try to parse as JSON
        try:
            parsed_json = json.loads(result_text)
            print("✓ JSON PARSING SUCCESSFUL!")
            print("Parsed result:")
            print(json.dumps(parsed_json, indent=2))
            
            if isinstance(parsed_json, list):
                print(f"\n📊 Found {len(parsed_json)} profiles")
                if len(parsed_json) == 0:
                    print("❌ EMPTY ARRAY - This is the problem!")
                else:
                    print("✓ Profiles found - system should be working")
            else:
                print("Result is not a list")
                
        except json.JSONDecodeError as e:
            print("✗ JSON PARSING FAILED!")
            print(f"Error: {e}")
            
            # Try fallback parsing
            json_match = re.search(r'\[.*\]', result_text, re.DOTALL)
            if json_match:
                try:
                    fallback_result = json.loads(json_match.group())
                    print("✓ FALLBACK PARSING SUCCESSFUL!")
                    print("Fallback result:")
                    print(json.dumps(fallback_result, indent=2))
                    
                    if isinstance(fallback_result, list):
                        print(f"\n📊 Found {len(fallback_result)} profiles via fallback")
                        if len(fallback_result) == 0:
                            print("❌ EMPTY ARRAY - This is the problem!")
                        else:
                            print("✓ Profiles found - system should be working")
                    
                except json.JSONDecodeError as e2:
                    print("✗ FALLBACK PARSING ALSO FAILED!")
                    print(f"Fallback error: {e2}")
            else:
                print("✗ NO JSON ARRAY FOUND IN RESPONSE!")
        
    except Exception as e:
        print(f"❌ API call failed: {e}")

if __name__ == "__main__":
    test_real_mistral_response()
