#!/usr/bin/env python3
"""
ScraperAPI Only Scraper: Complete scraper using ScraperAPI for everything
Works from scratch for any state/city with phone deduplication
Uses only ScraperAPI to bypass Cloudflare and other protections
"""

import sys
import os
import json
import time
import argparse
import re
import threading
from typing import List, Dict, Optional, Set
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from bs4 import BeautifulSoup
import pandas as pd


class ScraperAPIOnlyScraper:
    def __init__(self, scraperapi_key: str = "********************************",
                 mistral_api_key: str = "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"):
        """Initialize ScraperAPI-only scraper"""
        self.scraperapi_key = scraperapi_key
        self.mistral_api_key = mistral_api_key

        # ScraperAPI settings
        self.scraperapi_url = "https://api.scraperapi.com/"
        self.current_requests = 0
        self.max_requests_per_key = 4500

        # File naming will be set based on city/state
        self.urls_file = None
        self.phone_db_file = None
        self.results_file = None

        # Phone deduplication tracking
        self.known_phones: Set[str] = set()
        self.phone_to_url: Dict[str, str] = {}
        self.url_to_phone: Dict[str, str] = {}
        self.phone_lock = threading.Lock()

        # Rate limiting
        self.request_delay = 0.3  # Faster since ScraperAPI handles rate limiting
        self.last_request_time = {}

        # Scraping parameters
        self.min_pages_to_scrape = 15  # Reduced since we're using API
        self.max_consecutive_empty_pages = 3  # Faster detection
        self.max_pages_per_city = 50  # Reasonable limit
        self.phone_extraction_batch_size = 10
        self.phone_extraction_workers = 3

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('scraperapi_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_filenames(self, state_name: str, city_name: str):
        """Setup filenames based on state and city"""
        safe_state = state_name.lower().replace(' ', '_').replace('-', '_')
        safe_city = city_name.lower().replace(' ', '_').replace('-', '_')

        self.urls_file = f"urls_{safe_state}_{safe_city}.json"
        self.phone_db_file = f"phones_{safe_state}_{safe_city}.json"
        self.results_file = f"results_{safe_state}_{safe_city}.xlsx"

    def get_city_urls(self, state_name: str, city_name: str) -> List[Dict[str, str]]:
        """Get city URL combinations from the web scraper infrastructure"""
        try:
            # Import from existing infrastructure
            from web_scraper import WebScraper
            temp_scraper = WebScraper()
            all_cities = temp_scraper.parse_url_list()

            # Filter for our specific city and state
            city_combinations = []
            for city_data in all_cities:
                if (city_data['city'].lower() == city_name.lower() and
                    city_data['state'].lower() == state_name.lower()):
                    city_combinations.append(city_data)

            return city_combinations

        except Exception as e:
            self.logger.error(f"Failed to get city URLs: {e}")
            return []

    def fetch_with_scraperapi(self, url: str, render_js: bool = False) -> Optional[str]:
        """Fetch URL content using ScraperAPI"""
        try:
            params = {
                'api_key': self.scraperapi_key,
                'url': url,
                'render': 'true' if render_js else 'false',
                'country_code': 'us',
                'device_type': 'desktop'
            }

            response = requests.get(self.scraperapi_url, params=params, timeout=60)
            self.current_requests += 1

            if response.status_code == 200:
                return response.text
            else:
                self.logger.warning(f"ScraperAPI failed for {url}: Status {response.status_code}")
                if response.status_code == 422:
                    self.logger.warning("API quota exceeded or blocked URL")
                return None

        except Exception as e:
            self.logger.error(f"Error fetching {url}: {e}")
            return None

    def extract_dedicated_urls(self, search_html: str) -> List[str]:
        """Extract dedicated page URLs from search page HTML (age ≤30 only)"""
        if not search_html:
            return []

        try:
            soup = BeautifulSoup(search_html, 'html.parser')
            dedicated_urls = []

            # Look for different patterns that might contain profile links
            # Pattern 1: Direct links in anchor tags
            profile_links = soup.find_all('a', href=True)

            for link in profile_links:
                href = link['href']
                if 'post/escorts' in href:
                    try:
                        # Try to find age information near the link
                        age_found = False
                        age_value = None

                        # Look in the link text
                        link_text = link.get_text()
                        age_match = re.search(r'Age[:\s]*(\d+)', link_text, re.IGNORECASE)
                        if age_match:
                            age_value = int(age_match.group(1))
                            age_found = True

                        # Look in parent elements
                        if not age_found:
                            parent = link.parent
                            for _ in range(3):  # Check up to 3 parent levels
                                if parent:
                                    parent_text = parent.get_text()
                                    age_match = re.search(r'Age[:\s]*(\d+)', parent_text, re.IGNORECASE)
                                    if age_match:
                                        age_value = int(age_match.group(1))
                                        age_found = True
                                        break
                                    parent = parent.parent

                        # Look in sibling elements
                        if not age_found and link.parent:
                            siblings = link.parent.find_all(string=True)
                            for sibling in siblings:
                                age_match = re.search(r'Age[:\s]*(\d+)', str(sibling), re.IGNORECASE)
                                if age_match:
                                    age_value = int(age_match.group(1))
                                    age_found = True
                                    break

                        # If age found and ≤30, include the URL
                        if age_found and age_value and age_value <= 30:
                            full_url = href
                            if full_url.startswith('/'):
                                # Handle relative URLs
                                if 'aaok.com' in search_html or 'aaok' in search_html:
                                    full_url = 'https://aaok.com.listcrawler.eu' + full_url
                                elif 'aypapi.com' in search_html or 'aypapi' in search_html:
                                    full_url = 'https://aypapi.com.listcrawler.eu' + full_url

                            if full_url not in dedicated_urls:
                                dedicated_urls.append(full_url)

                        # If no age found, include anyway (we'll filter later)
                        elif not age_found:
                            full_url = href
                            if full_url.startswith('/'):
                                if 'aaok.com' in search_html or 'aaok' in search_html:
                                    full_url = 'https://aaok.com.listcrawler.eu' + full_url
                                elif 'aypapi.com' in search_html or 'aypapi' in search_html:
                                    full_url = 'https://aypapi.com.listcrawler.eu' + full_url

                            if full_url not in dedicated_urls:
                                dedicated_urls.append(full_url)

                    except Exception as e:
                        continue

            return dedicated_urls

        except Exception as e:
            self.logger.error(f"Error extracting dedicated URLs: {e}")
            return []

    def extract_urls_from_city(self, city_data: Dict[str, str]) -> List[str]:
        """Extract all URLs from all search pages for a city using ScraperAPI"""
        city_name = city_data['city']
        source = city_data['source']
        base_url = city_data['url']

        self.logger.info(f"📥 Extracting URLs from {city_name} ({source}) via ScraperAPI")

        all_urls = []
        page_num = 1
        empty_page_count = 0

        while True:
            # Construct page URL
            page_url = re.sub(r'/\d+$', f'/{page_num}', base_url)
            self.logger.info(f"   Processing page {page_num}: {page_url}")

            # Get search page HTML via ScraperAPI
            search_html = self.fetch_with_scraperapi(page_url, render_js=False)
            if not search_html:
                self.logger.warning(f"   Failed to get page {page_num}")
                empty_page_count += 1
            else:
                # Extract dedicated page URLs
                page_urls = self.extract_dedicated_urls(search_html)
                if not page_urls:
                    empty_page_count += 1
                    self.logger.info(f"   No URLs found on page {page_num} (empty page {empty_page_count}/{self.max_consecutive_empty_pages})")
                else:
                    empty_page_count = 0
                    all_urls.extend(page_urls)
                    self.logger.info(f"   Found {len(page_urls)} URLs on page {page_num}")

            # Check stopping conditions
            if page_num > self.min_pages_to_scrape and empty_page_count >= self.max_consecutive_empty_pages:
                self.logger.info(f"   Stopping after {self.max_consecutive_empty_pages} consecutive empty pages")
                break
            elif page_num <= self.min_pages_to_scrape:
                self.logger.info(f"   Continuing - need minimum {self.min_pages_to_scrape} pages (currently at {page_num})")

            time.sleep(self.request_delay)
            page_num += 1

            if page_num > self.max_pages_per_city:
                self.logger.info(f"   Reached maximum page limit ({self.max_pages_per_city})")
                break

        # Remove duplicates
        unique_urls = list(dict.fromkeys(all_urls))
        self.logger.info(f"   ✅ {city_name} ({source}): {len(unique_urls)} unique URLs from {page_num-1} pages")

        return unique_urls

    def phase1_extract_urls(self, state_name: str, city_name: str) -> List[str]:
        """Phase 1: Extract all URLs from search pages using ScraperAPI"""
        self.logger.info("🔍 PHASE 1: EXTRACTING URLS FROM SEARCH PAGES (ScraperAPI)")
        self.logger.info("=" * 60)

        # Get city combinations
        city_combinations = self.get_city_urls(state_name, city_name)

        if not city_combinations:
            self.logger.error(f"❌ No URL combinations found for {city_name}, {state_name}")
            return []

        self.logger.info(f"Found {len(city_combinations)} source combinations for {city_name}")

        all_urls = []
        urls_data = {}

        for i, city_data in enumerate(city_combinations):
            source = city_data['source']
            self.logger.info(f"Processing {i+1}/{len(city_combinations)}: {source}")

            # Extract URLs from this source
            source_urls = self.extract_urls_from_city(city_data)
            all_urls.extend(source_urls)

            # Store structured data
            combo_key = f"{state_name}_{city_name}_{source}"
            urls_data[combo_key] = {
                'state': state_name,
                'city': city_name,
                'source': source,
                'base_url': city_data['url'],
                'total_urls': len(source_urls),
                'urls': source_urls,
                'extracted_at': datetime.now().isoformat()
            }

        # Remove duplicates across all sources
        unique_urls = list(dict.fromkeys(all_urls))

        # Save URLs data
        try:
            with open(self.urls_file, 'w') as f:
                json.dump(urls_data, f, indent=2)
            self.logger.info(f"💾 Saved URL data to {self.urls_file}")
        except Exception as e:
            self.logger.error(f"Failed to save URL data: {e}")

        self.logger.info("=" * 60)
        self.logger.info(f"✅ Phase 1 Complete: {len(unique_urls)} unique URLs extracted")
        self.logger.info(f"📊 API requests used: {self.current_requests}")

        return unique_urls

    def extract_phone_only(self, url: str) -> Optional[str]:
        """Extract only phone number from URL (lightweight)"""
        try:
            response = self.fetch_with_scraperapi(url, render_js=False)
            if not response:
                return None

            soup = BeautifulSoup(response, 'html.parser')

            # Extract phone from viewposttelephone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                if phone_clean.strip():
                    # Process phone number (clean and add country code)
                    clean_phone = re.sub(r'[^0-9]', '', str(phone_clean))
                    if clean_phone and not clean_phone.startswith('1'):
                        clean_phone = '1' + clean_phone
                    return clean_phone

            # Try alternative selectors
            phone_patterns = [
                r'(?:phone|tel|call)[:\s]*([0-9\-\(\)\s\.]{10,})',
                r'(\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b)',
                r'(\b\(\d{3}\)\s?\d{3}[-.\s]?\d{4}\b)'
            ]

            page_text = soup.get_text()
            for pattern in phone_patterns:
                matches = re.findall(pattern, page_text, re.IGNORECASE)
                for match in matches:
                    clean_phone = re.sub(r'[^0-9]', '', str(match))
                    if len(clean_phone) >= 10:
                        if not clean_phone.startswith('1') and len(clean_phone) == 10:
                            clean_phone = '1' + clean_phone
                        return clean_phone

            return None

        except Exception as e:
            self.logger.warning(f"Error extracting phone from {url}: {e}")
            return None

    def extract_phones_batch(self, urls: List[str]) -> Dict[str, str]:
        """Extract phone numbers from batch of URLs"""
        phone_results = {}

        with ThreadPoolExecutor(max_workers=self.phone_extraction_workers) as executor:
            future_to_url = {
                executor.submit(self.extract_phone_only, url): url
                for url in urls
            }

            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    phone = future.result()
                    if phone:
                        phone_results[url] = phone
                        print(f"📱 Found phone {phone} for {url[-20:]}")

                except Exception as e:
                    self.logger.warning(f"Error processing {url}: {e}")

        return phone_results

    def phase2_deduplicate_phones(self, urls: List[str]) -> Dict[str, List[str]]:
        """Phase 2: Extract phones and deduplicate URLs"""
        self.logger.info("📱 PHASE 2: PHONE NUMBER DEDUPLICATION (ScraperAPI)")
        self.logger.info("=" * 60)

        # Load existing phone database if it exists
        if os.path.exists(self.phone_db_file):
            try:
                with open(self.phone_db_file, 'r') as f:
                    phone_data = json.load(f)
                self.known_phones = set(phone_data.get('known_phones', []))
                self.phone_to_url = phone_data.get('phone_to_url', {})
                self.url_to_phone = phone_data.get('url_to_phone', {})
                self.logger.info(f"📱 Loaded {len(self.known_phones)} known phones from database")
            except Exception as e:
                self.logger.warning(f"Failed to load phone database: {e}")

        # Process URLs in batches
        new_phones = 0
        duplicate_phones = 0
        no_phone_count = 0

        total_batches = (len(urls) + self.phone_extraction_batch_size - 1) // self.phone_extraction_batch_size

        for i in range(0, len(urls), self.phone_extraction_batch_size):
            batch = urls[i:i + self.phone_extraction_batch_size]
            batch_num = (i // self.phone_extraction_batch_size) + 1

            self.logger.info(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch)} URLs)")

            # Extract phones from batch
            batch_phones = self.extract_phones_batch(batch)

            # Update database
            with self.phone_lock:
                for url, phone in batch_phones.items():
                    if phone not in self.known_phones:
                        self.known_phones.add(phone)
                        self.phone_to_url[phone] = url
                        self.url_to_phone[url] = phone
                        new_phones += 1
                    else:
                        self.url_to_phone[url] = phone
                        duplicate_phones += 1

                # Count no-phone URLs
                for url in batch:
                    if url not in batch_phones:
                        no_phone_count += 1

            # Save progress
            if batch_num % 2 == 0:
                self.save_phone_database()

            time.sleep(0.5)  # Rate limiting

        # Final save
        self.save_phone_database()

        # Create groups
        unique_urls = []
        duplicate_urls = []
        no_phone_urls = []

        for url in urls:
            if url in self.url_to_phone:
                phone = self.url_to_phone[url]
                if self.phone_to_url[phone] == url:
                    unique_urls.append(url)
                else:
                    duplicate_urls.append(url)
            else:
                no_phone_urls.append(url)

        # Summary
        self.logger.info("=" * 60)
        self.logger.info(f"📊 Phone Deduplication Summary:")
        self.logger.info(f"   • Total URLs: {len(urls)}")
        self.logger.info(f"   • New phones found: {new_phones}")
        self.logger.info(f"   • Duplicate phones: {duplicate_phones}")
        self.logger.info(f"   • No phone URLs: {no_phone_count}")
        self.logger.info(f"   • Unique URLs to scrape: {len(unique_urls)}")
        self.logger.info(f"   • Duplicate URLs skipped: {len(duplicate_urls)}")
        self.logger.info(f"   • Reduction: {len(duplicate_urls)}/{len(urls)} ({len(duplicate_urls)/len(urls)*100:.1f}%)")
        self.logger.info(f"📊 API requests used so far: {self.current_requests}")

        return {
            'unique_urls': unique_urls,
            'duplicate_urls': duplicate_urls,
            'no_phone_urls': no_phone_urls
        }

    def save_phone_database(self):
        """Save phone database to file"""
        try:
            phone_data = {
                'known_phones': list(self.known_phones),
                'phone_to_url': self.phone_to_url,
                'url_to_phone': self.url_to_phone,
                'last_updated': datetime.now().isoformat()
            }

            with open(self.phone_db_file, 'w') as f:
                json.dump(phone_data, f, indent=2)

        except Exception as e:
            self.logger.error(f"Failed to save phone database: {e}")

    def extract_full_data(self, url: str) -> Dict:
        """Extract full profile data from URL using ScraperAPI"""
        data = {
            'url': url,
            'name': '',
            'age': '',
            'phone': '',
            'location': '',
            'description': '',
            'website_type': '',
            'social': '',
            'extraction_success': False,
            'scraped_at': datetime.now().isoformat()
        }

        try:
            response = self.fetch_with_scraperapi(url, render_js=False)
            if not response:
                data['error'] = 'Failed to fetch'
                return data

            soup = BeautifulSoup(response, 'html.parser')

            # Determine website type
            if 'aaok.com' in url:
                data['website_type'] = 'aaok'
            elif 'aypapi.com' in url:
                data['website_type'] = 'aypapi'

            # Extract name
            name_selectors = ['.viewpostusername', '.postusername', 'h1', '.profile-name', '.ad-title']
            for selector in name_selectors:
                name_elem = soup.select_one(selector)
                if name_elem:
                    name_text = name_elem.get_text(strip=True)
                    if name_text and len(name_text) > 1:
                        data['name'] = name_text[:100]  # Limit length
                        break

            # Extract age
            age_selectors = ['.viewpostage', '.age', '.profile-age']
            for selector in age_selectors:
                age_elem = soup.select_one(selector)
                if age_elem:
                    age_text = age_elem.get_text(strip=True)
                    age_match = re.search(r'\b(\d{2})\b', age_text)
                    if age_match:
                        age_val = int(age_match.group(1))
                        if 18 <= age_val <= 50:  # Reasonable age range
                            data['age'] = str(age_val)
                            break

            # Extract phone (we might already have it from deduplication)
            if url in self.url_to_phone:
                data['phone'] = self.url_to_phone[url]
            else:
                phone_selectors = ['.viewposttelephone', '.phone', '.contact-phone']
                for selector in phone_selectors:
                    phone_elem = soup.select_one(selector)
                    if phone_elem:
                        phone_text = phone_elem.get_text(strip=True)
                        phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                        if phone_clean.strip():
                            clean_phone = re.sub(r'[^0-9]', '', str(phone_clean))
                            if len(clean_phone) >= 10:
                                if not clean_phone.startswith('1') and len(clean_phone) == 10:
                                    clean_phone = '1' + clean_phone
                                data['phone'] = clean_phone
                                break

            # Extract location
            location_selectors = ['.viewpostlocation', '.location', '.profile-location']
            for selector in location_selectors:
                location_elem = soup.select_one(selector)
                if location_elem:
                    location_text = location_elem.get_text(strip=True)
                    if location_text and len(location_text) > 1:
                        data['location'] = location_text[:200]
                        break

            # Extract description
            desc_selectors = ['.viewposttext', '.posttext', '.description', '.profile-description']
            for selector in desc_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    desc_text = desc_elem.get_text(strip=True)
                    if desc_text and len(desc_text) > 10:
                        data['description'] = desc_text[:1000]  # Limit length
                        break

            # Mark as successful if we got meaningful data
            if data['name'] or data['age'] or data['phone']:
                data['extraction_success'] = True

        except Exception as e:
            data['error'] = str(e)
            self.logger.error(f"Error extracting data from {url}: {e}")

        return data

    def phase3_scrape_profiles(self, urls_to_scrape: List[str], max_workers: int = 3) -> List[Dict]:
        """Phase 3: Full scraping of deduplicated URLs using ScraperAPI"""
        self.logger.info("🚀 PHASE 3: FULL PROFILE SCRAPING (ScraperAPI)")
        self.logger.info("=" * 60)

        if not urls_to_scrape:
            self.logger.warning("No URLs to scrape")
            return []

        self.logger.info(f"Scraping {len(urls_to_scrape)} URLs with {max_workers} workers")

        results = []
        completed = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_url = {
                executor.submit(self.extract_full_data, url): url
                for url in urls_to_scrape
            }

            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    result = future.result()
                    results.append(result)
                    completed += 1

                    if completed % 5 == 0:
                        self.logger.info(f"   Progress: {completed}/{len(urls_to_scrape)} completed")
                        self.logger.info(f"   API requests used: {self.current_requests}")

                except Exception as e:
                    self.logger.error(f"Error processing {url}: {e}")
                    results.append({
                        'url': url,
                        'error': str(e),
                        'extraction_success': False,
                        'scraped_at': datetime.now().isoformat()
                    })

        self.logger.info("=" * 60)
        self.logger.info(f"✅ Phase 3 Complete: {len(results)} profiles processed")
        self.logger.info(f"📊 Total API requests used: {self.current_requests}")

        return results

    def save_results(self, results: List[Dict]):
        """Save results to Excel file"""
        try:
            df = pd.DataFrame(results)

            # Create summary statistics
            successful = df[df['extraction_success'] == True]
            failed = df[df['extraction_success'] == False]

            summary_data = {
                'Metric': [
                    'Total URLs Processed',
                    'Successful Extractions',
                    'Failed Extractions',
                    'Success Rate (%)',
                    'URLs with Names',
                    'URLs with Ages',
                    'URLs with Phones',
                    'AAOK URLs',
                    'AYPAPI URLs',
                    'Total API Requests Used'
                ],
                'Value': [
                    len(df),
                    len(successful),
                    len(failed),
                    f"{(len(successful) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                    len(df[df['name'].notna() & (df['name'].str.strip() != '')]),
                    len(df[df['age'].notna() & (df['age'].str.strip() != '')]),
                    len(df[df['phone'].notna() & (df['phone'].str.strip() != '')]),
                    len(df[df['website_type'] == 'aaok']),
                    len(df[df['website_type'] == 'aypapi']),
                    self.current_requests
                ]
            }

            summary_df = pd.DataFrame(summary_data)

            # Save to Excel with multiple sheets
            with pd.ExcelWriter(self.results_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Results', index=False)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

                # Add deduplication info if available
                if os.path.exists(self.phone_db_file):
                    try:
                        with open(self.phone_db_file, 'r') as f:
                            phone_data = json.load(f)

                        dedup_summary = pd.DataFrame({
                            'Deduplication Info': [
                                'Total Known Phones',
                                'Phone-to-URL Mappings',
                                'URL-to-Phone Mappings',
                                'Database Last Updated'
                            ],
                            'Value': [
                                len(phone_data.get('known_phones', [])),
                                len(phone_data.get('phone_to_url', {})),
                                len(phone_data.get('url_to_phone', {})),
                                phone_data.get('last_updated', 'Unknown')
                            ]
                        })

                        dedup_summary.to_excel(writer, sheet_name='Deduplication', index=False)
                    except:
                        pass

            self.logger.info(f"💾 Results saved to {self.results_file}")
            self.logger.info(f"📊 Summary: {len(successful)}/{len(df)} successful ({len(successful)/len(df)*100:.1f}%)")

            return True

        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
            return False

    def scrape_city(self, state_name: str, city_name: str, include_no_phone: bool = True, max_workers: int = 3):
        """Complete scraping process for a new city using ScraperAPI"""
        self.logger.info(f"🎯 Starting ScraperAPI-only scraping for {city_name}, {state_name}")
        self.setup_filenames(state_name, city_name)

        start_time = time.time()

        try:
            # Phase 1: Extract URLs
            all_urls = self.phase1_extract_urls(
