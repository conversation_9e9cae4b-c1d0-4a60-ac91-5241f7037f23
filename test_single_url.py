#!/usr/bin/env python3
"""
Test single URL extraction to see the quality of results
"""

import json
import sys
from pure_manual_extraction_scraper import PureManualNYCScraper
from nyc_boroughs_scraper import NYCBoroughsScraper

def test_single_url():
    """Test extraction on a single URL"""
    
    # Load URLs data
    try:
        with open("parallel_nyc_all_urls_deduplicated.json", 'r') as f:
            urls_data = json.load(f)
    except Exception as e:
        print(f"Error loading URLs file: {e}")
        return
    
    # Get the first URL from Manhattan aaok
    test_url = urls_data["Manhattan_aypapi"]["urls"][0]
    print(f"Testing URL: {test_url}")
    print("=" * 80)
    
    # Create scraper instances
    manual_scraper = PureManualNYCScraper(max_workers=1)
    nyc_scraper = NYCBoroughsScraper("dummy_key")  # We won't use Mistral
    
    # Download HTML
    print("1. Downloading HTML...")
    html = nyc_scraper.execute_curl_request(test_url, nyc_scraper.dedicated_curl_template)
    
    if not html:
        print("❌ Failed to download HTML")
        return
    
    print(f"✅ HTML downloaded successfully ({len(html)} characters)")
    print()
    
    # Extract data manually
    print("2. Extracting data manually...")
    extracted_data = manual_scraper.extract_all_data_manually(html, test_url)
    
    print("3. Extraction Results:")
    print("=" * 50)
    
    # Display results in a nice format
    for key, value in extracted_data.items():
        if key == 'raw_text':
            # Show only first 200 characters of raw text
            if value:
                print(f"{key:20}: {str(value)[:200]}...")
            else:
                print(f"{key:20}: {value}")
        else:
            print(f"{key:20}: {value}")
    
    print()
    print("4. Summary:")
    print("=" * 30)
    print(f"Extraction Status: {extracted_data['extraction_status']}")
    print(f"Is Female: {extracted_data['is_female']}")
    print(f"Has Name: {'Yes' if extracted_data['name'] else 'No'}")
    print(f"Has Age: {'Yes' if extracted_data['age'] else 'No'}")
    print(f"Has Phone: {'Yes' if extracted_data['phone'] else 'No'}")
    print(f"Has Description: {'Yes' if extracted_data['description'] else 'No'}")
    print(f"Has Social Media: {'Yes' if extracted_data['social_media'] else 'No'}")
    print(f"Has Address: {'Yes' if extracted_data['address'] else 'No'}")
    
    # Show a snippet of the raw HTML for debugging
    print()
    print("5. HTML Snippet (first 500 chars):")
    print("=" * 40)
    print(html[:500])
    print("...")

if __name__ == "__main__":
    test_single_url()
