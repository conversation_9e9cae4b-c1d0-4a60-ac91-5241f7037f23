#!/usr/bin/env python3
"""
Scraper Deduplication Patch
Integration guide and patch for adding phone number deduplication to existing scraperapi_scraper.py
"""

import pandas as pd
import re
import os
import threading
from typing import Set, Optional
from datetime import datetime

class DeduplicationPatch:
    """
    Patch to add phone number deduplication to existing ScraperAPIScraper class

    Usage:
    1. Add this patch to your existing scraper file
    2. Initialize deduplication in your scraper's __init__ method
    3. Add phone check before processing each URL
    4. Track scraped phone numbers
    """

    def __init__(self):
        self.scraped_phone_numbers: Set[str] = set()
        self.phone_lock = threading.Lock()
        self.skipped_count = 0

        # Load existing phone numbers from previous results
        self.load_existing_phone_numbers()

    def load_existing_phone_numbers(self):
        """Load phone numbers from existing result files to avoid duplicates"""
        print("🔍 Loading existing phone numbers from previous results...")

        # Look for existing Excel files with scraped results
        result_files = [f for f in os.listdir('.') if f.startswith('scraperapi_results_') and f.endswith('.xlsx')]

        total_loaded = 0
        for file_path in result_files:
            try:
                print(f"📂 Checking {file_path}...")
                df = pd.read_excel(file_path)

                if 'phone' in df.columns:
                    # Get valid phone numbers (not empty, not NaN)
                    valid_phones = df[df['phone'].notna() & (df['phone'] != '') & (df['phone'] != 'nan')]['phone']

                    # Clean phone numbers
                    for phone in valid_phones:
                        cleaned_phone = self.clean_phone_number(str(phone))
                        if cleaned_phone:
                            self.scraped_phone_numbers.add(cleaned_phone)
                            total_loaded += 1

            except Exception as e:
                print(f"   ⚠️ Error loading {file_path}: {e}")
                continue

        print(f"✅ Loaded {total_loaded} existing phone numbers from {len(result_files)} files")
        print(f"📊 Total unique phone numbers to skip: {len(self.scraped_phone_numbers)}")

    def clean_phone_number(self, phone_str: str) -> Optional[str]:
        """Clean phone number for consistent comparison"""
        if not phone_str or pd.isna(phone_str):
            return None

        # Convert to string and remove all non-digits
        phone_clean = re.sub(r'[^\d]', '', str(phone_str))

        # Must be at least 10 digits for US phone numbers
        if len(phone_clean) < 10:
            return None

        # Normalize to 10 digits (remove country code if present)
        if len(phone_clean) == 11 and phone_clean.startswith('1'):
            phone_clean = phone_clean[1:]
        elif len(phone_clean) > 11:
            return None  # Invalid length

        return phone_clean

    def is_phone_already_scraped(self, phone: str) -> bool:
        """Check if phone number has already been scraped"""
        cleaned_phone = self.clean_phone_number(phone)
        if not cleaned_phone:
            return False

        with self.phone_lock:
            return cleaned_phone in self.scraped_phone_numbers

    def add_phone_to_scraped(self, phone: str):
        """Add phone number to scraped set"""
        cleaned_phone = self.clean_phone_number(phone)
        if cleaned_phone:
            with self.phone_lock:
                self.scraped_phone_numbers.add(cleaned_phone)

    def quick_extract_phone_from_html(self, html: str) -> Optional[str]:
        """Quickly extract phone number from HTML for deduplication check"""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            phone_elem = soup.select_one('.viewposttelephone')

            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                return phone_clean.strip() if phone_clean.strip() else None

            return None
        except:
            return None


# INTEGRATION INSTRUCTIONS FOR EXISTING SCRAPERAPI_SCRAPER.PY:

"""
STEP 1: Add deduplication to your ScraperAPIScraper class __init__ method

Add this after your existing initialization code:

    def __init__(self, api_keys: List[str], max_workers: int = 3, enable_social_media: bool = True, mistral_api_key: str = None):
        # ... existing initialization code ...

        # ADD THIS: Phone number deduplication
        self.dedup_patch = DeduplicationPatch()
        self.skipped_count = 0


STEP 2: Modify your process_url_batch method

Replace your existing process_url_batch method with this enhanced version:

    def process_url_batch(self, urls: List[str], worker_id: str) -> List[Dict]:
        results = []
        print(f"Worker {worker_id}: Starting batch of {len(urls)} URLs")

        for i, url in enumerate(urls):
            print(f"\\nWorker {worker_id}: Processing {i+1}/{len(urls)}: {url}")

            # STEP 1: Quick phone check to avoid unnecessary scraping
            html_preview = self.fetch_with_scraperapi(url, worker_id)

            if html_preview:
                quick_phone = self.dedup_patch.quick_extract_phone_from_html(html_preview)

                if quick_phone and self.dedup_patch.is_phone_already_scraped(quick_phone):
                    print(f"Worker {worker_id}: 🚫 SKIPPED - Phone {self.dedup_patch.clean_phone_number(quick_phone)} already scraped")

                    # Add skipped entry
                    skipped_data = {
                        'url': url,
                        'extraction_success': False,
                        'skip_reason': 'phone_already_scraped',
                        'duplicate_phone': self.dedup_patch.clean_phone_number(quick_phone),
                        'scraped_at': datetime.now().isoformat()
                    }
                    results.append(skipped_data)

                    with self.results_lock:
                        self.skipped_count += 1

                    continue

                # STEP 2: Process normally since phone is new
                data = self.extract_data_from_html(html_preview, url)

                # Add phone to scraped set if extraction was successful
                if data['phone'] and data['extraction_success']:
                    self.dedup_patch.add_phone_to_scraped(data['phone'])

                results.append(data)

                if data['extraction_success']:
                    print(f"Worker {worker_id}: ✅ SUCCESS - New unique data")
                    print(f"    Name: '{data['name']}'")
                    print(f"    Age: {data['age']}")
                    print(f"    Phone: '{data['phone']}'")
                    print(f"    Type: {data['website_type']}")
                else:
                    print(f"Worker {worker_id}: ❌ No meaningful data extracted")
            else:
                # Add failed entry
                failed_data = {
                    'url': url,
                    'extraction_success': False,
                    'error': 'Failed to fetch page content via ScraperAPI',
                    'scraped_at': datetime.now().isoformat()
                }
                results.append(failed_data)
                print(f"Worker {worker_id}: ❌ Failed to fetch content")

            # Add results to main collection and save periodically
            with self.results_lock:
                self.results.append(data if html_preview else failed_data)
                self.processed_count += 1

                # Save intermediate results every 1000 URLs
                if self.processed_count % 1000 == 0:
                    self.save_results(self.results, f"checkpoint_{self.processed_count}")
                    print(f"\\n💾 CHECKPOINT: Saved {self.processed_count} URLs, Skipped {self.skipped_count} duplicates")

        successful = len([r for r in results if r.get('extraction_success', False) and not r.get('skip_reason')])
        skipped = len([r for r in results if r.get('skip_reason')])
        print(f"Worker {worker_id}: ✅ Batch completed: {successful} successful, {skipped} skipped duplicates")
        return results


STEP 3: Update your save_results method to include deduplication statistics

Add these lines to your statistics section in save_results method:

                stats_data = {
                    'Metric': [
                        # ... existing metrics ...
                        'Skipped Duplicates',
                        'Deduplication Rate (%)',
                        'Total Unique Phone Numbers',
                        # ... rest of existing metrics ...
                    ],
                    'Value': [
                        # ... existing values ...
                        self.skipped_count,
                        f"{(self.skipped_count / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                        len(self.dedup_patch.scraped_phone_numbers),
                        # ... rest of existing values ...
                    ]
                }


STEP 4: Update your main function to show deduplication statistics

Add this to your main function at the end:

    print(f"\\n🎉 SCRAPING WITH DEDUPLICATION COMPLETED!")
    print(f"📊 Deduplication Results:")
    print(f"   • Duplicates skipped: {scraper.skipped_count}")
    print(f"   • Unique phone numbers: {len(scraper.dedup_patch.scraped_phone_numbers)}")
    print(f"   • Deduplication efficiency: {(scraper.skipped_count / len(results) * 100):.1f}% API calls saved")

"""

# ALTERNATIVE: Quick patch for existing scraper
def patch_existing_scraper():
    """
    Quick patch function that can be imported and used with existing scraper
    """
    print("📋 DEDUPLICATION PATCH INSTRUCTIONS")
    print("=" * 50)
    print("1. Copy the DeduplicationPatch class to your scraper file")
    print("2. Add 'self.dedup_patch = DeduplicationPatch()' to your __init__ method")
    print("3. Follow the integration steps in the comments above")
    print("4. This will prevent scraping profiles with phone numbers you already have")
    print("5. Estimated API call savings: 30-70% depending on overlap")
    print("\n✅ Integration complete! Your scraper now avoids duplicate phone numbers.")

if __name__ == "__main__":
    patch_existing_scraper()
