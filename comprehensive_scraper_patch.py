#!/usr/bin/env python3
"""
Comprehensive scraper patch - captures ALL data including failed parsing
"""

import json
import re
import pandas as pd
from datetime import datetime
import threading

class ComprehensiveDataCapture:
    """Enhanced data capture that saves everything"""
    
    def __init__(self):
        self.all_data = []
        self.data_lock = threading.Lock()
        self.save_interval = 500  # Save every 500 records
        self.last_save_count = 0
    
    def process_mistral_response_comprehensive(self, raw_response: str, url: str, 
                                             city: str, source: str, worker_id: int) -> list:
        """Process Mistral response and capture ALL data"""
        
        results = []
        
        if not raw_response:
            # No Mistral response
            results.append({
                'status': 'FAILED_EXTRACTION',
                'url': url,
                'city': city,
                'source': source,
                'extraction_method': 'no_mistral_response',
                'name': None,
                'age': None,
                'gender': None,
                'phone': None,
                'description': None,
                'social_media': None,
                'email': None,
                'website': None,
                'post_id': self.extract_post_id(url),
                'raw_mistral_response': None,
                'parsing_error': 'No response from Mistral API',
                'worker_id': worker_id,
                'scraped_at': datetime.now().isoformat()
            })
            return results
        
        try:
            # Try direct JSON parsing
            parsed_data = json.loads(raw_response)
            
            if isinstance(parsed_data, list):
                for profile in parsed_data:
                    if isinstance(profile, dict):
                        # SUCCESS - Direct parsing
                        results.append({
                            'status': 'SUCCESS',
                            'url': url,
                            'city': city,
                            'source': source,
                            'extraction_method': 'direct_parsing',
                            'name': profile.get('name'),
                            'age': profile.get('age'),
                            'gender': profile.get('gender', 'woman'),  # Default assumption
                            'phone': profile.get('phone'),
                            'description': profile.get('description'),
                            'social_media': profile.get('social_media'),
                            'email': profile.get('email'),
                            'website': profile.get('website'),
                            'post_id': profile.get('post_id') or self.extract_post_id(url),
                            'raw_mistral_response': raw_response,
                            'parsing_error': None,
                            'worker_id': worker_id,
                            'scraped_at': datetime.now().isoformat()
                        })
            else:
                # Single object, convert to list
                if isinstance(parsed_data, dict):
                    results.append({
                        'status': 'SUCCESS',
                        'url': url,
                        'city': city,
                        'source': source,
                        'extraction_method': 'direct_parsing_single',
                        'name': parsed_data.get('name'),
                        'age': parsed_data.get('age'),
                        'gender': parsed_data.get('gender', 'woman'),
                        'phone': parsed_data.get('phone'),
                        'description': parsed_data.get('description'),
                        'social_media': parsed_data.get('social_media'),
                        'email': parsed_data.get('email'),
                        'website': parsed_data.get('website'),
                        'post_id': parsed_data.get('post_id') or self.extract_post_id(url),
                        'raw_mistral_response': raw_response,
                        'parsing_error': None,
                        'worker_id': worker_id,
                        'scraped_at': datetime.now().isoformat()
                    })
                    
        except json.JSONDecodeError as e:
            # Try fallback parsing
            json_match = re.search(r'\[.*\]', raw_response, re.DOTALL)
            if json_match:
                try:
                    parsed_data = json.loads(json_match.group())
                    
                    if isinstance(parsed_data, list):
                        for profile in parsed_data:
                            if isinstance(profile, dict):
                                # SUCCESS - Fallback parsing
                                results.append({
                                    'status': 'SUCCESS',
                                    'url': url,
                                    'city': city,
                                    'source': source,
                                    'extraction_method': 'fallback_parsing',
                                    'name': profile.get('name'),
                                    'age': profile.get('age'),
                                    'gender': profile.get('gender', 'woman'),
                                    'phone': profile.get('phone'),
                                    'description': profile.get('description'),
                                    'social_media': profile.get('social_media'),
                                    'email': profile.get('email'),
                                    'website': profile.get('website'),
                                    'post_id': profile.get('post_id') or self.extract_post_id(url),
                                    'raw_mistral_response': raw_response,
                                    'parsing_error': f"Initial parsing failed: {str(e)}",
                                    'worker_id': worker_id,
                                    'scraped_at': datetime.now().isoformat()
                                })
                    
                except json.JSONDecodeError as e2:
                    # FAILED PARSING - but save raw data for manual review
                    results.append({
                        'status': 'FAILED_PARSING',
                        'url': url,
                        'city': city,
                        'source': source,
                        'extraction_method': 'parsing_failed',
                        'name': None,
                        'age': None,
                        'gender': None,
                        'phone': None,
                        'description': None,
                        'social_media': None,
                        'email': None,
                        'website': None,
                        'post_id': self.extract_post_id(url),
                        'raw_mistral_response': raw_response,
                        'parsing_error': f"Direct: {str(e)}, Fallback: {str(e2)}",
                        'worker_id': worker_id,
                        'scraped_at': datetime.now().isoformat()
                    })
            else:
                # No JSON array found - save raw data
                results.append({
                    'status': 'FAILED_PARSING',
                    'url': url,
                    'city': city,
                    'source': source,
                    'extraction_method': 'no_json_found',
                    'name': None,
                    'age': None,
                    'gender': None,
                    'phone': None,
                    'description': None,
                    'social_media': None,
                    'email': None,
                    'website': None,
                    'post_id': self.extract_post_id(url),
                    'raw_mistral_response': raw_response,
                    'parsing_error': f"No JSON array found. Error: {str(e)}",
                    'worker_id': worker_id,
                    'scraped_at': datetime.now().isoformat()
                })
        
        return results
    
    def extract_post_id(self, url: str) -> str:
        """Extract post ID from URL"""
        match = re.search(r'/(\d+)$', url)
        return match.group(1) if match else 'unknown'
    
    def add_data(self, data_list: list):
        """Thread-safe data addition"""
        with self.data_lock:
            self.all_data.extend(data_list)
            
            # Auto-save check
            current_count = len(self.all_data)
            if current_count >= self.last_save_count + self.save_interval:
                self.save_intermediate_data()
                self.last_save_count = current_count
    
    def save_intermediate_data(self):
        """Save intermediate data with comprehensive format"""
        if not self.all_data:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        current_count = len(self.all_data)
        
        # Create DataFrame
        df = pd.DataFrame(self.all_data)
        
        # Save with multiple sheets
        output_file = f"comprehensive_nyc_data_{current_count}_records_{timestamp}.xlsx"
        
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # All data
                df.to_excel(writer, sheet_name='ALL_DATA', index=False)
                
                # Successful extractions
                success_df = df[df['status'] == 'SUCCESS']
                if not success_df.empty:
                    success_df.to_excel(writer, sheet_name='SUCCESS', index=False)
                
                # Failed parsing (for manual review)
                failed_parsing_df = df[df['status'] == 'FAILED_PARSING']
                if not failed_parsing_df.empty:
                    failed_parsing_df.to_excel(writer, sheet_name='FAILED_PARSING', index=False)
                
                # Failed extraction
                failed_extraction_df = df[df['status'] == 'FAILED_EXTRACTION']
                if not failed_extraction_df.empty:
                    failed_extraction_df.to_excel(writer, sheet_name='FAILED_EXTRACTION', index=False)
            
            # Show breakdown
            status_counts = df['status'].value_counts()
            print(f"\n📊 COMPREHENSIVE SAVE: {output_file}")
            print(f"Total records: {current_count}")
            for status, count in status_counts.items():
                print(f"  {status}: {count} ({count/current_count*100:.1f}%)")
            
            # Summary by source
            if 'source' in df.columns:
                source_counts = df['source'].value_counts()
                print(f"By source:")
                for source, count in source_counts.items():
                    print(f"  {source}: {count}")
            
        except Exception as e:
            print(f"❌ Failed to save comprehensive data: {e}")
    
    def save_final_data(self):
        """Save final comprehensive data"""
        if not self.all_data:
            print("No data to save")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        total_count = len(self.all_data)
        
        # Create DataFrame
        df = pd.DataFrame(self.all_data)
        
        # Save comprehensive final file
        final_file = f"FINAL_comprehensive_nyc_data_{total_count}_records_{timestamp}.xlsx"
        
        try:
            with pd.ExcelWriter(final_file, engine='openpyxl') as writer:
                # All data
                df.to_excel(writer, sheet_name='ALL_DATA', index=False)
                
                # Successful extractions
                success_df = df[df['status'] == 'SUCCESS']
                success_df.to_excel(writer, sheet_name='SUCCESS', index=False)
                
                # Failed parsing (for manual review)
                failed_parsing_df = df[df['status'] == 'FAILED_PARSING']
                failed_parsing_df.to_excel(writer, sheet_name='FAILED_PARSING', index=False)
                
                # Failed extraction
                failed_extraction_df = df[df['status'] == 'FAILED_EXTRACTION']
                failed_extraction_df.to_excel(writer, sheet_name='FAILED_EXTRACTION', index=False)
                
                # Summary sheet
                summary_data = []
                status_counts = df['status'].value_counts()
                for status, count in status_counts.items():
                    summary_data.append({
                        'Status': status,
                        'Count': count,
                        'Percentage': f"{count/total_count*100:.1f}%"
                    })
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='SUMMARY', index=False)
            
            print(f"\n🎉 FINAL COMPREHENSIVE SAVE: {final_file}")
            print(f"Total records: {total_count}")
            
            # Final breakdown
            status_counts = df['status'].value_counts()
            for status, count in status_counts.items():
                print(f"  {status}: {count} ({count/total_count*100:.1f}%)")
            
            print(f"\n💡 MANUAL REVIEW:")
            failed_parsing_count = len(failed_parsing_df)
            if failed_parsing_count > 0:
                print(f"  {failed_parsing_count} records in FAILED_PARSING sheet")
                print(f"  These contain raw Mistral responses for manual extraction")
                print(f"  Potential additional profiles: {failed_parsing_count}")
            
            return final_file
            
        except Exception as e:
            print(f"❌ Failed to save final comprehensive data: {e}")
            return None

# Example usage
def test_comprehensive_capture():
    """Test the comprehensive capture system"""
    
    capture = ComprehensiveDataCapture()
    
    # Test successful response
    success_response = '''[{"name": "Sarah", "age": "24", "phone": "555-1234", "description": "Available now"}]'''
    results = capture.process_mistral_response_comprehensive(
        success_response, 
        "https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/manhattan/191403342",
        "Manhattan", "aaok", 0
    )
    capture.add_data(results)
    
    # Test failed parsing response
    failed_response = '''Here is the data:
```json
[{"name": "Maria", "age": "22", "phone": "555-5678"}]
```
But with parsing issues'''
    results = capture.process_mistral_response_comprehensive(
        failed_response,
        "https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/brooklyn/191405678", 
        "Brooklyn", "aaok", 1
    )
    capture.add_data(results)
    
    # Save final data
    final_file = capture.save_final_data()
    print(f"\n📁 Test output: {final_file}")

if __name__ == "__main__":
    test_comprehensive_capture()
