#!/usr/bin/env python3
"""
Test ScrapingDog API to verify it's working correctly
"""

import requests
import json
import time

def test_scrapingdog_api(api_key, test_url="https://httpbin.org/ip"):
    """Test a single ScrapingDog API key"""
    try:
        print(f"Testing API Key: ...{api_key[-8:]}")
        print(f"Test URL: {test_url}")

        params = {
            'api_key': api_key,
            'url': test_url,
            'dynamic': 'false',
        }

        response = requests.get("https://api.scrapingdog.com/scrape", params=params, timeout=30)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✅ API Key WORKING!")
            try:
                # Try to parse as JSON if it's a JSON response
                data = response.json()
                print(f"Response: {json.dumps(data, indent=2)}")
            except:
                # If not JSON, show first 300 characters
                print(f"Response (first 300 chars): {response.text[:300]}...")
            return True
        else:
            print("❌ API Key FAILED!")
            print(f"Error response: {response.text[:500]}...")
            return False

    except Exception as e:
        print(f"❌ Exception testing API key: {e}")
        return False

def test_actual_scraping_target(api_key):
    """Test with an actual scraping target URL"""
    target_url = "https://aaok.com.listcrawler.eu/post/escorts/usa/newjersey/southjersey/191793963"

    print(f"\n🎯 Testing actual scraping target:")
    print(f"URL: {target_url}")

    try:
        params = {
            'api_key': api_key,
            'url': target_url,
            'dynamic': 'true',  # Use dynamic rendering for this type of content
        }

        response = requests.get("https://api.scrapingdog.com/scrape", params=params, timeout=60)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✅ Actual scraping SUCCESSFUL!")
            print(f"Response length: {len(response.text)} characters")

            # Look for some expected content
            if "listcrawler" in response.text.lower():
                print("✅ Content appears to be correctly scraped")
            else:
                print("⚠️ Content may not be fully scraped")

            print(f"First 500 chars: {response.text[:500]}...")
            return True
        else:
            print("❌ Actual scraping FAILED!")
            print(f"Error: {response.text[:300]}...")
            return False

    except Exception as e:
        print(f"❌ Exception during actual scraping: {e}")
        return False

def main():
    """Main test function"""

    # API keys to test
    api_keys = [
        "68a382be0525e5d984372175",
        # Add more API keys here if available
    ]

    print("🧪 Testing ScrapingDog API")
    print("=" * 50)

    working_keys = []
    failed_keys = []

    for i, api_key in enumerate(api_keys, 1):
        print(f"\n📋 Testing API Key {i}/{len(api_keys)}")
        print("-" * 30)

        # Test with simple URL first
        if test_scrapingdog_api(api_key):
            working_keys.append(api_key)
        else:
            failed_keys.append(api_key)

        # Small delay between tests
        time.sleep(2)

    print(f"\n🎯 BASIC TEST RESULTS")
    print("=" * 50)
    print(f"✅ Working API keys: {len(working_keys)}")
    print(f"❌ Failed API keys: {len(failed_keys)}")

    # Test with actual scraping target if we have working keys
    if working_keys:
        print(f"\n🎯 ADVANCED TEST - Actual Scraping Target")
        print("=" * 50)
        time.sleep(3)  # Wait a bit before advanced test
        test_actual_scraping_target(working_keys[0])
    else:
        print("\n❌ No working API keys found for advanced testing")

    print(f"\n✅ Testing completed!")

if __name__ == "__main__":
    main()
