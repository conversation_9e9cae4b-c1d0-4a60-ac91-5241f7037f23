#!/usr/bin/env python3
"""
Improved CURL Scraper with Anti-Bot Protection Handling
Uses rotating user agents, retry logic, and proper delays to avoid detection
"""

import json
import os
import time
import argparse
import subprocess
import random
from datetime import datetime
from typing import List, Dict, Optional
import pandas as pd
import re
from bs4 import BeautifulSoup
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

class ImprovedCurlScraper:
    def __init__(self, max_workers: int = 2):
        self.max_workers = max_workers
        self.urls_file = "parallel_nyc_all_urls_deduplicated.json"
        self.results = []
        self.results_lock = threading.Lock()
        self.processed_count = 0

        # User agents rotation
        self.user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]

        # Rate limiting
        self.min_delay = 1.0  # Minimum delay between requests
        self.max_delay = 3.0  # Maximum delay between requests
        self.last_request_time = {}  # Track last request time per worker

    def get_random_user_agent(self) -> str:
        """Get a random user agent"""
        return random.choice(self.user_agents)

    def create_curl_command(self, url: str) -> str:
        """Create curl command with rotating user agent and proper headers"""
        user_agent = self.get_random_user_agent()

        # Base curl command with anti-detection measures
        cmd = [
            'curl', '-s', '-L', '--max-time', '30',
            '--retry', '3', '--retry-delay', '2',
            '-H', f'User-Agent: {user_agent}',
            '-H', 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            '-H', 'Accept-Language: en-US,en;q=0.5',
            '-H', 'Accept-Encoding: gzip, deflate, br',
            '-H', 'DNT: 1',
            '-H', 'Connection: keep-alive',
            '-H', 'Upgrade-Insecure-Requests: 1',
            '-H', 'Sec-Fetch-Dest: document',
            '-H', 'Sec-Fetch-Mode: navigate',
            '-H', 'Sec-Fetch-Site: none',
            '-H', 'Cache-Control: no-cache',
            '--compressed',
            url
        ]

        return ' '.join(f'"{arg}"' if ' ' in arg else arg for arg in cmd)

    def execute_curl_request(self, url: str, worker_id: str) -> Optional[str]:
        """Execute curl request with proper error handling and rate limiting"""

        # Rate limiting per worker
        current_time = time.time()
        if worker_id in self.last_request_time:
            time_since_last = current_time - self.last_request_time[worker_id]
            min_interval = random.uniform(self.min_delay, self.max_delay)
            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                print(f"Worker {worker_id}: Rate limiting - sleeping {sleep_time:.1f}s")
                time.sleep(sleep_time)

        self.last_request_time[worker_id] = time.time()

        max_retries = 3
        for attempt in range(max_retries):
            try:
                print(f"Worker {worker_id}: Fetching {url} (attempt {attempt + 1}/{max_retries})")

                # Create curl command
                curl_cmd = self.create_curl_command(url)

                # Execute curl
                result = subprocess.run(
                    curl_cmd,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=45
                )

                if result.returncode == 0:
                    html = result.stdout

                    if len(html) < 500:
                        print(f"Worker {worker_id}: Content too short ({len(html)} chars), retrying...")
                        if attempt < max_retries - 1:
                            time.sleep(random.uniform(5, 10))
                            continue

                    # Check for anti-bot protection
                    protection_indicators = [
                        "Just a moment",
                        "Enable JavaScript and cookies",
                        "Checking your browser",
                        "Please wait while we verify",
                        "Ray ID:",
                        "Cloudflare",
                        "DDoS protection",
                        "Browser integrity check"
                    ]

                    if any(indicator in html for indicator in protection_indicators):
                        print(f"Worker {worker_id}: Anti-bot protection detected, waiting and retrying...")
                        if attempt < max_retries - 1:
                            # Exponential backoff with jitter
                            wait_time = random.uniform(10, 20) * (2 ** attempt)
                            time.sleep(wait_time)
                            continue
                        else:
                            print(f"Worker {worker_id}: Anti-bot protection persists after {max_retries} attempts")
                            return None

                    print(f"Worker {worker_id}: Successfully fetched {len(html)} characters")
                    return html
                else:
                    print(f"Worker {worker_id}: cURL failed with return code {result.returncode}")
                    print(f"Worker {worker_id}: stderr: {result.stderr[:200]}")
                    if attempt < max_retries - 1:
                        time.sleep(random.uniform(3, 7))
                        continue

            except subprocess.TimeoutExpired:
                print(f"Worker {worker_id}: Request timeout for {url}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(5, 10))
                    continue
            except Exception as e:
                print(f"Worker {worker_id}: Error executing cURL: {str(e)[:100]}")
                if attempt < max_retries - 1:
                    time.sleep(random.uniform(3, 7))
                    continue

        print(f"Worker {worker_id}: Failed to fetch {url} after {max_retries} attempts")
        return None

    def extract_data_from_html(self, html: str, url: str) -> Dict:
        """Extract profile data from HTML using proper selectors"""

        data = {
            'url': url,
            'name': '',
            'age': '',
            'phone': '',
            'location': '',
            'description': '',
            'website_type': '',
            'extraction_success': False,
            'scraped_at': datetime.now().isoformat(),
            'content_length': len(html)
        }

        try:
            soup = BeautifulSoup(html, 'html.parser')

            # Detect website type
            domain = url.lower()
            if 'aypapi' in domain:
                data['website_type'] = 'aypapi'
            elif 'aaok' in domain:
                data['website_type'] = 'aaok'
            else:
                data['website_type'] = 'unknown'

            # Extract name from title
            name_elem = soup.select_one('.viewposttitle')
            if name_elem:
                data['name'] = name_elem.get_text(strip=True)

            # Extract age
            age_elem = soup.select_one('.postTitleAge')
            if age_elem:
                age_text = age_elem.get_text(strip=True)
                if age_text.isdigit() and 18 <= int(age_text) <= 65:
                    data['age'] = age_text

            # Extract phone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                if phone_clean.strip():
                    data['phone'] = phone_clean.strip()

            # Extract description
            body_elem = soup.select_one('.viewpostbody')
            if body_elem:
                data['description'] = body_elem.get_text(strip=True)[:500]

            # Extract location from various possible elements
            location_selectors = [
                '.location', '.address', '.city-info', '.viewpostlocation'
            ]
            for selector in location_selectors:
                location_elem = soup.select_one(selector)
                if location_elem:
                    data['location'] = location_elem.get_text(strip=True)
                    break

            # Get page title for additional context
            title = soup.title
            if title:
                data['page_title'] = title.get_text(strip=True)

            # Check if we got meaningful data
            if data['name'] or data['age'] or data['phone']:
                data['extraction_success'] = True

        except Exception as e:
            print(f"Error extracting data from {url}: {e}")
            data['error'] = str(e)

        return data

    def process_url_batch(self, urls: List[str], worker_id: str) -> List[Dict]:
        """Process a batch of URLs"""
        results = []

        print(f"Worker {worker_id}: Starting batch of {len(urls)} URLs")

        for i, url in enumerate(urls):
            print(f"\nWorker {worker_id}: Processing {i+1}/{len(urls)}: {url}")

            # Fetch HTML
            html = self.execute_curl_request(url, worker_id)

            if html:
                # Extract data
                data = self.extract_data_from_html(html, url)
                results.append(data)

                if data['extraction_success']:
                    print(f"Worker {worker_id}: ✅ Success - Name: '{data['name']}', Age: {data['age']}, Phone: '{data['phone']}'")
                else:
                    print(f"Worker {worker_id}: ❌ No meaningful data extracted")
            else:
                # Add failed entry
                failed_data = {
                    'url': url,
                    'extraction_success': False,
                    'error': 'Failed to fetch page content',
                    'scraped_at': datetime.now().isoformat()
                }
                results.append(failed_data)
                print(f"Worker {worker_id}: ❌ Failed to fetch content")

            # Add results to main collection
            with self.results_lock:
                self.results.extend([data] if html else [failed_data])
                self.processed_count += 1

                # Save intermediate results periodically
                if self.processed_count % 100 == 0:
                    self.save_results(self.results, f"checkpoint_{self.processed_count}")
                    print(f"💾 Saved checkpoint at {self.processed_count} URLs")

        print(f"Worker {worker_id}: Completed batch - {len([r for r in results if r.get('extraction_success', False)])}/{len(results)} successful")
        return results

    def process_urls_parallel(self, urls: List[str], max_urls: Optional[int] = None) -> List[Dict]:
        """Process URLs using multiple workers"""

        if max_urls:
            urls = urls[:max_urls]

        print(f"🚀 Starting parallel processing of {len(urls)} URLs with {self.max_workers} workers")

        # Split URLs into batches for workers
        batch_size = max(1, len(urls) // self.max_workers)
        url_batches = []

        for i in range(0, len(urls), batch_size):
            batch = urls[i:i + batch_size]
            url_batches.append(batch)

        # Ensure we don't exceed max_workers
        while len(url_batches) > self.max_workers:
            extra_batch = url_batches.pop()
            url_batches[-1].extend(extra_batch)

        print(f"📊 Split into {len(url_batches)} batches: {[len(batch) for batch in url_batches]}")

        # Process batches in parallel
        all_results = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_batch = {
                executor.submit(self.process_url_batch, batch, f"curl-{i}"): batch
                for i, batch in enumerate(url_batches)
            }

            # Collect results
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                except Exception as e:
                    print(f"❌ Worker batch failed: {e}")

        return all_results

    def save_results(self, results: List[Dict], suffix: str = "final"):
        """Save results to Excel file"""

        if not results:
            print("⚠️  No results to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"improved_curl_{len(results)}_urls_{timestamp}_{suffix}.xlsx"

        try:
            df = pd.DataFrame(results)

            # Reorder columns
            column_order = [
                'url', 'name', 'age', 'phone', 'location', 'description',
                'website_type', 'extraction_success', 'page_title', 'content_length', 'scraped_at', 'error'
            ]

            # Only include columns that exist
            existing_columns = [col for col in column_order if col in df.columns]
            df = df[existing_columns]

            # Create multiple sheets
            with pd.ExcelWriter(filename) as writer:
                # All data
                df.to_excel(writer, sheet_name='All_Data', index=False)

                # Successful extractions only
                successful_df = df[df['extraction_success'] == True]
                if not successful_df.empty:
                    successful_df.to_excel(writer, sheet_name='Successful', index=False)

                # Failed extractions
                failed_df = df[df['extraction_success'] == False]
                if not failed_df.empty:
                    failed_df.to_excel(writer, sheet_name='Failed', index=False)

                # Statistics sheet
                stats_data = {
                    'Metric': [
                        'Total URLs',
                        'Successful Extractions',
                        'Failed Extractions',
                        'Success Rate (%)',
                        'AAOK URLs',
                        'AYPAPI URLs',
                        'URLs with Names',
                        'URLs with Ages',
                        'URLs with Phones'
                    ],
                    'Value': [
                        len(df),
                        len(successful_df),
                        len(failed_df),
                        f"{(len(successful_df) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                        len(df[df['website_type'] == 'aaok']),
                        len(df[df['website_type'] == 'aypapi']),
                        len(df[df['name'].str.strip() != '']),
                        len(df[df['age'].str.strip() != '']),
                        len(df[df['phone'].str.strip() != ''])
                    ]
                }
                pd.DataFrame(stats_data).to_excel(writer, sheet_name='Statistics', index=False)

            # Calculate and display statistics
            successful = len(successful_df)
            failed = len(failed_df)
            success_rate = (successful / len(df)) * 100 if len(df) > 0 else 0

            print(f"💾 Saved {len(df)} records to {filename}")
            print(f"📊 Results: {successful} successful, {failed} failed ({success_rate:.1f}% success rate)")

            # Show sample of successful extractions
            if not successful_df.empty:
                print(f"✅ Sample successful extractions:")
                for i, row in successful_df.head(5).iterrows():
                    name = row.get('name', 'N/A').strip()
                    age = row.get('age', 'N/A')
                    phone = row.get('phone', 'N/A').strip()
                    print(f"   • Name: '{name}' | Age: {age} | Phone: '{phone}'")

            # Show failure analysis
            if not failed_df.empty and 'error' in failed_df.columns:
                error_counts = failed_df['error'].value_counts()
                print(f"❌ Top failure reasons:")
                for error, count in error_counts.head(5).items():
                    print(f"   • {error}: {count} URLs")

        except Exception as e:
            print(f"❌ Error saving results: {e}")

def main():
    parser = argparse.ArgumentParser(description="Improved CURL Scraper with Anti-Bot Protection")
    parser.add_argument('--max-urls', type=int, help='Maximum URLs to process (for testing)')
    parser.add_argument('--workers', type=int, default=2, help='Number of parallel workers (default: 2)')

    args = parser.parse_args()

    scraper = ImprovedCurlScraper(max_workers=args.workers)

    # Load URLs
    if not os.path.exists(scraper.urls_file):
        print(f"❌ URLs file not found: {scraper.urls_file}")
        return False

    print(f"📂 Loading URLs from {scraper.urls_file}")
    with open(scraper.urls_file, 'r') as f:
        url_data = json.load(f)

    # Extract all URLs
    all_urls = []
    for borough_source, data in url_data.items():
        if 'urls' in data and data['urls']:
            all_urls.extend(data['urls'])
            print(f"📍 Loaded {len(data['urls'])} URLs from {borough_source}")

    print(f"📊 Total URLs loaded: {len(all_urls)}")

    if args.max_urls:
        print(f"🧪 Testing mode: limiting to {args.max_urls} URLs")

    # Process URLs
    try:
        start_time = time.time()
        results = scraper.process_urls_parallel(all_urls, args.max_urls)

        # Save final results
        scraper.save_results(results, "final")

        end_time = time.time()
        processing_time = (end_time - start_time) / 60

        print(f"\n🎉 Scraping completed successfully!")
        print(f"⏰ Total processing time: {processing_time:.1f} minutes")
        print(f"⚡ Average speed: {len(results) / (processing_time * 60):.2f} URLs/second")
        return True

    except KeyboardInterrupt:
        print(f"\n⚠️  Scraping interrupted by user")
        print(f"💾 Saving partial results...")
        scraper.save_results(scraper.results, "interrupted")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    result = main()
    exit(0 if result else 1)
