#!/usr/bin/env python3
"""
Test script to verify both aaok and aypapi sources are working
"""

import sys
from web_scraper import WebScraper

def test_both_sources():
    """Test both aaok and aypapi sources"""
    print("Testing Both Sources (aaok and aypapi)")
    print("=" * 50)
    
    # Use your Mistral API key
    scraper = WebScraper(mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso")
    scraper.request_delay = 0.5  # Faster for testing
    
    try:
        # Get city-source combinations
        cities = scraper.parse_url_list()
        if not cities:
            print("✗ No cities available for testing")
            return False
        
        print(f"Total city-source combinations: {len(cities)}")
        
        # Find Auburn, Alabama from both sources
        auburn_aaok = None
        auburn_aypapi = None
        
        for city in cities:
            if city['city'] == 'Auburn' and city['state'] == 'Alabama':
                if city['source'] == 'aaok':
                    auburn_aaok = city
                elif city['source'] == 'aypapi':
                    auburn_aypapi = city
        
        if not auburn_aaok or not auburn_aypapi:
            print("✗ Could not find Auburn, Alabama from both sources")
            return False
        
        print(f"✓ Found Auburn, Alabama from both sources:")
        print(f"  aaok: {auburn_aaok['url']}")
        print(f"  aypapi: {auburn_aypapi['url']}")
        
        # Test both sources
        results = {}
        
        for source_name, city_info in [("aaok", auburn_aaok), ("aypapi", auburn_aypapi)]:
            print(f"\nTesting {source_name.upper()} source...")
            
            # Get search page
            search_html = scraper.execute_curl_request(city_info['url'], scraper.search_curl_template)
            if not search_html:
                print(f"✗ Failed to get search page from {source_name}")
                continue
            
            # Extract URLs (filtered by age ≤30)
            dedicated_urls = scraper.extract_dedicated_urls(search_html)
            if not dedicated_urls:
                print(f"✗ No dedicated URLs found from {source_name}")
                continue
            
            print(f"✓ Found {len(dedicated_urls)} URLs from {source_name} (age ≤30)")
            
            # Test first dedicated page
            if dedicated_urls:
                test_url = dedicated_urls[0]
                print(f"Testing first URL: {test_url}")
                
                html = scraper.execute_curl_request(test_url, scraper.dedicated_curl_template)
                if html:
                    data = scraper.extract_dedicated_page_data(html, test_url)
                    if data:
                        data['city'] = city_info['city']
                        data['state'] = city_info['state']
                        data['source'] = city_info['source']
                        
                        print(f"✓ Successfully extracted data from {source_name}:")
                        print(f"  Title: {data.get('title', 'N/A')[:50]}...")
                        print(f"  Age: {data.get('age', 'N/A')}")
                        print(f"  Phone: {data.get('phone', 'N/A')}")
                        if data.get('social_media'):
                            print(f"  Social Media: {data.get('social_media', 'N/A')}")
                        
                        results[source_name] = data
                    else:
                        print(f"✗ Failed to extract data from {source_name} (not a woman or failed)")
                else:
                    print(f"✗ Failed to get dedicated page from {source_name}")
        
        if len(results) == 2:
            print(f"\n✓ Both sources working successfully!")
            print(f"✓ aaok and aypapi are both providing data")
            
            # Save test results
            scraper.scraped_data = list(results.values())
            scraper.save_to_excel("both_sources_test.xlsx")
            print("✓ Test data saved to both_sources_test.xlsx")
            
            return True
        else:
            print(f"\n✗ Only {len(results)} source(s) working")
            return False
            
    except Exception as e:
        print(f"✗ Test failed with exception: {e}")
        return False

def main():
    """Run both sources test"""
    success = test_both_sources()
    
    if success:
        print("\n" + "=" * 50)
        print("✓ Both sources test PASSED!")
        print("\nFeatures confirmed:")
        print("  ✓ aaok.com working")
        print("  ✓ aypapi.com working")
        print("  ✓ Age ≤30 filtering")
        print("  ✓ Women only filtering")
        print("  ✓ Mistral AI enhancement")
        print("  ✓ Social media extraction")
        print("\nTo scrape a specific state:")
        print("  python state_scraper.py Alabama")
        print("  python state_scraper.py 'New York'")
        print("  python state_scraper.py --list-states")
        return 0
    else:
        print("\n" + "=" * 50)
        print("✗ Both sources test FAILED. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
