#!/usr/bin/env python3
"""
Direct URL Scraper - Phase 1 URL Collection
Collects profile URLs from search pages without using ScraperAPI
Works similar to the existing comprehensive_data_extractor.py infrastructure

Usage:
    python direct_url_scraper.py --cities "Columbus,Cleveland" --state "Ohio" --max-pages 10
    python direct_url_scraper.py --interactive  # Interactive mode
    python direct_url_scraper.py --test-mode    # Test with limited scope
"""

import json
import requests
import re
import time
import logging
import os
import argparse
from datetime import datetime
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any, Set
from urllib.parse import urljoin, urlparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('direct_url_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DirectURLScraper:
    """Direct URL scraper for Phase 1 - collects profile URLs from search pages"""

    def __init__(self, delay: float = 2.0, max_pages_per_source: int = 15):
        self.delay = delay
        self.max_pages_per_source = max_pages_per_source

        # Session setup with realistic headers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'no-cache',
        })

        # Sources to scrape (no escortalligator as per instructions)
        self.sources = ['aaok', 'aypapi']

        # Statistics tracking
        self.stats = {
            'search_pages_scraped': 0,
            'urls_extracted': 0,
            'cities_processed': 0,
            'requests_made': 0
        }

        # Default cities for different states
        self.city_mappings = {
            'Ohio': [
                {'name': 'Columbus', 'url_name': 'columbus'},
                {'name': 'Cleveland', 'url_name': 'cleveland'},
                {'name': 'Dayton', 'url_name': 'dayton'},
                {'name': 'Toledo', 'url_name': 'toledo'},
                {'name': 'Cincinnati', 'url_name': 'cincinnati'}
            ],
            'Maryland': [
                {'name': 'Baltimore', 'url_name': 'baltimore'},
                {'name': 'Annapolis', 'url_name': 'annapolis'}
            ],
            'Pennsylvania': [
                {'name': 'Philadelphia', 'url_name': 'philadelphia'},
                {'name': 'Pittsburgh', 'url_name': 'pittsburgh'}
            ],
            'Delaware': [
                {'name': 'Wilmington', 'url_name': 'wilmington'},
                {'name': 'Dover', 'url_name': 'dover'}
            ],
            'New Jersey': [
                {'name': 'South New Jersey', 'url_name': 'south%20new%20jersey'}
            ]
        }

    def fetch_page_content(self, url: str, retries: int = 3) -> Optional[str]:
        """Fetch page content with retries and error handling"""
        for attempt in range(retries):
            try:
                logger.debug(f"Fetching: {url} (attempt {attempt + 1})")
                response = self.session.get(url, timeout=30)
                self.stats['requests_made'] += 1

                if response.status_code == 200:
                    return response.text
                elif response.status_code == 403:
                    logger.warning(f"403 Forbidden for {url} - trying with different headers")
                    # Try with simplified headers
                    simple_headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                    response = self.session.get(url, headers=simple_headers, timeout=30)
                    if response.status_code == 200:
                        return response.text
                else:
                    logger.warning(f"HTTP {response.status_code} for {url}")

            except requests.exceptions.RequestException as e:
                logger.warning(f"Request failed for {url}: {e}")
                if attempt < retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff

        return None

    def extract_age_from_context(self, link_element) -> Optional[int]:
        """Extract age information from link context"""
        try:
            # Check the link text itself
            link_text = link_element.get_text(strip=True)
            age_match = re.search(r'\b(\d{2})\b', link_text)
            if age_match:
                age = int(age_match.group(1))
                if 18 <= age <= 99:
                    return age

            # Check parent elements for age info
            parent = link_element.parent
            for _ in range(3):  # Check up to 3 parent levels
                if parent is None:
                    break

                # Look for age in class names or text
                parent_text = parent.get_text()
                age_patterns = [
                    r'Age[:\s]*(\d{2})',
                    r'(\d{2})\s*years?\s*old',
                    r'I\'m\s*(\d{2})',
                    r'\b(\d{2})\b'
                ]

                for pattern in age_patterns:
                    age_match = re.search(pattern, parent_text, re.IGNORECASE)
                    if age_match:
                        age = int(age_match.group(1))
                        if 18 <= age <= 99:
                            return age

                parent = parent.parent

            return None

        except Exception:
            return None

    def extract_profile_urls_from_html(self, html: str, source: str, base_url: str) -> List[Dict]:
        """Extract profile URLs from search page HTML"""
        if not html:
            return []

        try:
            soup = BeautifulSoup(html, 'html.parser')
            profile_urls = []

            # Look for profile links - multiple patterns
            link_patterns = [
                soup.find_all('a', class_='listtitle'),  # Main pattern
                soup.find_all('a', href=re.compile(r'/post/escorts/')),  # Direct post links
                soup.find_all('a', href=re.compile(r'/escorts/usa/'))  # Alternative pattern
            ]

            # Combine all found links
            all_links = []
            for pattern_links in link_patterns:
                all_links.extend(pattern_links)

            # Remove duplicates based on href
            seen_hrefs = set()
            unique_links = []
            for link in all_links:
                href = link.get('href', '')
                if href and href not in seen_hrefs:
                    seen_hrefs.add(href)
                    unique_links.append(link)

            for link in unique_links:
                href = link.get('href', '')

                # Only process links that contain 'post/escorts'
                if 'post/escorts' not in href:
                    continue

                # Skip escortalligator links
                if 'escortalligator' in href:
                    continue

                # Ensure link belongs to current source
                if source not in href:
                    continue

                # Build full URL
                if href.startswith('http'):
                    full_url = href
                elif href.startswith('/'):
                    full_url = f"https://{source}.com.listcrawler.eu{href}"
                else:
                    full_url = urljoin(base_url, href)

                # Extract age information
                age_info = self.extract_age_from_context(link)

                # Only include if age ≤ 30 or age not found (will filter in Phase 2)
                if age_info is None or age_info <= 30:
                    profile_urls.append({
                        'url': full_url,
                        'estimated_age': age_info,
                        'source': source
                    })

            return profile_urls

        except Exception as e:
            logger.error(f"Error extracting URLs from HTML: {e}")
            return []

    def scrape_city_urls(self, city_info: Dict, state: str) -> List[Dict]:
        """Scrape URLs from search pages for a city"""
        city_name = city_info['name']
        url_name = city_info['url_name']

        logger.info(f"Extracting URLs for {city_name}, {state}")

        all_urls = []

        for source in self.sources:
            logger.info(f"   Scraping {source.upper()} search pages...")

            # Build base URL
            state_url = state.lower().replace(' ', '%20')
            base_url = f"https://{source}.com.listcrawler.eu/brief/escorts/usa/{state_url}/{url_name}"

            page = 1
            consecutive_empty = 0

            while page <= self.max_pages_per_source and consecutive_empty < 3:
                search_url = f"{base_url}/{page}"
                logger.info(f"      Page {page}: {search_url}")

                # Fetch search page
                html = self.fetch_page_content(search_url)
                if not html:
                    consecutive_empty += 1
                    page += 1
                    time.sleep(self.delay)
                    continue

                # Extract URLs from this page
                page_urls = self.extract_profile_urls_from_html(html, source, base_url)

                if not page_urls:
                    consecutive_empty += 1
                    logger.info(f"         No URLs found on page {page}")
                else:
                    consecutive_empty = 0
                    # Add city/state info to each URL
                    for url_data in page_urls:
                        url_data.update({
                            'city': city_name,
                            'state': state,
                            'page': page
                        })

                    all_urls.extend(page_urls)
                    logger.info(f"         Found {len(page_urls)} URLs on page {page}")

                self.stats['search_pages_scraped'] += 1
                page += 1
                time.sleep(self.delay)  # Rate limiting

            source_count = len([u for u in all_urls if u['source'] == source])
            logger.info(f"   {source.upper()}: {source_count} URLs")

        logger.info(f"{city_name} total: {len(all_urls)} URLs")
        self.stats['urls_extracted'] += len(all_urls)
        self.stats['cities_processed'] += 1

        return all_urls

    def validate_url(self, url: str) -> bool:
        """Quick validation of URL format"""
        try:
            parsed = urlparse(url)
            return (
                parsed.scheme in ['http', 'https'] and
                'listcrawler.eu' in parsed.netloc and
                'post/escorts' in parsed.path and
                ('aaok' in parsed.netloc or 'aypapi' in parsed.netloc)
            )
        except:
            return False

    def scrape_state_cities(self, state: str, custom_cities: List[str] = None,
                           max_pages: int = None, test_mode: bool = False) -> str:
        """Scrape URLs from all cities in a state"""
        logger.info("=" * 80)
        logger.info(f"PHASE 1: URL EXTRACTION FOR {state.upper()}")
        logger.info("=" * 80)

        # Get cities for this state
        if state not in self.city_mappings:
            logger.error(f"State '{state}' not found in city mappings")
            logger.info(f"Available states: {', '.join(self.city_mappings.keys())}")
            return None

        # Get city list
        state_cities = self.city_mappings[state]

        # Filter cities if custom list provided
        if custom_cities:
            filtered_cities = []
            for city_info in state_cities:
                if city_info['name'] in custom_cities:
                    filtered_cities.append(city_info)
            if filtered_cities:
                state_cities = filtered_cities
            else:
                logger.warning("No matching cities found, using all default cities")

        # Test mode adjustments
        if test_mode:
            state_cities = state_cities[:2]  # Limit to 2 cities
            self.max_pages_per_source = min(self.max_pages_per_source, 3)
            logger.info("🧪 TEST MODE: Limited to 2 cities, 3 pages per source")

        # Override max pages if specified
        if max_pages:
            self.max_pages_per_source = max_pages

        logger.info(f"Cities to process: {[c['name'] for c in state_cities]}")
        logger.info(f"Max pages per source: {self.max_pages_per_source}")

        all_urls = []

        for city_info in state_cities:
            city_urls = self.scrape_city_urls(city_info, state)

            # Validate URLs
            valid_urls = [url_data for url_data in city_urls if self.validate_url(url_data['url'])]
            invalid_count = len(city_urls) - len(valid_urls)

            if invalid_count > 0:
                logger.info(f"   Filtered out {invalid_count} invalid URLs")

            all_urls.extend(valid_urls)

            # Save individual city results
            city_filename = f"phase1_urls_{city_info['name'].replace(' ', '_')}_{state.replace(' ', '_')}.json"
            with open(city_filename, 'w') as f:
                json.dump(valid_urls, f, indent=2)
            logger.info(f"Saved {len(valid_urls)} URLs to {city_filename}")

        # Save all URLs
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        all_urls_filename = f"phase1_all_urls_{state.replace(' ', '_')}_{timestamp}.json"
        with open(all_urls_filename, 'w') as f:
            json.dump(all_urls, f, indent=2)

        # Print statistics
        logger.info("=" * 80)
        logger.info("PHASE 1 COMPLETE")
        logger.info("=" * 80)
        logger.info(f"Total URLs extracted: {len(all_urls)}")
        logger.info(f"Cities processed: {self.stats['cities_processed']}")
        logger.info(f"Search pages scraped: {self.stats['search_pages_scraped']}")
        logger.info(f"Requests made: {self.stats['requests_made']}")
        logger.info(f"URLs saved to: {all_urls_filename}")

        # URL breakdown by source
        source_breakdown = {}
        for url_data in all_urls:
            source = url_data['source']
            source_breakdown[source] = source_breakdown.get(source, 0) + 1

        logger.info("URLs by Source:")
        for source, count in source_breakdown.items():
            logger.info(f"   {source}: {count} URLs")

        logger.info("=" * 80)
        return all_urls_filename

    def show_available_options(self):
        """Show available states and cities"""
        print("\n📍 Available States and Cities:")
        print("=" * 50)
        for state, cities in self.city_mappings.items():
            print(f"\n{state}:")
            for i, city in enumerate(cities, 1):
                print(f"  {i}. {city['name']}")

    def interactive_mode(self):
        """Interactive mode for user input"""
        print("=" * 80)
        print("🚀 DIRECT URL SCRAPER - INTERACTIVE MODE")
        print("=" * 80)

        self.show_available_options()

        # Get state
        while True:
            state = input(f"\nEnter state name: ").strip()
            if state in self.city_mappings:
                break
            print(f"❌ State '{state}' not found. Available: {', '.join(self.city_mappings.keys())}")

        # Get cities
        print(f"\nCities available for {state}: {', '.join([c['name'] for c in self.city_mappings[state]])}")
        cities_input = input("Enter city names (comma-separated, or press Enter for all): ").strip()

        custom_cities = None
        if cities_input:
            custom_cities = [city.strip() for city in cities_input.split(',')]
            print(f"Selected cities: {custom_cities}")
        else:
            print("Using all cities")

        # Get max pages
        while True:
            try:
                max_pages_input = input(f"Max pages per city/source (default {self.max_pages_per_source}): ").strip()
                if not max_pages_input:
                    break
                max_pages = int(max_pages_input)
                if max_pages > 0:
                    break
                else:
                    print("❌ Please enter a positive number.")
            except ValueError:
                print("❌ Please enter a valid number.")

        max_pages = int(max_pages_input) if max_pages_input else None

        # Confirm
        print(f"\n📋 Configuration:")
        print(f"   State: {state}")
        print(f"   Cities: {custom_cities if custom_cities else 'All'}")
        print(f"   Max pages: {max_pages if max_pages else self.max_pages_per_source}")
        print(f"   Delay: {self.delay}s")

        confirm = input(f"\nStart scraping? (y/N): ").strip().lower()
        if confirm == 'y':
            return self.scrape_state_cities(state, custom_cities, max_pages)
        else:
            print("❌ Operation cancelled.")
            return None


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Direct URL Scraper - Phase 1 URL Collection',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    # Main options
    parser.add_argument('--state', type=str, help='State to scrape (e.g., "Ohio", "Maryland")')
    parser.add_argument('--cities', type=str, help='Comma-separated list of cities')
    parser.add_argument('--max-pages', type=int, default=15, help='Max pages per city/source')
    parser.add_argument('--delay', type=float, default=2.0, help='Delay between requests')
    parser.add_argument('--test-mode', action='store_true', help='Test mode (limited scope)')
    parser.add_argument('--interactive', '-i', action='store_true', help='Interactive mode')
    parser.add_argument('--list-states', action='store_true', help='List available states')

    args = parser.parse_args()

    # Initialize scraper
    scraper = DirectURLScraper(delay=args.delay, max_pages_per_source=args.max_pages)

    # Handle list states
    if args.list_states:
        scraper.show_available_options()
        return

    # Interactive mode
    if args.interactive or (not args.state and len(os.sys.argv) == 1):
        scraper.interactive_mode()
        return

    # Command line mode
    if not args.state:
        print("❌ State is required. Use --interactive for guided setup or --list-states to see options")
        return

    # Parse cities
    custom_cities = None
    if args.cities:
        custom_cities = [city.strip() for city in args.cities.split(',')]

    # Test mode adjustments
    if args.test_mode:
        args.max_pages = min(args.max_pages, 3)
        args.delay = max(args.delay, 2.0)  # Slower for stability
        logger.info("🧪 Running in TEST MODE")

    try:
        # Run scraping
        result = scraper.scrape_state_cities(
            args.state,
            custom_cities,
            args.max_pages,
            args.test_mode
        )

        if result:
            print(f"\n🎉 SCRAPING COMPLETED SUCCESSFULLY!")
            print(f"📁 URLs saved to: {result}")
            print(f"🔄 You can now run Phase 2 with:")
            print(f"     python run_extractor.py --file {result}")
        else:
            print(f"\n❌ SCRAPING FAILED")

    except KeyboardInterrupt:
        print(f"\n🛑 Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        print(f"\n❌ Scraping failed with error: {e}")


if __name__ == "__main__":
    main()
