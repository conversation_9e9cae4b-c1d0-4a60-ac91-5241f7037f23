#!/usr/bin/env python3
"""
Simple comparison test between curl and Playwright methods
Tests a single URL to show the difference
"""

import asyncio
import json
import os
import subprocess
import time
from datetime import datetime
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async
from bs4 import BeautifulSoup

async def test_curl_method(test_url):
    """Test the traditional curl method"""
    print("\n🔧 Testing CURL Method")
    print("-" * 30)

    try:
        # Load the curl template from the existing scraper
        from nyc_boroughs_scraper import NYCBoroughsScraper
        scraper = NYCBoroughsScraper("dummy_key")

        start_time = time.time()
        html = scraper.execute_curl_request(test_url, scraper.dedicated_curl_template)
        fetch_time = time.time() - start_time

        if html:
            print(f"✅ CURL Success - Fetched {len(html)} characters in {fetch_time:.1f}s")

            # Check for anti-bot protection
            protection_phrases = [
                "Just a moment",
                "Enable JavaScript and cookies",
                "Checking your browser",
                "Please wait while we verify"
            ]

            protection_detected = any(phrase in html for phrase in protection_phrases)

            if protection_detected:
                print("❌ CURL blocked by anti-bot protection")
                return {
                    'success': False,
                    'method': 'curl',
                    'fetch_time': fetch_time,
                    'content_length': len(html),
                    'error': 'Anti-bot protection detected'
                }

            # Try to extract some basic data
            soup = BeautifulSoup(html, 'html.parser')

            # Look for age
            age_elem = soup.find('div', class_='titleAge')
            age = age_elem.get_text(strip=True) if age_elem else None

            # Look for title
            title_elem = soup.find('h1')
            title = title_elem.get_text(strip=True)[:50] if title_elem else None

            print(f"📊 CURL extracted - Age: {age}, Title: {title}")

            return {
                'success': True,
                'method': 'curl',
                'fetch_time': fetch_time,
                'content_length': len(html),
                'age': age,
                'title': title
            }
        else:
            print("❌ CURL failed to fetch content")
            return {
                'success': False,
                'method': 'curl',
                'fetch_time': fetch_time,
                'content_length': 0,
                'error': 'No content received'
            }

    except Exception as e:
        print(f"❌ CURL error: {str(e)[:100]}")
        return {
            'success': False,
            'method': 'curl',
            'fetch_time': 0,
            'content_length': 0,
            'error': f'Exception: {str(e)[:100]}'
        }

async def test_playwright_method(test_url):
    """Test the Playwright method"""
    print("\n🎭 Testing PLAYWRIGHT Method")
    print("-" * 30)

    playwright = None
    browser = None

    try:
        playwright = await async_playwright().start()

        # Launch browser
        browser = await playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-extensions'
            ]
        )

        # Create context
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )

        # Create page
        page = await context.new_page()
        await stealth_async(page)

        start_time = time.time()

        # Navigate to page
        response = await page.goto(test_url, wait_until='domcontentloaded', timeout=30000)

        if response and response.status == 200:
            print(f"✅ PLAYWRIGHT got response: {response.status}")

            # Wait for content
            await page.wait_for_timeout(2000)

            content = await page.content()
            fetch_time = time.time() - start_time

            print(f"📄 PLAYWRIGHT fetched {len(content)} characters in {fetch_time:.1f}s")

            # Check for anti-bot protection
            protection_phrases = [
                "Just a moment",
                "Enable JavaScript and cookies",
                "Checking your browser",
                "Please wait while we verify"
            ]

            protection_detected = any(phrase in content for phrase in protection_phrases)

            if protection_detected:
                print("⚠️  PLAYWRIGHT detected anti-bot protection, waiting longer...")
                await page.wait_for_timeout(5000)
                content = await page.content()
                fetch_time = time.time() - start_time
                print(f"📄 PLAYWRIGHT updated content: {len(content)} characters")

            # Try to extract data
            soup = BeautifulSoup(content, 'html.parser')

            # Look for age
            age_elem = soup.find('div', class_='titleAge')
            age = age_elem.get_text(strip=True) if age_elem else None

            # Look for title
            title_elem = soup.find('h1')
            title = title_elem.get_text(strip=True)[:50] if title_elem else None

            print(f"📊 PLAYWRIGHT extracted - Age: {age}, Title: {title}")

            # Check if we got meaningful data
            meaningful_content = len(content) > 1000 and (age or title)

            return {
                'success': meaningful_content,
                'method': 'playwright',
                'fetch_time': fetch_time,
                'content_length': len(content),
                'age': age,
                'title': title,
                'protection_detected': protection_detected
            }
        else:
            fetch_time = time.time() - start_time
            status = response.status if response else 'No response'
            print(f"❌ PLAYWRIGHT failed - Status: {status}")

            return {
                'success': False,
                'method': 'playwright',
                'fetch_time': fetch_time,
                'content_length': 0,
                'error': f'Bad response: {status}'
            }

    except Exception as e:
        fetch_time = time.time() - start_time if 'start_time' in locals() else 0
        print(f"❌ PLAYWRIGHT error: {str(e)[:100]}")
        return {
            'success': False,
            'method': 'playwright',
            'fetch_time': fetch_time,
            'content_length': 0,
            'error': f'Exception: {str(e)[:100]}'
        }

    finally:
        if browser:
            await browser.close()
        if playwright:
            await playwright.stop()

def compare_results(curl_result, playwright_result):
    """Compare the results from both methods"""
    print("\n" + "=" * 50)
    print("📊 COMPARISON RESULTS")
    print("=" * 50)

    print(f"CURL Success:       {'✅' if curl_result['success'] else '❌'}")
    print(f"PLAYWRIGHT Success: {'✅' if playwright_result['success'] else '❌'}")

    print(f"\nCURL Time:       {curl_result['fetch_time']:.1f}s")
    print(f"PLAYWRIGHT Time: {playwright_result['fetch_time']:.1f}s")

    print(f"\nCURL Content:       {curl_result['content_length']} chars")
    print(f"PLAYWRIGHT Content: {playwright_result['content_length']} chars")

    # Data comparison
    print(f"\nData Extraction:")
    print(f"  CURL Age:        {curl_result.get('age', 'None')}")
    print(f"  PLAYWRIGHT Age:  {playwright_result.get('age', 'None')}")
    print(f"  CURL Title:      {curl_result.get('title', 'None')}")
    print(f"  PLAYWRIGHT Title: {playwright_result.get('title', 'None')}")

    # Determine winner
    print(f"\n🏆 WINNER:")
    if playwright_result['success'] and not curl_result['success']:
        print("🎭 PLAYWRIGHT wins - Successfully bypassed anti-bot protection!")
    elif curl_result['success'] and not playwright_result['success']:
        print("🔧 CURL wins - Faster and worked in this case")
    elif both_success := (curl_result['success'] and playwright_result['success']):
        if playwright_result['content_length'] > curl_result['content_length']:
            print("🎭 PLAYWRIGHT wins - Got more complete content")
        elif curl_result['fetch_time'] < playwright_result['fetch_time']:
            print("🔧 CURL wins - Faster with similar results")
        else:
            print("🤝 TIE - Both methods worked similarly")
    else:
        print("😞 Both methods failed on this URL")

    # Errors
    if not curl_result['success']:
        print(f"\nCURL Error: {curl_result.get('error', 'Unknown')}")
    if not playwright_result['success']:
        print(f"PLAYWRIGHT Error: {playwright_result.get('error', 'Unknown')}")

async def main():
    print("🔍 CURL vs PLAYWRIGHT Comparison Test")
    print("=" * 50)
    print("This test compares the two scraping methods on a single URL")
    print("to show how Playwright handles JavaScript and anti-bot protection")

    # Load test URL
    urls_file = "parallel_nyc_all_urls_deduplicated.json"
    test_url = None

    if os.path.exists(urls_file):
        with open(urls_file, 'r') as f:
            url_data = json.load(f)

        # Extract first URL
        for borough_source, data in url_data.items():
            if 'urls' in data and data['urls']:
                test_url = data['urls'][0]
                print(f"\nTesting URL from {borough_source}:")
                print(f"{test_url}")
                break

    if not test_url:
        print("❌ No test URL available")
        return False

    # Run both tests
    curl_result = await test_curl_method(test_url)
    playwright_result = await test_playwright_method(test_url)

    # Compare results
    compare_results(curl_result, playwright_result)

    # Recommendation
    print(f"\n💡 RECOMMENDATION:")
    if playwright_result['success'] and not curl_result['success']:
        print("Use the Playwright enhanced scraper for better success rate!")
        print("Command: python playwright_enhanced_scraper.py --max-urls 100")
    elif curl_result['success'] and playwright_result['success']:
        print("Both methods work, but Playwright may be more reliable for larger batches.")
        print("Try the full comparison: python compare_methods.py --max-urls 10")
    else:
        print("Further investigation needed. Try different URLs or check anti-bot protection.")

    return True

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
