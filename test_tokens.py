#!/usr/bin/env python3
"""
Test scrape.do tokens to see which ones are working
"""

import requests
import urllib.parse
import time
import json

def test_token(token, delay=2):
    """Test a single scrape.do token"""
    time.sleep(delay)

    try:
        # Use a simple test URL
        target_url = urllib.parse.quote("https://httpbin.org/ip")
        url = f"http://api.scrape.do/?url={target_url}&token={token}"

        print(f"Testing token: ...{token[-10:]}")
        print(f"Request URL: {url}")

        response = requests.get(url, timeout=30)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✅ Token WORKING!")
            try:
                data = response.json()
                print(f"Response: {json.dumps(data, indent=2)}")
            except:
                print(f"Response (first 200 chars): {response.text[:200]}...")
            return True
        else:
            print("❌ Token FAILED!")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('Message', 'Unknown error')}")
            except:
                print(f"Raw error: {response.text[:200]}...")
            return False

    except Exception as e:
        print(f"❌ Exception testing token: {e}")
        return False

def main():
    """Test all scrape.do tokens"""

    tokens = [
        "02bce8647f2b4e41ae7ca4db0ddf7dcbf1ec56081b3",
        "8fff20a4dddb418c9cce8bd644e03104586a0a97bb4",
        "7d9d306c569b492b93e93b4ca78516f8907444f4528",
        "6ffae17dc41f4e578bb037a9659f90372906f942cec",
        "db61a2d276da49fe8f9fde1b819e0ffeb7e90628dd6"
    ]

    print("🧪 Testing scrape.do tokens")
    print("=" * 50)

    working_tokens = []
    failed_tokens = []

    for i, token in enumerate(tokens, 1):
        print(f"\n📋 Testing Token {i}/{len(tokens)}")
        print("-" * 30)

        if test_token(token, delay=5):  # 5 second delay between tests
            working_tokens.append(token)
        else:
            failed_tokens.append(token)

    print(f"\n🎯 RESULTS")
    print("=" * 50)
    print(f"✅ Working tokens: {len(working_tokens)}")
    print(f"❌ Failed tokens: {len(failed_tokens)}")

    if working_tokens:
        print(f"\n✅ Working tokens:")
        for i, token in enumerate(working_tokens, 1):
            print(f"  {i}. ...{token[-10:]}")

    if failed_tokens:
        print(f"\n❌ Failed tokens:")
        for i, token in enumerate(failed_tokens, 1):
            print(f"  {i}. ...{token[-10:]}")

    # Test with an actual target URL if we have working tokens
    if working_tokens:
        print(f"\n🎯 Testing with actual scraping target...")
        print("-" * 40)

        target_url = urllib.parse.quote("https://aaok.com.listcrawler.eu/post/escorts/usa/newjersey/southjersey/191793963")
        working_token = working_tokens[0]
        url = f"http://api.scrape.do/?url={target_url}&token={working_token}"

        time.sleep(5)
        print(f"Testing actual scraping URL...")

        try:
            response = requests.get(url, timeout=60)
            print(f"Status: {response.status_code}")

            if response.status_code == 200:
                print(f"✅ Actual scraping SUCCESSFUL!")
                print(f"Response length: {len(response.text)} characters")
                print(f"First 200 chars: {response.text[:200]}...")
            else:
                print(f"❌ Actual scraping failed:")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data}")
                except:
                    print(f"Raw error: {response.text[:300]}...")

        except Exception as e:
            print(f"❌ Exception during actual scraping test: {e}")

if __name__ == "__main__":
    main()
