curl 'https://escortalligator.com.listcrawler.eu/post/escorts/usa/newyork/newyork/191400736' \
-H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8' \
-H 'accept-language: en-GB,en;q=0.9' \
-H 'cache-control: no-cache' \
-b 'cf_clearance=qvOezeqgwwGwEfKNOBDp4xVeiCCWMltoFiS3ia6kpnk-1754841494-1.2.1.1-qWdcHCkIAmQ..4G6.g46tDNhJbzgYsVxHZQJ4Nl4fttweWbapDDK_OFvebQkUJQt9NqAADefc95NoKkwFHo59YpOS..3Bf5tlqfrbtEDtCELZ8zOh.ETw3RDF3SBjNwBRO31dS9sMgGHzEkor6bLUCSs5R7eNNhBVPMlh9cqrZLU705_EArHGURyTvBjx7UYZwdllArQHyeRTTNdjAef_38XuZVgLajED5Tk11I5k.A; ageCheckAgree6=yes; startRef=inner; GID2=427785d2839ab19ef3d354602d801817; city=newyork; JSESSIONID=BB71F83CD2CA90358BA7C2BBDD1CBE07; showScumWarning=1; nichePointerCounter=2' \
-H 'pragma: no-cache' \
-H 'priority: u=0, i' \
-H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Brave";v="138"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'sec-fetch-dest: document' \
-H 'sec-fetch-mode: navigate' \
-H 'sec-fetch-site: none' \
-H 'sec-fetch-user: ?1' \
-H 'sec-gpc: 1' \
-H 'upgrade-insecure-requests: 1' \
-H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
$(document).ready(function () {

/*
* view saved post
*/
$("#view_saved_button").click(function () {
var pid = $(this).attr("pid");
var search = $(this).attr("search");
search = (!search || search.length === 0 ) ? "" : "?search="+search;

var uid = $('#uid').val();
//if user authorized
if (uid != '') {
location.href = "/private/1" + search;
}
});

$('.savePost[form="link_save_form"]').on('click', function () {
$('#rocky_modal').css({
"display": "block"
})
});
$('#link_save_form').on('submit', function (e) {
e.preventDefault();
});
/*
* note post
*/
function view_note(pid, note_post_button, mode) {
$(note_post_button).attr("mode", mode);
$(note_post_button).html("SAVE NOTE");
$("#view_note_text_" + pid).css("display", "none");
$("#edit_note_text_" + pid).css("display", "block");
$("#myDiv" + pid).css("display", "block"); // added by Rocky
}

function save_note(pid, note_post_button, mode) {
$(note_post_button).attr("mode", "wait");

var note = $("#edit_note_text_" + pid).find("textarea").val();
var url = "/note_post/";
var data = {
pid: pid,
note: note
};
$.ajax({
type: "POST",
url: url,
data: data,
success: function (data) {

$(note_post_button).attr("mode", mode);
$(note_post_button).html("EDIT NOTE");

$("#view_note_text_" + pid).html(data.data)
$("#view_note_text_" + pid).css("display", "block");
$("#edit_note_text_" + pid).css("display", "none");
if (!($.cookie("mySaved"))) {
$("#savedPointer").css("display", "block").delay(2000).fadeOut(1000);
}

}
});
}

function define_note_mode(note_post_button) {
var mode = $(note_post_button).attr("mode");
var pid = $(note_post_button).attr("pid");

if (mode == "wait") {
return false;
}

if (mode == "create_view") {
$(note_post_button).css('background-color', '#000000');
view_note(pid, note_post_button, "create_save");
$("#viewPostNoteId").removeClass("hiddenStuffFromPostPage");
// $(".viewPostNoteEdit").css("display", "block")
}

if (mode == "edit_view") {
$(note_post_button).css('background-color', '#000000');
view_note(pid, note_post_button, "edit_save");
}

if (mode == "create_save") {
$(note_post_button).css('background-color', '#FAAA3C');
save_note(pid, note_post_button, "edit_view");
}

if (mode == "edit_save") {
$(note_post_button).css('background-color', '#FAAA3C');
save_note(pid, note_post_button, "edit_view");
}
}

$(".note_post_button").click(function () {
define_note_mode(this);
});



/*
*
* rocky
*/

$("#dialogNoButton").click(function () {
$("#rocky_modal").css("display", "none");
if (!($.cookie("mySaved"))) {
////pointer is shown only if you haven't been on fridge
$("#savedPointer").css("display", "block").delay(2000).fadeOut(1000);
////////////////////////
};
// var new_uurrll = "/brief/1/";
// $('#link_save_form')[0].submit()
// if (!document.referrer) {} else {}
var url = $('#link_save_form').attr('action');;
var data = $('#link_save_form').serialize() + '&auth=none';
$.ajax({
url: url,
type: 'POST',
data: data,
success: function () {
location.reload();
}
});
});
// #link_save_form .save-note
$("#dialogYesButton").click(function () {
$("#viewPostNoteSave").css("display", "block");
$("#rocky_modal").css("display", "none");
$('html, body').animate({
scrollTop: $("#viewPostNoteSave").offset().top - 300
}, 1000);
});
$(".note-save-btn").on('click', function () {
$('#link_save_form .save-note')[0].value = $(".viewPostNoteEdit textarea")[0].value;
// alert($('#link_save_form .save-note')[0].value);
// $('#link_save_form')[0].submit();
var url = $('#link_save_form').attr('action');;
var data = $('#link_save_form').serialize() + '&auth=none';
$.ajax({
url: url,
type: 'POST',
data: data,
success: function () {
location.reload();
}
});
});

$(".delete .privateListButtonLink").click(function (event) {
event.preventDefault();
var itemId = $(this).attr("data-uurrll");
var thisBonus = $(this).attr("data-bonus");
var serch = $(this).attr("data-search");
serch = (!serch || serch.length === 0 ) ? "" : "?search="+serch;

var uurrll = thisBonus + "/delete_post/" + itemId + "/save" + serch;
$("#postDeleteYesButton a").attr("href", uurrll);
$("#rocky_modal_delete_confirmation").css("display", "block");
});


$("#postDeleteNoButton").click(function (event) {
event.preventDefault();
$("#rocky_modal_delete_confirmation").css("display", "none");
});

});
