#!/usr/bin/env python3
"""
New City Deduplication Scraper: Complete scraper for new cities from scratch
Combines URL extraction, phone deduplication, and full scraping in one tool
No existing files required - works from scratch for any state/city
"""

import sys
import os
import json
import time
import argparse
import re
import threading
from typing import List, Dict, Optional, Set
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from bs4 import BeautifulSoup
import pandas as pd
import subprocess


class NewCityDeduplicationScraper:
    def __init__(self, scraperapi_key: str = "********************************",
                 mistral_api_key: str = "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"):
        """Initialize new city deduplication scraper"""
        self.scraperapi_key = scraperapi_key
        self.mistral_api_key = mistral_api_key

        # File naming will be set based on city/state
        self.urls_file = None
        self.phone_db_file = None
        self.results_file = None

        # Phone deduplication tracking
        self.known_phones: Set[str] = set()
        self.phone_to_url: Dict[str, str] = {}
        self.url_to_phone: Dict[str, str] = {}
        self.phone_lock = threading.Lock()

        # ScraperAPI settings
        self.scraperapi_url = "https://api.scraperapi.com/"
        self.current_requests = 0
        self.max_requests_per_key = 4500

        # Web scraping headers
        self.search_headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive"
        }

        # Rate limiting
        self.request_delay = 0.5
        self.last_request_time = {}

        # Scraping parameters
        self.min_pages_to_scrape = 25
        self.max_consecutive_empty_pages = 5
        self.max_pages_per_city = 100
        self.phone_extraction_batch_size = 15
        self.phone_extraction_workers = 3

        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('new_city_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_filenames(self, state_name: str, city_name: str):
        """Setup filenames based on state and city"""
        safe_state = state_name.lower().replace(' ', '_').replace('-', '_')
        safe_city = city_name.lower().replace(' ', '_').replace('-', '_')

        self.urls_file = f"urls_{safe_state}_{safe_city}.json"
        self.phone_db_file = f"phones_{safe_state}_{safe_city}.json"
        self.results_file = f"results_{safe_state}_{safe_city}.xlsx"

    def get_city_urls(self, state_name: str, city_name: str) -> List[Dict[str, str]]:
        """Get city URL combinations from the web scraper infrastructure"""
        try:
            # Import from existing infrastructure
            from web_scraper import WebScraper
            temp_scraper = WebScraper()
            all_cities = temp_scraper.parse_url_list()

            # Filter for our specific city and state
            city_combinations = []
            for city_data in all_cities:
                if (city_data['city'].lower() == city_name.lower() and
                    city_data['state'].lower() == state_name.lower()):
                    city_combinations.append(city_data)

            return city_combinations

        except Exception as e:
            self.logger.error(f"Failed to get city URLs: {e}")
            return []

    def fetch_search_page(self, url: str) -> Optional[str]:
        """Fetch search page content using requests"""
        try:
            response = requests.get(url, headers=self.search_headers, timeout=30)
            if response.status_code == 200:
                return response.text
            else:
                self.logger.warning(f"HTTP {response.status_code} for {url}")
                return None

        except Exception as e:
            self.logger.error(f"Error fetching {url}: {e}")
            return None

    def extract_dedicated_urls(self, search_html: str) -> List[str]:
        """Extract dedicated page URLs from search page HTML (age ≤30 only)"""
        if not search_html:
            return []

        try:
            soup = BeautifulSoup(search_html, 'html.parser')
            dedicated_urls = []

            # Find all profile entries
            profile_divs = soup.find_all('div', class_='row')

            for div in profile_divs:
                try:
                    # Extract age
                    age_span = div.find('span', string=re.compile(r'Age:\s*\d+'))
                    if age_span:
                        age_text = age_span.get_text()
                        age_match = re.search(r'Age:\s*(\d+)', age_text)
                        if age_match:
                            age = int(age_match.group(1))

                            # Only include if age ≤ 30
                            if age <= 30:
                                # Find the link to dedicated page
                                link = div.find('a', href=True)
                                if link and 'post/escorts' in link['href']:
                                    full_url = link['href']
                                    if full_url.startswith('/'):
                                        # Handle relative URLs
                                        if 'aaok.com' in search_html:
                                            full_url = 'https://aaok.com.listcrawler.eu' + full_url
                                        elif 'aypapi.com' in search_html:
                                            full_url = 'https://aypapi.com.listcrawler.eu' + full_url

                                    if full_url not in dedicated_urls:
                                        dedicated_urls.append(full_url)

                except Exception as e:
                    continue

            return dedicated_urls

        except Exception as e:
            self.logger.error(f"Error extracting dedicated URLs: {e}")
            return []

    def extract_urls_from_city(self, city_data: Dict[str, str]) -> List[str]:
        """Extract all URLs from all search pages for a city"""
        city_name = city_data['city']
        source = city_data['source']
        base_url = city_data['url']

        self.logger.info(f"📥 Extracting URLs from {city_name} ({source})")

        all_urls = []
        page_num = 1
        empty_page_count = 0

        while True:
            # Construct page URL
            page_url = re.sub(r'/\d+$', f'/{page_num}', base_url)
            self.logger.info(f"   Processing page {page_num}: {page_url}")

            # Get search page HTML
            search_html = self.fetch_search_page(page_url)
            if not search_html:
                self.logger.warning(f"   Failed to get page {page_num}")
                empty_page_count += 1
            else:
                # Extract dedicated page URLs
                page_urls = self.extract_dedicated_urls(search_html)
                if not page_urls:
                    empty_page_count += 1
                    self.logger.info(f"   No URLs found on page {page_num} (empty page {empty_page_count}/{self.max_consecutive_empty_pages})")
                else:
                    empty_page_count = 0
                    all_urls.extend(page_urls)
                    self.logger.info(f"   Found {len(page_urls)} URLs on page {page_num}")

            # Check stopping conditions
            if page_num > self.min_pages_to_scrape and empty_page_count >= self.max_consecutive_empty_pages:
                self.logger.info(f"   Stopping after {self.max_consecutive_empty_pages} consecutive empty pages")
                break
            elif page_num <= self.min_pages_to_scrape:
                self.logger.info(f"   Continuing - need minimum {self.min_pages_to_scrape} pages (currently at {page_num})")

            time.sleep(self.request_delay)
            page_num += 1

            if page_num > self.max_pages_per_city:
                self.logger.info(f"   Reached maximum page limit ({self.max_pages_per_city})")
                break

        # Remove duplicates
        unique_urls = list(dict.fromkeys(all_urls))
        self.logger.info(f"   ✅ {city_name} ({source}): {len(unique_urls)} unique URLs from {page_num-1} pages")

        return unique_urls

    def phase1_extract_urls(self, state_name: str, city_name: str) -> List[str]:
        """Phase 1: Extract all URLs from search pages"""
        self.logger.info("🔍 PHASE 1: EXTRACTING URLS FROM SEARCH PAGES")
        self.logger.info("=" * 60)

        # Get city combinations
        city_combinations = self.get_city_urls(state_name, city_name)

        if not city_combinations:
            self.logger.error(f"❌ No URL combinations found for {city_name}, {state_name}")
            return []

        self.logger.info(f"Found {len(city_combinations)} source combinations for {city_name}")

        all_urls = []
        urls_data = {}

        for i, city_data in enumerate(city_combinations):
            source = city_data['source']
            self.logger.info(f"Processing {i+1}/{len(city_combinations)}: {source}")

            # Extract URLs from this source
            source_urls = self.extract_urls_from_city(city_data)
            all_urls.extend(source_urls)

            # Store structured data
            combo_key = f"{state_name}_{city_name}_{source}"
            urls_data[combo_key] = {
                'state': state_name,
                'city': city_name,
                'source': source,
                'base_url': city_data['url'],
                'total_urls': len(source_urls),
                'urls': source_urls,
                'extracted_at': datetime.now().isoformat()
            }

        # Remove duplicates across all sources
        unique_urls = list(dict.fromkeys(all_urls))

        # Save URLs data
        try:
            with open(self.urls_file, 'w') as f:
                json.dump(urls_data, f, indent=2)
            self.logger.info(f"💾 Saved URL data to {self.urls_file}")
        except Exception as e:
            self.logger.error(f"Failed to save URL data: {e}")

        self.logger.info("=" * 60)
        self.logger.info(f"✅ Phase 1 Complete: {len(unique_urls)} unique URLs extracted")

        return unique_urls

    def fetch_with_scraperapi(self, url: str) -> Optional[str]:
        """Fetch URL content using ScraperAPI"""
        try:
            params = {
                'api_key': self.scraperapi_key,
                'url': url,
                'render': 'false',
                'country_code': 'us'
            }

            response = requests.get(self.scraperapi_url, params=params, timeout=30)
            self.current_requests += 1

            if response.status_code == 200:
                return response.text
            else:
                self.logger.warning(f"ScraperAPI failed for {url}: Status {response.status_code}")
                return None

        except Exception as e:
            self.logger.error(f"Error fetching {url}: {e}")
            return None

    def extract_phone_only(self, url: str) -> Optional[str]:
        """Extract only phone number from URL (lightweight)"""
        try:
            response = self.fetch_with_scraperapi(url)
            if not response:
                return None

            soup = BeautifulSoup(response, 'html.parser')

            # Extract phone from viewposttelephone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                if phone_clean.strip():
                    # Process phone number (clean and add country code)
                    clean_phone = re.sub(r'[^0-9]', '', str(phone_clean))
                    if clean_phone and not clean_phone.startswith('1'):
                        clean_phone = '1' + clean_phone
                    return clean_phone

            return None

        except Exception as e:
            self.logger.warning(f"Error extracting phone from {url}: {e}")
            return None

    def extract_phones_batch(self, urls: List[str]) -> Dict[str, str]:
        """Extract phone numbers from batch of URLs"""
        phone_results = {}

        with ThreadPoolExecutor(max_workers=self.phone_extraction_workers) as executor:
            future_to_url = {
                executor.submit(self.extract_phone_only, url): url
                for url in urls
            }

            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    phone = future.result()
                    if phone:
                        phone_results[url] = phone
                        print(f"📱 Found phone {phone} for {url[-20:]}")

                except Exception as e:
                    self.logger.warning(f"Error processing {url}: {e}")

        return phone_results

    def phase2_deduplicate_phones(self, urls: List[str]) -> Dict[str, List[str]]:
        """Phase 2: Extract phones and deduplicate URLs"""
        self.logger.info("📱 PHASE 2: PHONE NUMBER DEDUPLICATION")
        self.logger.info("=" * 60)

        # Load existing phone database if it exists
        if os.path.exists(self.phone_db_file):
            try:
                with open(self.phone_db_file, 'r') as f:
                    phone_data = json.load(f)
                self.known_phones = set(phone_data.get('known_phones', []))
                self.phone_to_url = phone_data.get('phone_to_url', {})
                self.url_to_phone = phone_data.get('url_to_phone', {})
                self.logger.info(f"📱 Loaded {len(self.known_phones)} known phones from database")
            except Exception as e:
                self.logger.warning(f"Failed to load phone database: {e}")

        # Process URLs in batches
        new_phones = 0
        duplicate_phones = 0
        no_phone_count = 0

        total_batches = (len(urls) + self.phone_extraction_batch_size - 1) // self.phone_extraction_batch_size

        for i in range(0, len(urls), self.phone_extraction_batch_size):
            batch = urls[i:i + self.phone_extraction_batch_size]
            batch_num = (i // self.phone_extraction_batch_size) + 1

            self.logger.info(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch)} URLs)")

            # Extract phones from batch
            batch_phones = self.extract_phones_batch(batch)

            # Update database
            with self.phone_lock:
                for url, phone in batch_phones.items():
                    if phone not in self.known_phones:
                        self.known_phones.add(phone)
                        self.phone_to_url[phone] = url
                        self.url_to_phone[url] = phone
                        new_phones += 1
                    else:
                        self.url_to_phone[url] = phone
                        duplicate_phones += 1

                # Count no-phone URLs
                for url in batch:
                    if url not in batch_phones:
                        no_phone_count += 1

            # Save progress
            if batch_num % 3 == 0:
                self.save_phone_database()

            time.sleep(0.5)  # Rate limiting

        # Final save
        self.save_phone_database()

        # Create groups
        unique_urls = []
        duplicate_urls = []
        no_phone_urls = []

        for url in urls:
            if url in self.url_to_phone:
                phone = self.url_to_phone[url]
                if self.phone_to_url[phone] == url:
                    unique_urls.append(url)
                else:
                    duplicate_urls.append(url)
            else:
                no_phone_urls.append(url)

        # Summary
        self.logger.info("=" * 60)
        self.logger.info(f"📊 Phone Deduplication Summary:")
        self.logger.info(f"   • Total URLs: {len(urls)}")
        self.logger.info(f"   • New phones found: {new_phones}")
        self.logger.info(f"   • Duplicate phones: {duplicate_phones}")
        self.logger.info(f"   • No phone URLs: {no_phone_count}")
        self.logger.info(f"   • Unique URLs to scrape: {len(unique_urls)}")
        self.logger.info(f"   • Duplicate URLs skipped: {len(duplicate_urls)}")
        self.logger.info(f"   • Reduction: {len(duplicate_urls)}/{len(urls)} ({len(duplicate_urls)/len(urls)*100:.1f}%)")

        return {
            'unique_urls': unique_urls,
            'duplicate_urls': duplicate_urls,
            'no_phone_urls': no_phone_urls
        }

    def save_phone_database(self):
        """Save phone database to file"""
        try:
            phone_data = {
                'known_phones': list(self.known_phones),
                'phone_to_url': self.phone_to_url,
                'url_to_phone': self.url_to_phone,
                'last_updated': datetime.now().isoformat()
            }

            with open(self.phone_db_file, 'w') as f:
                json.dump(phone_data, f, indent=2)

        except Exception as e:
            self.logger.error(f"Failed to save phone database: {e}")

    def extract_full_data(self, url: str) -> Dict:
        """Extract full profile data from URL"""
        data = {
            'url': url,
            'name': '',
            'age': '',
            'phone': '',
            'location': '',
            'description': '',
            'website_type': '',
            'social': '',
            'extraction_success': False,
            'scraped_at': datetime.now().isoformat()
        }

        try:
            response = self.fetch_with_scraperapi(url)
            if not response:
                data['error'] = 'Failed to fetch'
                return data

            soup = BeautifulSoup(response, 'html.parser')

            # Determine website type
            if 'aaok.com' in url:
                data['website_type'] = 'aaok'
            elif 'aypapi.com' in url:
                data['website_type'] = 'aypapi'

            # Extract name
            name_elem = soup.select_one('.viewpostusername, .postusername, h1')
            if name_elem:
                data['name'] = name_elem.get_text(strip=True)

            # Extract age
            age_elem = soup.select_one('.viewpostage')
            if age_elem:
                age_text = age_elem.get_text(strip=True)
                age_match = re.search(r'\b(\d{2})\b', age_text)
                if age_match:
                    data['age'] = age_match.group(1)

            # Extract phone
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                if phone_clean.strip():
                    clean_phone = re.sub(r'[^0-9]', '', str(phone_clean))
                    if clean_phone and not clean_phone.startswith('1'):
                        clean_phone = '1' + clean_phone
                    data['phone'] = clean_phone

            # Extract location
            location_elem = soup.select_one('.viewpostlocation')
            if location_elem:
                data['location'] = location_elem.get_text(strip=True)

            # Extract description
            desc_elem = soup.select_one('.viewposttext, .posttext')
            if desc_elem:
                data['description'] = desc_elem.get_text(strip=True)[:1000]  # Limit length

            # Mark as successful if we got meaningful data
            if data['name'] or data['age'] or data['phone']:
                data['extraction_success'] = True

        except Exception as e:
            data['error'] = str(e)
            self.logger.error(f"Error extracting data from {url}: {e}")

        return data

    def phase3_scrape_profiles(self, urls_to_scrape: List[str], max_workers: int = 3) -> List[Dict]:
        """Phase 3: Full scraping of deduplicated URLs"""
        self.logger.info("🚀 PHASE 3: FULL PROFILE SCRAPING")
        self.logger.info("=" * 60)

        if not urls_to_scrape:
            self.logger.warning("No URLs to scrape")
            return []

        self.logger.info(f"Scraping {len(urls_to_scrape)} URLs with {max_workers} workers")

        results = []
        completed = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_url = {
                executor.submit(self.extract_full_data, url): url
                for url in urls_to_scrape
            }

            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    result = future.result()
                    results.append(result)
                    completed += 1

                    if completed % 10 == 0:
                        self.logger.info(f"   Progress: {completed}/{len(urls_to_scrape)} completed")

                except Exception as e:
                    self.logger.error(f"Error processing {url}: {e}")
                    results.append({
                        'url': url,
                        'error': str(e),
                        'extraction_success': False,
                        'scraped_at': datetime.now().isoformat()
                    })

        self.logger.info("=" * 60)
        self.logger.info(f"✅ Phase 3 Complete: {len(results)} profiles processed")

        return results

    def save_results(self, results: List[Dict]):
        """Save results to Excel file"""
        try:
            df = pd.DataFrame(results)

            # Create summary statistics
            successful = df[df['extraction_success'] == True]
            failed = df[df['extraction_success'] == False]

            summary_data = {
                'Metric': [
                    'Total URLs Processed',
                    'Successful Extractions',
                    'Failed Extractions',
                    'Success Rate (%)',
                    'URLs with Names',
                    'URLs with Ages',
                    'URLs with Phones',
                    'AAOK URLs',
                    'AYPAPI URLs'
                ],
                'Value': [
                    len(df),
                    len(successful),
                    len(failed),
                    f"{(len(successful) / len(df)) * 100:.1f}%" if len(df) > 0 else "0%",
                    len(df[df['name'].notna() & (df['name'].str.strip() != '')]),
                    len(df[df['age'].notna() & (df['age'].str.strip() != '')]),
                    len(df[df['phone'].notna() & (df['phone'].str.strip() != '')]),
                    len(df[df['website_type'] == 'aaok']),
                    len(df[df['website_type'] == 'aypapi'])
                ]
            }

            summary_df = pd.DataFrame(summary_data)

            # Save to Excel with multiple sheets
            with pd.ExcelWriter(self.results_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Results', index=False)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)

                # Add deduplication info if available
                if os.path.exists(self.phone_db_file):
                    try:
                        with open(self.phone_db_file, 'r') as f:
                            phone_data = json.load(f)

                        dedup_summary = pd.DataFrame({
                            'Deduplication Info': [
                                'Total Known Phones',
                                'Phone-to-URL Mappings',
                                'URL-to-Phone Mappings',
                                'Database Last Updated'
                            ],
                            'Value': [
                                len(phone_data.get('known_phones', [])),
                                len(phone_data.get('phone_to_url', {})),
                                len(phone_data.get('url_to_phone', {})),
                                phone_data.get('last_updated', 'Unknown')
                            ]
                        })

                        dedup_summary.to_excel(writer, sheet_name='Deduplication', index=False)
                    except:
                        pass

            self.logger.info(f"💾 Results saved to {self.results_file}")
            self.logger.info(f"📊 Summary: {len(successful)}/{len(df)} successful ({len(successful)/len(df)*100:.1f}%)")

            return True

        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
            return False

    def scrape_city(self, state_name: str, city_name: str, include_no_phone: bool = True, max_workers: int = 3):
        """Complete scraping process for a new city"""
        self.logger.info(f"🎯 Starting complete scraping for {city_name}, {state_name}")
        self.setup_filenames(state_name, city_name)

        start_time = time.time()

        try:
            # Phase 1: Extract URLs
            all_urls = self.phase1_extract_urls(state_name, city_name)
            if not all_urls:
                self.logger.error("❌ No URLs extracted, stopping")
                return False

            # Phase 2: Deduplicate by phone
            phone_groups = self.phase2_deduplicate_phones(all_urls)
            if not phone_groups:
                self.logger.error("❌ Phone deduplication failed, stopping")
                return False

            # Determine URLs to scrape
            urls_to_scrape = phone_groups['unique_urls']
            if include_no_phone and phone_groups['no_phone_urls']:
                urls_to_scrape.extend(phone_groups['no_phone_urls'])

            if not urls_to_scrape:
                self.logger.error("❌ No URLs to scrape after deduplication")
                return False

            # Phase 3: Full scraping
            results = self.phase3_scrape_profiles(urls_to_scrape, max_workers)
            if not results:
                self.logger.error("❌ No results from scraping")
                return False

            # Save results
            success = self.save_results(results)

            end_time = time.time()
            processing_time = end_time - start_time

            if success:
                self.logger.info("=" * 60)
                self.logger.info("🎉 SCRAPING COMPLETED SUCCESSFULLY!")
                self.logger.info(f"⏱️  Total time: {processing_time/60:.1f} minutes")
                self.logger.info(f"📊 Results: {len(results)} profiles")
                self.logger.info(f"💾 Saved to: {self.results_file}")
                self.logger.info("=" * 60)
                return True
            else:
                self.logger.error("❌ Failed to save results")
                return False

        except KeyboardInterrupt:
            self.logger.info("⏹️  Interrupted by user, saving progress...")
            self.save_phone_database()
            return False
        except Exception as e:
            self.logger.error(f"❌ Scraping failed: {e}")
            return False


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='New City Deduplication Scraper - From scratch for any city')
    parser.add_argument('state', help='State name (e.g., "Alabama", "New York")')
    parser.add_argument('city', help='City name (e.g., "Birmingham", "New York City")')
    parser.add_argument('--max-workers', type=int, default=3,
                       help='Number of parallel workers (default: 3)')
    parser.add_argument('--phone-batch-size', type=int, default=15,
                       help='Batch size for phone extraction (default: 15)')
    parser.add_argument('--phone-workers', type=int, default=3,
                       help='Workers for phone extraction (default: 3)')
    parser.add_argument('--exclude-no-phone', action='store_true',
                       help='Exclude URLs with no phone numbers from final scraping')
    parser.add_argument('--clean', action='store_true',
                       help='Clean existing files and start fresh')

    args = parser.parse_args()

    # Create scraper
    scraper = NewCityDeduplicationScraper()
    scraper.phone_extraction_batch_size = args.phone_batch_size
    scraper.phone_extraction_workers = args.phone_workers

    # Setup filenames
    scraper.setup_filenames(args.state, args.city)

    # Clean files if requested
    if args.clean:
        files_to_clean = [
            scraper.urls_file,
            scraper.phone_db_file,
            scraper.results_file,
            'new_city_scraper.log'
        ]

        for file in files_to_clean:
            try:
                if file and os.path.exists(file):
                    os.remove(file)
                    print(f"🗑️  Cleaned: {file}")
            except Exception as e:
                print(f"Failed to clean {file}: {e}")

        print("Cleanup completed. Starting fresh.")

    print(f"🚀 New City Deduplication Scraper")
    print("=" * 50)
    print(f"Target: {args.city}, {args.state}")
    print(f"ScraperAPI Key: {scraper.scraperapi_key[:20]}...")
    print(f"Phone deduplication enabled")
    print(f"Workers: {args.max_workers}")
    print()

    # Run complete scraping process
    success = scraper.scrape_city(
        state_name=args.state,
        city_name=args.city,
        include_no_phone=not args.exclude_no_phone,
        max_workers=args.max_workers
    )

    if success:
        print("\n✅ SUCCESS: City scraping completed!")
        return 0
    else:
        print("\n❌ FAILED: City scraping failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
