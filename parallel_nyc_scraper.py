#!/usr/bin/env python3
"""
Parallel NYC Boroughs Scraper - Multi-worker parallel processing system
Scrapes the 5 NYC boroughs using multiple workers for faster processing
"""

import sys
import os
import json
import time
import threading
import argparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Optional
from datetime import datetime
import pandas as pd
from pathlib import Path

from nyc_boroughs_scraper import NYCBoroughsScraper

class ParallelNYCScraper:
    def __init__(self, mistral_api_key: str = None, max_workers: int = 3):
        """Initialize parallel NYC scraper"""
        self.mistral_api_key = mistral_api_key or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"
        self.max_workers = max_workers
        self.progress_file = "parallel_nyc_progress.json"
        self.results_lock = threading.Lock()
        self.all_scraped_data = []
        self.completed_combinations = set()
        
        # Setup logging
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - [Worker-%(thread)d] - %(message)s',
            handlers=[
                logging.FileHandler('parallel_scraper.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def get_remaining_combinations(self) -> List[Dict[str, str]]:
        """Get remaining borough-source combinations to process"""
        # Create a temporary scraper to get all combinations
        temp_scraper = NYCBoroughsScraper(self.mistral_api_key)
        all_combinations = temp_scraper.get_nyc_boroughs()
        
        # Load existing progress
        completed = set()
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r') as f:
                    progress_data = json.load(f)
                    completed = set(progress_data.get('completed_combinations', []))
                    self.logger.info(f"Loaded {len(completed)} completed combinations from progress file")
            except Exception as e:
                self.logger.warning(f"Could not load progress file: {e}")
        
        # Filter out completed combinations
        remaining = []
        for combo in all_combinations:
            combo_key = f"{combo['state']}_{combo['city']}_{combo['source']}"
            if combo_key not in completed:
                remaining.append(combo)
        
        self.logger.info(f"Found {len(remaining)} remaining combinations to process")
        return remaining
    
    def save_progress(self, completed_combination: str = None):
        """Thread-safe progress saving"""
        with self.results_lock:
            if completed_combination:
                self.completed_combinations.add(completed_combination)
            
            progress_data = {
                'timestamp': datetime.now().isoformat(),
                'completed_combinations': list(self.completed_combinations),
                'total_records': len(self.all_scraped_data),
                'max_workers': self.max_workers
            }
            
            try:
                with open(self.progress_file, 'w') as f:
                    json.dump(progress_data, f, indent=2)
                self.logger.info(f"Progress saved: {len(self.completed_combinations)} completed, {len(self.all_scraped_data)} total records")
            except Exception as e:
                self.logger.error(f"Failed to save progress: {e}")
    
    def scrape_single_combination(self, combination: Dict[str, str], worker_id: int) -> Optional[List[Dict]]:
        """Scrape a single borough-source combination"""
        borough_name = combination['city']
        source = combination['source']
        combo_key = f"{combination['state']}_{borough_name}_{source}"
        
        self.logger.info(f"Worker {worker_id} starting: {borough_name} from {source}")
        
        try:
            # Create a dedicated scraper instance for this worker
            worker_scraper = NYCBoroughsScraper(self.mistral_api_key)
            worker_scraper.request_delay = 1.0  # Slightly faster for parallel processing
            
            # Scrape this specific combination
            combination_data = worker_scraper.scrape_city_pages(combination)
            
            if combination_data:
                self.logger.info(f"Worker {worker_id} completed {borough_name} ({source}): {len(combination_data)} records")
                
                # Thread-safe data collection
                with self.results_lock:
                    self.all_scraped_data.extend(combination_data)
                
                # Save individual checkpoint
                checkpoint_name = f"parallel_nyc_{borough_name}_{source}_checkpoint.xlsx"
                try:
                    df = pd.DataFrame(combination_data)
                    df.to_excel(checkpoint_name, index=False)
                    self.logger.info(f"Worker {worker_id} saved checkpoint: {checkpoint_name}")
                except Exception as e:
                    self.logger.warning(f"Worker {worker_id} failed to save checkpoint: {e}")
                
                # Update progress
                self.save_progress(combo_key)
                
                return combination_data
            else:
                self.logger.warning(f"Worker {worker_id} found no data for {borough_name} ({source})")
                self.save_progress(combo_key)  # Mark as completed even if no data
                return []
                
        except Exception as e:
            self.logger.error(f"Worker {worker_id} failed on {borough_name} ({source}): {e}")
            return None
    
    def run_parallel_scraping(self) -> bool:
        """Run parallel scraping of remaining combinations"""
        self.logger.info(f"Starting parallel NYC boroughs scraping with {self.max_workers} workers")
        
        # Get remaining combinations
        remaining_combinations = self.get_remaining_combinations()
        
        if not remaining_combinations:
            self.logger.info("No remaining combinations to process")
            return True
        
        self.logger.info(f"Processing {len(remaining_combinations)} combinations in parallel:")
        for combo in remaining_combinations:
            self.logger.info(f"  - {combo['city']} from {combo['source']}")
        
        # Process combinations in parallel
        successful_workers = 0
        failed_workers = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_combo = {
                executor.submit(self.scrape_single_combination, combo, i): combo 
                for i, combo in enumerate(remaining_combinations)
            }
            
            # Process completed tasks
            for future in as_completed(future_to_combo):
                combo = future_to_combo[future]
                try:
                    result = future.result()
                    if result is not None:
                        successful_workers += 1
                        self.logger.info(f"✓ Completed: {combo['city']} from {combo['source']}")
                    else:
                        failed_workers += 1
                        self.logger.error(f"✗ Failed: {combo['city']} from {combo['source']}")
                except Exception as e:
                    failed_workers += 1
                    self.logger.error(f"✗ Exception in {combo['city']} from {combo['source']}: {e}")
        
        self.logger.info(f"Parallel processing completed: {successful_workers} successful, {failed_workers} failed")
        return failed_workers == 0
    
    def save_final_results(self) -> str:
        """Save final consolidated results"""
        if not self.all_scraped_data:
            self.logger.warning("No data to save")
            return None
        
        try:
            # Create DataFrame
            df = pd.DataFrame(self.all_scraped_data)
            
            # Reorder columns for better readability
            column_order = [
                'state', 'city', 'source', 'title', 'name', 'age', 'phone', 
                'description', 'social_media', 'email', 'website', 'posted_date', 
                'post_id', 'url', 'search_url', 'scraped_at'
            ]
            
            # Reorder columns (only include existing columns)
            existing_columns = [col for col in column_order if col in df.columns]
            remaining_columns = [col for col in df.columns if col not in existing_columns]
            final_columns = existing_columns + remaining_columns
            df = df[final_columns]
            
            # Sort by state, city, source, then scraped_at
            df = df.sort_values(['state', 'city', 'source', 'scraped_at'])
            
            # Save to Excel
            output_file = "parallel_nyc_boroughs_final.xlsx"
            df.to_excel(output_file, index=False)
            
            self.logger.info(f"Final results saved to {output_file}: {len(df)} records")
            
            # Clean up progress file on successful completion
            try:
                if os.path.exists(self.progress_file):
                    os.remove(self.progress_file)
                    self.logger.info("Progress file cleaned up after successful completion")
            except Exception as e:
                self.logger.warning(f"Could not clean up progress file: {e}")
            
            return output_file
            
        except Exception as e:
            self.logger.error(f"Failed to save final results: {e}")
            return None

def main():
    """Main entry point for parallel NYC boroughs scraper"""
    parser = argparse.ArgumentParser(description='Parallel NYC Boroughs Web Scraper')
    parser.add_argument('--workers', type=int, default=3, help='Number of parallel workers (default: 3)')
    parser.add_argument('--mistral-key', help='Mistral AI API key for enhanced text extraction')
    parser.add_argument('--clean-progress', action='store_true', help='Clean up progress files and start fresh')
    
    args = parser.parse_args()
    
    # Validate worker count
    if args.workers < 1 or args.workers > 10:
        print("Error: Number of workers must be between 1 and 10")
        return 1
    
    # Get Mistral API key
    mistral_key = args.mistral_key or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"
    
    # Clean progress files if requested
    if args.clean_progress:
        progress_file = "parallel_nyc_progress.json"
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print(f"Cleaned up progress file: {progress_file}")
        
        # Also clean checkpoint files
        import glob
        checkpoint_pattern = "parallel_nyc_*_checkpoint.xlsx"
        checkpoint_files = glob.glob(checkpoint_pattern)
        for cf in checkpoint_files:
            os.remove(cf)
            print(f"Cleaned up checkpoint file: {cf}")
        
        print("Progress files cleaned. Starting fresh.")
    
    # Create and run parallel scraper
    scraper = ParallelNYCScraper(mistral_api_key=mistral_key, max_workers=args.workers)
    
    print(f"Starting parallel NYC boroughs scraping with {args.workers} workers...")
    print("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
    print("Features: Age ≤30, Women only, Mistral AI, Min 25 pages per combination")
    print()
    
    start_time = time.time()
    
    success = scraper.run_parallel_scraping()
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    if success:
        output_file = scraper.save_final_results()
        
        print(f"\n✓ Parallel scraping completed successfully!")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Total records collected: {len(scraper.all_scraped_data)}")
        
        if output_file:
            print(f"Final results saved to: {output_file}")
        
        # Show summary by source
        aaok_count = len([r for r in scraper.all_scraped_data if r.get('source') == 'aaok'])
        aypapi_count = len([r for r in scraper.all_scraped_data if r.get('source') == 'aypapi'])
        print(f"  - aaok.com: {aaok_count} records")
        print(f"  - aypapi.com: {aypapi_count} records")
        
        # Show summary by borough
        boroughs = list(set(r.get('city') for r in scraper.all_scraped_data))
        print(f"NYC Boroughs processed: {len(boroughs)}")
        for borough in sorted(boroughs):
            borough_count = len([r for r in scraper.all_scraped_data if r.get('city') == borough])
            print(f"  - {borough}: {borough_count} records")
        
        return 0
    else:
        print(f"\n✗ Parallel scraping completed with some failures")
        print(f"Processing time: {processing_time/60:.1f} minutes")
        print(f"Partial results collected: {len(scraper.all_scraped_data)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
