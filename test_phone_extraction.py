#!/usr/bin/env python3
"""
Test phone number extraction from viewposttelephone div
"""

from bs4 import BeautifulSoup
import re

def test_phone_extraction():
    """Test phone extraction with the provided HTML structure"""

    # Test HTML from the user
    html_sample = """
    <div class="viewposttelephone">
        <a href="tel:3477042905" data-transition="slide" class="normal" data-analytic="click">
             ************
        </a>
    </div>
    """

    soup = BeautifulSoup(html_sample, 'html.parser')

    print("Testing phone extraction methods:")
    print("=" * 50)

    # Method 1: Current scraper method - get_text() from entire div
    phone_elem = soup.select_one('.viewposttelephone')
    if phone_elem:
        phone_text = phone_elem.get_text(strip=True)
        phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
        print(f"Method 1 (current): '{phone_text}' -> '{phone_clean.strip()}'")

    # Method 2: Get text from the <a> tag specifically
    phone_link = soup.select_one('.viewposttelephone a')
    if phone_link:
        phone_text2 = phone_link.get_text(strip=True)
        phone_clean2 = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text2)
        print(f"Method 2 (from <a>): '{phone_text2}' -> '{phone_clean2.strip()}'")

    # Method 3: Get from href attribute
    phone_link = soup.select_one('.viewposttelephone a[href^="tel:"]')
    if phone_link:
        tel_href = phone_link.get('href')
        if tel_href:
            # Extract numbers from tel: link
            phone_from_href = tel_href.replace('tel:', '')
            # Format as XXX-XXX-XXXX
            if len(phone_from_href) == 10:
                formatted_phone = f"{phone_from_href[:3]}-{phone_from_href[3:6]}-{phone_from_href[6:]}"
                print(f"Method 3 (from href): '{tel_href}' -> '{formatted_phone}'")

    print("\n" + "=" * 50)

    # Test with variations
    test_cases = [
        # Case 1: Standard format
        """<div class="viewposttelephone">
            <a href="tel:3477042905">************</a>
        </div>""",

        # Case 2: Different formatting
        """<div class="viewposttelephone">
            <a href="tel:9177813697">(*************</a>
        </div>""",

        # Case 3: Just text without link
        """<div class="viewposttelephone">
            ************
        </div>""",

        # Case 4: With extra whitespace
        """<div class="viewposttelephone">
            <a href="tel:5551234567">
                ************
            </a>
        </div>""",
    ]

    print("Testing various formats:")
    for i, test_html in enumerate(test_cases, 1):
        soup = BeautifulSoup(test_html, 'html.parser')

        # Current method
        phone_elem = soup.select_one('.viewposttelephone')
        if phone_elem:
            phone_text = phone_elem.get_text(strip=True)
            phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
            print(f"Case {i}: '{phone_text}' -> '{phone_clean.strip()}'")

if __name__ == "__main__":
    test_phone_extraction()
