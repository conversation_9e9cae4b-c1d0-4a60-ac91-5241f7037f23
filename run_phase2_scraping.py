#!/usr/bin/env python3
"""
Phase 2 Scraping Runner - Run targeted profile scraping with ScrapingDog
Uses API key rotation and progress saving for the targeted_all_urls_20250819_001221.json file
"""

import json
import sys
import os
from datetime import datetime
from targeted_city_scraper import TargetedCityScraper

def load_urls_from_file(filename: str):
    """Load URLs from the JSON file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"📂 Loaded {len(data)} URLs from {filename}")
        return data
    except Exception as e:
        print(f"❌ Error loading URLs from {filename}: {e}")
        return None

def main():
    """Main function to run phase 2 scraping"""

    # Configuration
    urls_file = "targeted_all_urls_20250819_001221.json"
    resume_file = None  # Set to progress file name if resuming

    print("🎯 Phase 2 Targeted City Scraping with ScrapingDog")
    print("=" * 60)

    # Check command line arguments for resume functionality
    if len(sys.argv) > 1:
        if sys.argv[1] == "--resume" and len(sys.argv) > 2:
            resume_file = sys.argv[2]
            print(f"🔄 Resuming from progress file: {resume_file}")
            if not os.path.exists(resume_file):
                print(f"❌ Resume file {resume_file} not found!")
                return
        elif sys.argv[1] == "--help" or sys.argv[1] == "-h":
            print("Usage:")
            print(f"  python {sys.argv[0]}                    # Start fresh")
            print(f"  python {sys.argv[0]} --resume <file>    # Resume from progress file")
            print(f"  python {sys.argv[0]} --help             # Show this help")
            return
        else:
            print("❌ Invalid arguments!")
            print("Usage:")
            print(f"  python {sys.argv[0]}                    # Start fresh")
            print(f"  python {sys.argv[0]} --resume <file>    # Resume from progress file")
            print(f"  python {sys.argv[0]} --help             # Show this help")
            return

    # Load URLs
    if not os.path.exists(urls_file):
        print(f"❌ URLs file {urls_file} not found!")
        return

    profile_urls = load_urls_from_file(urls_file)
    if not profile_urls:
        print("❌ No URLs loaded, exiting.")
        return

    # Initialize scraper with ScrapingDog API keys
    scrapingdog_api_keys = [
        "68a390dbc2920968e9acce34",
        # Add more API keys here if you have them
    ]

    print("🚀 Initializing scraper with ScrapingDog API keys")
    print(f"📊 Total API keys available: {len(scrapingdog_api_keys)}")
    print(f"🎯 Max requests per key: 900")
    print(f"🎯 Processing limit: 200 URLs")
    print(f"👥 Using 4 workers for parallel processing")
    print(f"💾 Progress will be saved after processing 200 URLs")

    try:
        scraper = TargetedCityScraper(
            scrapingdog_api_keys=scrapingdog_api_keys,
            mistral_api_key="dvP7AR4TRAdCe4brUOeElSyXxOqwVVso",
            max_urls_to_process=200
        )
    except Exception as e:
        print(f"❌ Failed to initialize scraper: {e}")
        return

    # Run phase 2 scraping
    print(f"\n🎯 Starting Phase 2 scraping")
    print(f"📂 Total URLs available: {len(profile_urls)}")
    print(f"🎯 Will process: 200 URLs (or resume from progress)")
    if resume_file:
        print(f"🔄 Resuming from: {resume_file}")
    print("=" * 60)
    print("⚠️  Press Ctrl+C at any time to stop and save progress")
    print("💾 Progress is saved automatically after 200 URLs")
    print("👥 Using 4 workers for faster processing")
    print("-" * 60)

    try:
        results = scraper.phase2_scrape_all_profiles(
            profile_urls=profile_urls,
            max_workers=4,  # Use 4 workers for parallel processing
            resume_file=resume_file
        )

        # Save final results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"phase2_results_{timestamp}.json"

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)

        # Also save a summary file
        summary = {
            'total_profiles_scraped': len(results),
            'duplicates_skipped': scraper.stats.get('duplicates_skipped', 0),
            'failed_extractions': scraper.stats.get('failed_extractions', 0),
            'api_requests_made': scraper.stats.get('api_requests', 0),
            'api_keys_used': scraper.current_key_index + 1,
            'final_api_key_requests': scraper.current_key_requests,
            'completion_time': timestamp
        }

        summary_file = f"phase2_summary_{timestamp}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"\n🎉 SCRAPING COMPLETED!")
        print(f"📊 Final Results:")
        print(f"   ✅ Total profiles scraped: {len(results)}")
        print(f"   🔄 Duplicates skipped: {scraper.stats.get('duplicates_skipped', 0)}")
        print(f"   ❌ Failed extractions: {scraper.stats.get('failed_extractions', 0)}")
        print(f"   🎯 API requests made: {scraper.stats.get('api_requests', 0)}")
        print(f"   🔑 API keys used: {scraper.current_key_index + 1}/{len(scrapingdog_api_keys)}")
        print(f"   💾 Results saved to: {results_file}")
        print(f"   📋 Summary saved to: {summary_file}")

        # Save to Excel if results exist
        if results:
            try:
                excel_file = scraper.save_results_to_excel(results)
                print(f"   📊 Excel file saved: {excel_file}")
            except Exception as e:
                print(f"   ⚠️  Excel save failed: {e}")

    except KeyboardInterrupt:
        print("\n⚠️ Scraping interrupted by user")
        print("💾 Progress has been saved automatically")
        print("🔄 You can resume later using: python run_phase2_scraping.py --resume <progress_file>")
    except Exception as e:
        print(f"\n❌ Scraping error: {e}")
        print("💾 Progress has been saved automatically")
        print("🔄 You can resume later using: python run_phase2_scraping.py --resume <progress_file>")
        print("📋 For help, run: python run_phase2_scraping.py --help")

if __name__ == "__main__":
    main()
