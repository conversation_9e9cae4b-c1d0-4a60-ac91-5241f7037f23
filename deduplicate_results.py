#!/usr/bin/env python3
"""
Deduplication Script for ScraperAPI Results
Removes duplicate phone numbers to get unique people only
"""

import pandas as pd
import os
from datetime import datetime

def deduplicate_by_phone(input_file, output_file=None):
    """
    Deduplicate results by phone number, keeping the most recent/complete record
    """

    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        return False

    print(f"📂 Loading data from: {input_file}")

    # Load the data
    try:
        df = pd.read_excel(input_file)
        print(f"📊 Total records loaded: {len(df)}")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return False

    # Show initial stats
    print(f"📱 Unique phone numbers: {df['phone'].nunique()}")
    print(f"🔄 Total duplicates (by phone): {len(df) - df['phone'].nunique()}")

    # Filter out empty phone numbers first
    df_with_phones = df[df['phone'].notna() & (df['phone'] != '') & (df['phone'] != 'nan')]
    print(f"📱 Records with valid phone numbers: {len(df_with_phones)}")

    # Deduplication strategy: Keep the record with most complete data for each phone number
    def select_best_record(group):
        """Select the best record from duplicates based on data completeness"""

        # Score each record based on data completeness
        group = group.copy()
        group['completeness_score'] = 0

        # Add points for each non-empty field
        for col in ['name', 'age', 'location', 'description']:
            if col in group.columns:
                group['completeness_score'] += (~group[col].isna() & (group[col] != '') & (group[col] != 'nan')).astype(int)

        # Add points for successful extraction
        if 'extraction_success' in group.columns:
            group['completeness_score'] += group['extraction_success'].astype(int)

        # Add points for longer descriptions (more detailed profiles)
        if 'description' in group.columns:
            group['completeness_score'] += (group['description'].str.len().fillna(0) > 100).astype(int)

        # Sort by completeness score (desc), then by scraped_at (most recent first)
        if 'scraped_at' in group.columns:
            group = group.sort_values(['completeness_score', 'scraped_at'], ascending=[False, False])
        else:
            group = group.sort_values('completeness_score', ascending=False)

        # Return the best record
        return group.iloc[0]

    print("🔄 Deduplicating by phone number...")

    # Group by phone number and select best record for each
    unique_df = df_with_phones.groupby('phone').apply(select_best_record).reset_index(drop=True)

    print(f"✅ Deduplication complete!")
    print(f"📊 Unique people (by phone): {len(unique_df)}")
    print(f"🗑️ Duplicates removed: {len(df_with_phones) - len(unique_df)}")
    print(f"📉 Reduction: {((len(df_with_phones) - len(unique_df)) / len(df_with_phones) * 100):.1f}%")

    # Add some statistics about the deduplicated data
    successful_extractions = len(unique_df[unique_df['extraction_success'] == True]) if 'extraction_success' in unique_df.columns else len(unique_df)
    print(f"✅ Successful extractions: {successful_extractions} ({(successful_extractions/len(unique_df)*100):.1f}%)")

    # Show data completeness
    print(f"\n📈 Data Completeness in Deduplicated Set:")
    for col in ['name', 'age', 'phone', 'location']:
        if col in unique_df.columns:
            complete_count = len(unique_df[unique_df[col].notna() & (unique_df[col] != '') & (unique_df[col] != 'nan')])
            print(f"   {col.capitalize()}: {complete_count}/{len(unique_df)} ({(complete_count/len(unique_df)*100):.1f}%)")

    # Generate output filename if not provided
    if output_file is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = f"{base_name}_deduplicated_{timestamp}.xlsx"

    # Save deduplicated results
    try:
        unique_df.to_excel(output_file, index=False)
        print(f"\n💾 Deduplicated results saved to: {output_file}")
        print(f"📊 File size: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
    except Exception as e:
        print(f"❌ Error saving file: {e}")
        return False

    # Show sample of deduplicated data
    print(f"\n📋 Sample of Unique People:")
    print("=" * 80)
    sample_df = unique_df.head(5)
    for i, (_, row) in enumerate(sample_df.iterrows(), 1):
        print(f"{i}. Name: {row['name'] if pd.notna(row['name']) else 'N/A'}")
        print(f"   Phone: {row['phone']}")
        print(f"   Age: {row['age'] if pd.notna(row['age']) else 'N/A'}")
        print(f"   Location: {row['location'] if pd.notna(row['location']) else 'N/A'}")
        print(f"   Website: {row['website_type'] if 'website_type' in row else 'N/A'}")
        print()

    # Show phone number frequency analysis
    print("📞 Phone Number Analysis (Top 10 most frequent):")
    phone_counts = df_with_phones['phone'].value_counts().head(10)
    for phone, count in phone_counts.items():
        if count > 1:
            print(f"   {phone}: {count} posts (reduced to 1 unique person)")

    return True

def main():
    """Main function to run deduplication"""

    input_file = "scraperapi_results_18731_urls_20250812_014155_final.xlsx"

    print("🔄 Phone Number Deduplication Tool")
    print("=" * 50)
    print(f"📂 Input file: {input_file}")

    # Run deduplication
    success = deduplicate_by_phone(input_file)

    if success:
        print("\n🎉 Deduplication completed successfully!")
        print("📊 You now have a file with unique people only (no duplicate phone numbers)")
    else:
        print("\n❌ Deduplication failed")

    return success

if __name__ == "__main__":
    main()
