#!/usr/bin/env python3
"""
Deduplication Scraper: Enhanced scraper that avoids duplicate phone numbers
First extracts phone numbers from URLs, then deduplicates before full scraping
"""

import sys
import os
import json
import time
import argparse
import re
import threading
from typing import List, Dict, Optional, Set
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from bs4 import BeautifulSoup
import pandas as pd

from scraperapi_scraper import ScraperAPIScraper


class DeduplicationScraper(ScraperAPIScraper):
    def __init__(self, api_keys: List[str], max_workers: int = 3, enable_social_media: bool = True,
                 mistral_api_key: str = None, phone_db_file: str = "phone_numbers_db.json"):
        """Initialize deduplication scraper with phone number tracking"""
        super().__init__(api_keys, max_workers, enable_social_media, mistral_api_key)

        self.phone_db_file = phone_db_file
        self.known_phones: Set[str] = set()
        self.phone_to_url: Dict[str, str] = {}  # Map phone numbers to first URL that had them
        self.url_to_phone: Dict[str, str] = {}  # Map URLs to their phone numbers
        self.phone_lock = threading.Lock()

        # Load existing phone database
        self.load_phone_database()

        # Deduplication settings
        self.phone_extraction_batch_size = 20  # URLs to extract phones from in parallel
        self.phone_extraction_workers = 5
        self.skip_phone_extraction = False  # Set to True to skip phone extraction phase

    def load_phone_database(self):
        """Load existing phone number database"""
        if os.path.exists(self.phone_db_file):
            try:
                with open(self.phone_db_file, 'r') as f:
                    phone_data = json.load(f)

                self.known_phones = set(phone_data.get('known_phones', []))
                self.phone_to_url = phone_data.get('phone_to_url', {})
                self.url_to_phone = phone_data.get('url_to_phone', {})

                print(f"📱 Loaded {len(self.known_phones)} known phone numbers from database")

            except Exception as e:
                print(f"⚠️  Failed to load phone database: {e}")
                self.known_phones = set()
                self.phone_to_url = {}
                self.url_to_phone = {}
        else:
            print("📱 No existing phone database found, starting fresh")

    def save_phone_database(self):
        """Save phone number database to file"""
        try:
            phone_data = {
                'known_phones': list(self.known_phones),
                'phone_to_url': self.phone_to_url,
                'url_to_phone': self.url_to_phone,
                'last_updated': datetime.now().isoformat()
            }

            with open(self.phone_db_file, 'w') as f:
                json.dump(phone_data, f, indent=2)

            print(f"📱 Saved phone database with {len(self.known_phones)} numbers")

        except Exception as e:
            print(f"⚠️  Failed to save phone database: {e}")

    def extract_phone_only(self, url: str) -> Optional[str]:
        """Extract only phone number from a single URL (lightweight extraction)"""
        try:
            # Get HTML content
            response = self.fetch_with_scraperapi(url)
            if not response:
                return None

            soup = BeautifulSoup(response, 'html.parser')

            # Extract phone from viewposttelephone (same as main scraper)
            phone_elem = soup.select_one('.viewposttelephone')
            if phone_elem:
                phone_text = phone_elem.get_text(strip=True)
                # Clean phone number (same as main scraper)
                phone_clean = re.sub(r'[^\d\-\(\)\s\.]', '', phone_text)
                if phone_clean.strip():
                    # Process phone number (clean and add country code)
                    clean_phone = re.sub(r'[^0-9]', '', str(phone_clean))
                    if clean_phone and not clean_phone.startswith('1'):
                        clean_phone = '1' + clean_phone
                    return clean_phone

            return None

        except Exception as e:
            print(f"⚠️  Error extracting phone from {url}: {e}")
            return None

    def extract_phones_batch(self, urls: List[str]) -> Dict[str, str]:
        """Extract phone numbers from a batch of URLs in parallel"""
        phone_results = {}

        with ThreadPoolExecutor(max_workers=self.phone_extraction_workers) as executor:
            # Submit all URLs for phone extraction
            future_to_url = {
                executor.submit(self.extract_phone_only, url): url
                for url in urls
            }

            # Collect results
            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    phone = future.result()
                    if phone:
                        phone_results[url] = phone
                        print(f"📱 Found phone {phone} for URL {url[-20:]}")
                    else:
                        print(f"📱 No phone found for URL {url[-20:]}")

                except Exception as e:
                    print(f"⚠️  Error processing {url}: {e}")

        return phone_results

    def build_phone_database(self, urls: List[str]) -> Dict[str, List[str]]:
        """Build phone number database from URLs and return deduplicated URL groups"""
        print(f"\n🔍 Phase 1: Building phone number database from {len(urls)} URLs...")

        if self.skip_phone_extraction:
            print("⏭️  Skipping phone extraction phase")
            return {"unique_urls": urls}

        # Process URLs in batches
        new_phones = 0
        duplicate_phones = 0
        no_phone_count = 0

        for i in range(0, len(urls), self.phone_extraction_batch_size):
            batch = urls[i:i + self.phone_extraction_batch_size]
            batch_num = (i // self.phone_extraction_batch_size) + 1
            total_batches = (len(urls) + self.phone_extraction_batch_size - 1) // self.phone_extraction_batch_size

            print(f"\n📦 Processing batch {batch_num}/{total_batches} ({len(batch)} URLs)")

            # Extract phones from this batch
            batch_phones = self.extract_phones_batch(batch)

            # Update database
            with self.phone_lock:
                for url, phone in batch_phones.items():
                    if phone not in self.known_phones:
                        # New phone number
                        self.known_phones.add(phone)
                        self.phone_to_url[phone] = url
                        self.url_to_phone[url] = phone
                        new_phones += 1
                    else:
                        # Duplicate phone number
                        self.url_to_phone[url] = phone
                        duplicate_phones += 1

                # Count URLs with no phone
                for url in batch:
                    if url not in batch_phones:
                        no_phone_count += 1

            # Save progress periodically
            if batch_num % 5 == 0:
                self.save_phone_database()

            # Rate limiting between batches
            time.sleep(1)

        # Final save
        self.save_phone_database()

        # Create deduplicated groups
        phone_groups = {}
        unique_urls = []
        duplicate_urls = []
        no_phone_urls = []

        for url in urls:
            if url in self.url_to_phone:
                phone = self.url_to_phone[url]
                if self.phone_to_url[phone] == url:
                    # This is the first URL for this phone number
                    unique_urls.append(url)
                else:
                    # This is a duplicate
                    duplicate_urls.append(url)
            else:
                # No phone found
                no_phone_urls.append(url)

        phone_groups = {
            "unique_urls": unique_urls,
            "duplicate_urls": duplicate_urls,
            "no_phone_urls": no_phone_urls
        }

        # Print summary
        print(f"\n📊 Phone Database Summary:")
        print(f"   • Total URLs processed: {len(urls)}")
        print(f"   • New phone numbers found: {new_phones}")
        print(f"   • Duplicate phone numbers: {duplicate_phones}")
        print(f"   • URLs with no phone: {no_phone_count}")
        print(f"   • Total known phones: {len(self.known_phones)}")
        print(f"\n🎯 Deduplication Results:")
        print(f"   • Unique URLs to scrape: {len(unique_urls)}")
        print(f"   • Duplicate URLs skipped: {len(duplicate_urls)}")
        print(f"   • No phone URLs to scrape: {len(no_phone_urls)}")
        print(f"   • Total reduction: {len(duplicate_urls)} URLs ({len(duplicate_urls)/len(urls)*100:.1f}%)")

        return phone_groups

    def process_urls_with_deduplication(self, urls: List[str], max_urls: Optional[int] = None) -> List[Dict]:
        """Process URLs with phone number deduplication"""
        print(f"🚀 Starting deduplication scraping for {len(urls)} URLs")

        # Limit URLs if specified
        if max_urls:
            urls = urls[:max_urls]
            print(f"📝 Limited to {max_urls} URLs for processing")

        # Phase 1: Build phone database and deduplicate
        phone_groups = self.build_phone_database(urls)

        # Determine which URLs to scrape
        urls_to_scrape = phone_groups["unique_urls"]

        # Optionally include no-phone URLs
        if phone_groups["no_phone_urls"]:
            print(f"\n❓ Found {len(phone_groups['no_phone_urls'])} URLs with no phone numbers")
            response = input("Include URLs with no phone numbers in full scraping? (y/n): ").lower()
            if response == 'y':
                urls_to_scrape.extend(phone_groups["no_phone_urls"])

        print(f"\n🎯 Phase 2: Full scraping of {len(urls_to_scrape)} deduplicated URLs")

        if not urls_to_scrape:
            print("⚠️  No URLs to scrape after deduplication")
            return []

        # Phase 2: Full scraping of deduplicated URLs
        results = self.process_urls_parallel(urls_to_scrape)

        # Add deduplication metadata to results
        for result in results:
            url = result.get('url', '')
            if url in self.url_to_phone:
                result['phone_deduplication'] = 'unique'
            elif url in phone_groups.get("no_phone_urls", []):
                result['phone_deduplication'] = 'no_phone'
            else:
                result['phone_deduplication'] = 'unknown'

        return results

    def get_deduplication_stats(self) -> Dict:
        """Get deduplication statistics"""
        return {
            'total_known_phones': len(self.known_phones),
            'phone_to_url_mappings': len(self.phone_to_url),
            'url_to_phone_mappings': len(self.url_to_phone),
            'database_file': self.phone_db_file,
            'database_exists': os.path.exists(self.phone_db_file)
        }

    def save_results_with_dedup_info(self, results: List[Dict], filename: str = "deduplicated_results.xlsx"):
        """Save results with deduplication information"""
        # Add deduplication information to the save
        print(f"\n💾 Saving {len(results)} results with deduplication info to {filename}")

        # Call parent save method
        success = self.save_results(results, filename)

        if success:
            # Also save deduplication summary
            dedup_stats = self.get_deduplication_stats()

            # Create summary sheet data
            summary_data = {
                'Deduplication Summary': [
                    f"Total known phone numbers: {dedup_stats['total_known_phones']}",
                    f"Phone-to-URL mappings: {dedup_stats['phone_to_url_mappings']}",
                    f"URL-to-phone mappings: {dedup_stats['url_to_phone_mappings']}",
                    f"Database file: {dedup_stats['database_file']}",
                    f"Results with deduplication: {len(results)}",
                    f"Unique phone results: {len([r for r in results if r.get('phone_deduplication') == 'unique'])}",
                    f"No phone results: {len([r for r in results if r.get('phone_deduplication') == 'no_phone'])}",
                ]
            }

            # Try to add deduplication summary to the Excel file
            try:
                with pd.ExcelWriter(filename, mode='a', engine='openpyxl') as writer:
                    pd.DataFrame(summary_data).to_excel(writer, sheet_name='Deduplication_Summary', index=False)
                print(f"📊 Added deduplication summary to {filename}")
            except Exception as e:
                print(f"⚠️  Could not add deduplication summary: {e}")

        return success


def main():
    """Main entry point for deduplication scraper"""
    parser = argparse.ArgumentParser(description='Phone Number Deduplication Scraper')
    parser.add_argument('urls_file', help='JSON file containing URLs to scrape')
    parser.add_argument('--scraperapi-keys', nargs='+', required=True,
                       help='ScraperAPI keys (can provide multiple)')
    parser.add_argument('--max-urls', type=int,
                       help='Maximum number of URLs to process')
    parser.add_argument('--max-workers', type=int, default=3,
                       help='Number of parallel workers')
    parser.add_argument('--phone-db', default='phone_numbers_db.json',
                       help='Phone number database file')
    parser.add_argument('--output', default='deduplicated_results.xlsx',
                       help='Output Excel file')
    parser.add_argument('--skip-phone-extraction', action='store_true',
                       help='Skip phone extraction phase (use existing database only)')
    parser.add_argument('--phone-batch-size', type=int, default=20,
                       help='Batch size for phone extraction')
    parser.add_argument('--phone-workers', type=int, default=5,
                       help='Number of workers for phone extraction')
    parser.add_argument('--mistral-key',
                       help='Mistral AI API key for enhanced text extraction')
    parser.add_argument('--stats-only', action='store_true',
                       help='Show deduplication statistics only')
    parser.add_argument('--clean-db', action='store_true',
                       help='Clean phone database and start fresh')

    args = parser.parse_args()

    # Clean database if requested
    if args.clean_db:
        if os.path.exists(args.phone_db):
            os.remove(args.phone_db)
            print(f"🗑️  Cleaned phone database: {args.phone_db}")
        else:
            print(f"🗑️  No phone database to clean: {args.phone_db}")
        if not args.stats_only:
            return 0

    # Create scraper
    scraper = DeduplicationScraper(
        api_keys=args.scraperapi_keys,
        max_workers=args.max_workers,
        enable_social_media=True,
        mistral_api_key=args.mistral_key,
        phone_db_file=args.phone_db
    )

    # Set deduplication parameters
    scraper.phone_extraction_batch_size = args.phone_batch_size
    scraper.phone_extraction_workers = args.phone_workers
    scraper.skip_phone_extraction = args.skip_phone_extraction

    # Show stats if requested
    if args.stats_only:
        stats = scraper.get_deduplication_stats()
        print("\n📊 Deduplication Statistics:")
        print(f"   • Known phone numbers: {stats['total_known_phones']}")
        print(f"   • Phone-to-URL mappings: {stats['phone_to_url_mappings']}")
        print(f"   • URL-to-phone mappings: {stats['url_to_phone_mappings']}")
        print(f"   • Database file: {stats['database_file']}")
        print(f"   • Database exists: {stats['database_exists']}")
        return 0

    # Load URLs from file
    if not os.path.exists(args.urls_file):
        print(f"❌ URLs file not found: {args.urls_file}")
        return 1

    try:
        with open(args.urls_file, 'r') as f:
            if args.urls_file.endswith('.json'):
                # Handle both simple list and structured JSON
                data = json.load(f)
                if isinstance(data, list):
                    urls = data
                elif isinstance(data, dict):
                    # Extract URLs from structured format (like from generic_url_scraper.py)
                    urls = []
                    for combo_data in data.values():
                        if 'urls' in combo_data:
                            urls.extend(combo_data['urls'])
                else:
                    raise ValueError("Invalid JSON format")
            else:
                # Plain text file (one URL per line)
                urls = [line.strip() for line in f.readlines() if line.strip()]

    except Exception as e:
        print(f"❌ Failed to load URLs from {args.urls_file}: {e}")
        return 1

    if not urls:
        print(f"❌ No URLs found in {args.urls_file}")
        return 1

    print(f"📂 Loaded {len(urls)} URLs from {args.urls_file}")

    # Process URLs with deduplication
    start_time = time.time()

    try:
        results = scraper.process_urls_with_deduplication(urls, args.max_urls)

        if results:
            # Save results
            success = scraper.save_results_with_dedup_info(results, args.output)

            end_time = time.time()
            processing_time = end_time - start_time

            if success:
                print(f"\n🎉 Deduplication scraping completed successfully!")
                print(f"⏱️  Processing time: {processing_time/60:.1f} minutes")
                print(f"📊 Results: {len(results)} profiles scraped")
                print(f"💾 Saved to: {args.output}")

                # Final deduplication stats
                stats = scraper.get_deduplication_stats()
                print(f"📱 Phone database: {stats['total_known_phones']} unique numbers")

                return 0
            else:
                print("❌ Failed to save results")
                return 1
        else:
            print("⚠️  No results to save")
            return 0

    except KeyboardInterrupt:
        print("\n⏹️  Interrupted by user")
        # Save phone database before exiting
        scraper.save_phone_database()
        return 1
    except Exception as e:
        print(f"❌ Scraping failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
