#!/usr/bin/env python3
"""
Fresh Batch Processor - Process fresh validated URLs in batches
Uses the newly extracted fresh URLs that should have much better success rates
"""

import sys
import os
import json
import time
import glob
import pandas as pd
from datetime import datetime
from pathlib import Path
from two_phase_city_scraper import TwoPhaseCityScraper

class FreshBatchProcessor:
    def __init__(self, scraperapi_key: str = "********************************",
                 mistral_api_key: str = "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"):
        """Initialize fresh batch processor"""

        self.scraper = TwoPhaseCityScraper(scraperapi_key, mistral_api_key)
        self.all_results = []
        self.processed_batches = 0
        self.total_profiles = 0
        self.start_time = datetime.now()

        # Progress tracking for fresh batches
        self.progress_file = "fresh_batch_processing_progress.json"
        self.combined_results_file = None

        print("🔥 Fresh Batch Processor Initialized")
        print(f"🔑 ScraperAPI: {'✅ Ready' if scraperapi_key else '❌ Missing'}")
        print(f"🤖 Mistral AI: {'✅ Ready' if self.scraper.mistral_client else '❌ Missing'}")
        print("✨ Using FRESH validated URLs for better success rates!")

    def find_fresh_batch_files(self) -> list:
        """Find all fresh batch files in order"""
        batch_files = glob.glob("fresh_batch_*.json")

        # Sort by batch number
        def extract_batch_number(filename):
            try:
                return int(filename.split('_')[2])
            except:
                return 999

        batch_files.sort(key=extract_batch_number)

        print(f"📁 Found {len(batch_files)} fresh batch files:")
        for i, filename in enumerate(batch_files):
            try:
                with open(filename, 'r') as f:
                    urls = json.load(f)
                print(f"   {i+1:2d}. {filename} ({len(urls)} URLs)")
            except:
                print(f"   {i+1:2d}. {filename} (ERROR reading file)")

        return batch_files

    def load_progress(self):
        """Load previous progress if exists"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r') as f:
                    progress = json.load(f)

                print(f"📋 Loading previous progress:")
                print(f"   Batches completed: {progress.get('batches_completed', 0)}")
                print(f"   Total profiles: {progress.get('total_profiles', 0)}")
                print(f"   Last batch: {progress.get('last_batch', 'None')}")

                return progress
            except Exception as e:
                print(f"⚠️ Error loading progress: {e}")
                return {}
        return {}

    def save_progress(self, batch_number, batch_file, batch_results):
        """Save progress after each batch"""
        progress = {
            'batches_completed': batch_number,
            'total_profiles': self.total_profiles,
            'last_batch': batch_file,
            'last_updated': datetime.now().isoformat(),
            'processing_start_time': self.start_time.isoformat(),
            'batch_results_count': len(batch_results) if batch_results else 0
        }

        with open(self.progress_file, 'w') as f:
            json.dump(progress, f, indent=2)

        print(f"💾 Progress saved: Fresh Batch {batch_number} complete")

    def combine_and_save_results(self, final=False):
        """Combine all results and save to Excel"""
        if not self.all_results:
            print("⚠️ No results to save")
            return

        # Create combined dataframe
        df = pd.DataFrame(self.all_results)

        # Remove duplicates based on phone number (final deduplication)
        if 'phone' in df.columns:
            initial_count = len(df)
            df = df.drop_duplicates(subset=['phone'], keep='first')
            deduplicated_count = len(df)
            if initial_count > deduplicated_count:
                print(f"🔄 Final deduplication: Removed {initial_count - deduplicated_count} duplicates")

        # Sort by state, city, source
        if all(col in df.columns for col in ['state', 'city', 'source']):
            df = df.sort_values(['state', 'city', 'source'])

        # Generate filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        status = "FINAL" if final else "PROGRESS"
        filename = f"fresh_batch_results_{status}_{len(df)}_profiles_{timestamp}.xlsx"

        # Column order
        column_order = ['state', 'city', 'source', 'name', 'age', 'phone', 'description',
                       'social_media', 'email', 'url', 'post_id', 'scraped_at']

        df = df.reindex(columns=[col for col in column_order if col in df.columns] +
                               [col for col in df.columns if col not in column_order])

        # Save to Excel
        df.to_excel(filename, index=False)
        self.combined_results_file = filename

        print(f"💾 Combined results saved to: {filename}")
        self.print_comprehensive_stats(df, final)

    def print_comprehensive_stats(self, df, final=False):
        """Print detailed statistics"""
        elapsed_time = datetime.now() - self.start_time

        print(f"\n{'='*60}")
        print(f"{'FINAL' if final else 'PROGRESS'} STATISTICS - Fresh Batch Processing")
        print(f"{'='*60}")
        print(f"⏱️  Processing Time: {elapsed_time}")
        print(f"📊 Fresh Batches Completed: {self.processed_batches}")
        print(f"👥 Total Profiles: {len(df)}")
        print(f"🏙️ Cities Covered: {df['city'].nunique() if 'city' in df.columns else 'N/A'}")
        print(f"🗺️  States Covered: {df['state'].nunique() if 'state' in df.columns else 'N/A'}")
        print(f"📡 Sources Used: {', '.join(df['source'].unique()) if 'source' in df.columns else 'N/A'}")

        if 'age' in df.columns:
            print(f"👶 Age Range: {df['age'].min()}-{df['age'].max()}")

        if 'phone' in df.columns:
            print(f"📱 Unique Phone Numbers: {df['phone'].nunique()}")

        # City breakdown
        if 'city' in df.columns and 'state' in df.columns:
            print(f"\n🏙️ Profiles by City:")
            city_counts = df.groupby(['state', 'city']).size().sort_values(ascending=False)
            for (state, city), count in city_counts.items():
                print(f"   {city}, {state}: {count}")

        print(f"🌐 Total API Requests: {self.scraper.stats['api_requests']}")
        print(f"✅ Successful Extractions: {self.scraper.stats['profiles_scraped']}")
        print(f"🔄 Duplicates Skipped: {self.scraper.stats['duplicates_skipped']}")
        print(f"❌ Failed Extractions: {self.scraper.stats['failed_extractions']}")

        # Success rate calculation
        total_attempts = self.scraper.stats['profiles_scraped'] + self.scraper.stats['failed_extractions']
        if total_attempts > 0:
            success_rate = (self.scraper.stats['profiles_scraped'] / total_attempts) * 100
            print(f"📈 Success Rate: {success_rate:.1f}%")

        print(f"{'='*60}")

    def process_single_fresh_batch(self, batch_file, batch_number, skip_if_processed=True):
        """Process a single fresh batch file"""
        print(f"\n🔥 Processing Fresh Batch {batch_number}: {batch_file}")
        print(f"{'='*50}")

        # Check if this batch was already processed
        if skip_if_processed:
            progress = self.load_progress()
            if progress.get('batches_completed', 0) >= batch_number:
                print(f"⏭️  Fresh Batch {batch_number} already processed, skipping...")
                return []

        try:
            # Load batch URLs
            with open(batch_file, 'r') as f:
                batch_urls = json.load(f)

            print(f"📁 Loaded {len(batch_urls)} FRESH URLs from {batch_file}")

            # Show sample of URLs being processed
            if len(batch_urls) > 0:
                print(f"📋 Sample URLs:")
                for i, url_data in enumerate(batch_urls[:3]):
                    print(f"   {i+1}. {url_data['city']}, {url_data['state']} ({url_data['source']}) - {url_data['url'][:80]}...")

            # Process this batch with better settings for fresh URLs
            batch_results = self.scraper.phase2_scrape_all_profiles(batch_urls, max_workers=2)

            # Add results to master collection
            if batch_results:
                self.all_results.extend(batch_results)
                self.total_profiles += len(batch_results)
                print(f"✅ Fresh Batch {batch_number} complete: {len(batch_results)} profiles added")
            else:
                print(f"⚠️ Fresh Batch {batch_number}: No valid profiles found")

            # Save individual batch results
            if batch_results:
                batch_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                batch_results_file = f"fresh_batch_{batch_number:02d}_results_{len(batch_results)}_profiles_{batch_timestamp}.xlsx"
                batch_df = pd.DataFrame(batch_results)
                batch_df.to_excel(batch_results_file, index=False)
                print(f"💾 Batch results saved to: {batch_results_file}")

            # Save progress
            self.processed_batches = batch_number
            self.save_progress(batch_number, batch_file, batch_results)

            # Save intermediate combined results every 2 batches
            if batch_number % 2 == 0:
                self.combine_and_save_results(final=False)

            return batch_results

        except Exception as e:
            print(f"❌ Error processing fresh batch {batch_number}: {e}")
            # Save progress even on error
            self.save_progress(batch_number, batch_file, [])
            return []

    def process_all_fresh_batches(self, start_from_batch=1, max_batches=None):
        """Process all fresh batch files sequentially"""
        print(f"🚀 STARTING FRESH BATCH PROCESSING")
        print(f"{'='*60}")
        print(f"✨ Using FRESH validated URLs for better success rates!")
        print(f"🎯 Expected much lower 499 error rates!")

        # Find all fresh batch files
        batch_files = self.find_fresh_batch_files()

        if not batch_files:
            print("❌ No fresh batch files found. Run fresh URL extractor first!")
            return

        # Filter batch files if starting from specific batch
        if start_from_batch > 1:
            batch_files = [f for f in batch_files if self.extract_batch_number(f) >= start_from_batch]

        if max_batches:
            batch_files = batch_files[:max_batches]

        print(f"📋 Processing {len(batch_files)} fresh batches starting from batch {start_from_batch}")

        # Process each batch sequentially
        for i, batch_file in enumerate(batch_files):
            batch_number = self.extract_batch_number(batch_file)

            try:
                batch_results = self.process_single_fresh_batch(batch_file, batch_number)

                # Shorter delay between batches since we're using fresh URLs
                if i < len(batch_files) - 1:  # Don't delay after last batch
                    print(f"⏸️  Waiting 15 seconds before next batch...")
                    time.sleep(15)

            except KeyboardInterrupt:
                print(f"\n🛑 Processing interrupted by user after batch {batch_number}")
                break

            except Exception as e:
                print(f"❌ Critical error in batch {batch_number}: {e}")
                print("🔄 Continuing with next batch...")
                continue

        # Save final combined results
        print(f"\n🎉 ALL FRESH BATCHES COMPLETE!")
        self.combine_and_save_results(final=True)

        # Cleanup progress file on successful completion
        if os.path.exists(self.progress_file):
            os.remove(self.progress_file)
            print(f"🧹 Cleaned up progress file")

        print(f"\n✅ FRESH BATCH PROCESSING COMPLETE")
        print(f"📁 Final results saved to: {self.combined_results_file}")
        print(f"🔥 Used FRESH URLs - should have much better success rates!")

    def extract_batch_number(self, filename):
        """Extract batch number from filename"""
        try:
            return int(filename.split('_')[2])
        except:
            return 999

    def resume_processing(self):
        """Resume processing from where it left off"""
        progress = self.load_progress()

        if not progress:
            print("📋 No previous progress found, starting from beginning")
            return self.process_all_fresh_batches()

        last_completed = progress.get('batches_completed', 0)
        next_batch = last_completed + 1

        print(f"🔄 Resuming from fresh batch {next_batch}")
        return self.process_all_fresh_batches(start_from_batch=next_batch)

def main():
    """Main execution function"""
    import argparse

    parser = argparse.ArgumentParser(description='Fresh Batch Processor for Validated URLs')
    parser.add_argument('--resume', action='store_true', help='Resume from last checkpoint')
    parser.add_argument('--start-from', type=int, default=1, help='Start from specific batch number')
    parser.add_argument('--max-batches', type=int, help='Maximum number of batches to process')
    parser.add_argument('--clean-progress', action='store_true', help='Clean progress and start fresh')

    args = parser.parse_args()

    # Initialize processor
    processor = FreshBatchProcessor()

    try:
        # Clean progress if requested
        if args.clean_progress and os.path.exists(processor.progress_file):
            os.remove(processor.progress_file)
            print("🧹 Progress file cleaned")

        # Resume or start fresh
        if args.resume:
            processor.resume_processing()
        else:
            processor.process_all_fresh_batches(
                start_from_batch=args.start_from,
                max_batches=args.max_batches
            )

    except KeyboardInterrupt:
        print(f"\n🛑 Processing interrupted by user")
        if processor.all_results:
            processor.combine_and_save_results(final=False)
            print(f"💾 Partial results saved")

    except Exception as e:
        print(f"❌ Critical error: {e}")
        if processor.all_results:
            processor.combine_and_save_results(final=False)
            print(f"💾 Partial results saved despite error")

if __name__ == "__main__":
    main()
