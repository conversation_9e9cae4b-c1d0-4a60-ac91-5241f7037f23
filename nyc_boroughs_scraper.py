#!/usr/bin/env python3
"""
NYC Boroughs Scraper - Scrapes only the 5 NYC boroughs from New York state
(A) Brooklyn, (B) Bronx, (C) Queens, (D) Manhattan, (E) Staten Island
"""

import sys
import os
import argparse
from typing import List, Dict
from state_scraper import StateScraper

class NYCBoroughsScraper(StateScraper):
    def __init__(self, mistral_api_key: str = None):
        """Initialize NYC boroughs scraper with Mistral API key"""
        super().__init__(mistral_api_key=mistral_api_key)
        
        # Define the 5 NYC boroughs to scrape
        self.target_boroughs = [
            'Brooklyn',
            'Bronx', 
            'Queens',
            'Manhattan',
            'Staten Island'
        ]
        
    def get_nyc_boroughs(self) -> List[Dict[str, str]]:
        """Get only the 5 NYC boroughs from New York state"""
        all_cities = self.parse_url_list()
        
        # Filter for New York state cities that match our target boroughs
        nyc_boroughs = []
        for city in all_cities:
            if (city['state'].lower() == 'new york' and 
                city['city'] in self.target_boroughs):
                nyc_boroughs.append(city)
        
        return nyc_boroughs
    
    def scrape_nyc_boroughs(self, max_cities: int = None, resume: bool = True) -> bool:
        """Scrape only the 5 NYC boroughs"""
        self.logger.info("Starting NYC Boroughs scrape")
        self.logger.info("Target boroughs: Brooklyn, Bronx, Queens, Manhattan, Staten Island")
        
        # Set up progress file for NYC boroughs
        self.progress_file = "nyc_boroughs_progress.json"
        
        # Load previous progress if resuming
        if resume:
            progress_data = self.load_progress()
            if progress_data:
                self.logger.info(f"Resuming NYC boroughs from previous session (last saved: {progress_data.get('timestamp', 'unknown')})")
        
        # Get NYC boroughs
        nyc_boroughs = self.get_nyc_boroughs()
        if not nyc_boroughs:
            self.logger.error("No NYC boroughs found")
            return False
        
        # Limit cities if specified (for testing)
        if max_cities:
            nyc_boroughs = nyc_boroughs[:max_cities]
            self.logger.info(f"Limited to first {max_cities} borough-source combinations for testing")
        
        self.logger.info(f"Found {len(nyc_boroughs)} borough-source combinations")
        
        # Group by borough name to show what will be processed
        boroughs_by_name = {}
        for borough_info in nyc_boroughs:
            borough_name = borough_info['city']
            if borough_name not in boroughs_by_name:
                boroughs_by_name[borough_name] = []
            boroughs_by_name[borough_name].append(borough_info)
        
        self.logger.info(f"Will process {len(boroughs_by_name)} NYC boroughs from both aaok and aypapi sources:")
        for borough_name in sorted(boroughs_by_name.keys()):
            sources = [borough['source'] for borough in boroughs_by_name[borough_name]]
            self.logger.info(f"  - {borough_name} (sources: {', '.join(sources)})")
        
        # Process each borough-source combination
        total_combinations = len(nyc_boroughs)
        processed_count = 0
        
        for i, borough_info in enumerate(nyc_boroughs):
            borough_name = borough_info['city']
            source = borough_info['source']
            
            self.logger.info(f"Processing combination {i+1}/{total_combinations}: {borough_name} from {source}")
            
            # Skip if already processed
            borough_key = f"New York_{borough_name}_{source}"
            if borough_key in self.processed_cities:
                self.logger.info(f"Skipping already processed: {borough_name} ({source})")
                continue
            
            try:
                # Scrape borough data (multi-page, all available pages)
                borough_data = self.scrape_city_pages(borough_info)
                
                # Add to main data collection
                self.scraped_data.extend(borough_data)
                
                # Mark as processed
                self.processed_cities.add(borough_key)
                processed_count += 1
                
                # Save progress after every borough-source combination
                progress_data = {
                    'target': 'NYC Boroughs',
                    'current_borough': borough_name,
                    'current_source': source,
                    'progress_index': i + 1,
                    'total_combinations': total_combinations,
                    'processed_count': processed_count
                }
                self.save_progress(progress_data)
                
                # Save data checkpoint after every borough-source combination
                checkpoint_name = f"nyc_boroughs_{borough_name}_{source}_checkpoint.xlsx"
                self.save_data_checkpoint(checkpoint_name)
                
                # Rate limiting between borough-source combinations
                import time
                time.sleep(self.request_delay)
                
            except Exception as e:
                self.logger.error(f"Error processing {borough_name} from {source}: {e}")
                # Save progress even on error
                progress_data = {
                    'target': 'NYC Boroughs',
                    'current_borough': borough_name,
                    'current_source': source,
                    'progress_index': i + 1,
                    'total_combinations': total_combinations,
                    'processed_count': processed_count,
                    'last_error': str(e)
                }
                self.save_progress(progress_data)
                continue
        
        # Final save
        final_file = "nyc_boroughs_final.xlsx"
        self.save_to_excel(final_file)
        self.logger.info(f"NYC Boroughs scrape completed. Final data saved to {final_file}")
        
        # Clean up progress file on successful completion
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                self.logger.info("Progress file cleaned up after successful completion")
        except Exception as e:
            self.logger.warning(f"Could not clean up progress file: {e}")
        
        return True

def main():
    """Main entry point for NYC boroughs scraper"""
    parser = argparse.ArgumentParser(description='NYC Boroughs Web Scraper (Brooklyn, Bronx, Queens, Manhattan, Staten Island)')
    parser.add_argument('--max-cities', type=int, help='Maximum number of borough-source combinations to process (for testing)')
    parser.add_argument('--delay', type=float, default=2.0, help='Delay between requests in seconds')
    parser.add_argument('--mistral-key', help='Mistral AI API key for enhanced text extraction')
    parser.add_argument('--no-resume', action='store_true', help='Start fresh without resuming from previous session')
    parser.add_argument('--clean-progress', action='store_true', help='Clean up progress files and start fresh')
    
    args = parser.parse_args()
    
    # Get Mistral API key (use your key as default)
    mistral_key = args.mistral_key or "dvP7AR4TRAdCe4brUOeElSyXxOqwVVso"
    
    # Create scraper instance
    scraper = NYCBoroughsScraper(mistral_api_key=mistral_key)
    scraper.request_delay = args.delay
    
    # Clean progress files if requested
    if args.clean_progress:
        progress_file = "nyc_boroughs_progress.json"
        if os.path.exists(progress_file):
            os.remove(progress_file)
            print(f"Cleaned up progress file: {progress_file}")
        
        # Also clean checkpoint files
        import glob
        checkpoint_pattern = "nyc_boroughs_*_checkpoint.xlsx"
        checkpoint_files = glob.glob(checkpoint_pattern)
        for cf in checkpoint_files:
            os.remove(cf)
            print(f"Cleaned up checkpoint file: {cf}")
        
        print("Progress files cleaned for NYC boroughs. Starting fresh.")
    
    # Scrape the NYC boroughs
    resume = not args.no_resume
    success = scraper.scrape_nyc_boroughs(max_cities=args.max_cities, resume=resume)
    
    if success:
        print(f"\n✓ Successfully completed scraping NYC boroughs")
        print(f"Total records collected: {len(scraper.scraped_data)}")
        
        # Show summary by source
        aaok_count = len([r for r in scraper.scraped_data if r.get('source') == 'aaok'])
        aypapi_count = len([r for r in scraper.scraped_data if r.get('source') == 'aypapi'])
        print(f"  - aaok.com: {aaok_count} records")
        print(f"  - aypapi.com: {aypapi_count} records")
        
        # Show summary by borough
        boroughs = list(set(r.get('city') for r in scraper.scraped_data))
        print(f"NYC Boroughs processed: {len(boroughs)}")
        for borough in sorted(boroughs):
            borough_count = len([r for r in scraper.scraped_data if r.get('city') == borough])
            print(f"  - {borough}: {borough_count} records")
        
        return 0
    else:
        print(f"\n✗ Failed to scrape NYC boroughs")
        return 1

if __name__ == "__main__":
    import time
    sys.exit(main())
