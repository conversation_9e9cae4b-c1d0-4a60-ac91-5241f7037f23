<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="11">
            <item index="0" class="java.lang.String" itemvalue="pydantic-settings" />
            <item index="1" class="java.lang.String" itemvalue="structlog" />
            <item index="2" class="java.lang.String" itemvalue="dotenv" />
            <item index="3" class="java.lang.String" itemvalue="aioredis" />
            <item index="4" class="java.lang.String" itemvalue="asyncio" />
            <item index="5" class="java.lang.String" itemvalue="hashlib" />
            <item index="6" class="java.lang.String" itemvalue="playwright" />
            <item index="7" class="java.lang.String" itemvalue="robotexclusionrulesparser" />
            <item index="8" class="java.lang.String" itemvalue="msgpack" />
            <item index="9" class="java.lang.String" itemvalue="simhash" />
            <item index="10" class="java.lang.String" itemvalue="yfinance" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>