# Data Extraction Scripts - Complete Guide

This document provides a comprehensive guide to all the data extraction scripts used to scrape profile data from escort websites. The scripts extract phone numbers, names, ages, locations, social media information, and full text descriptions.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Features](#features)
3. [Scripts Description](#scripts-description)
4. [Installation & Setup](#installation--setup)
5. [Usage Examples](#usage-examples)
6. [Output Format](#output-format)
7. [Phone Number Formatting](#phone-number-formatting)
8. [Duplicate Detection](#duplicate-detection)
9. [Progress Saving & Resume](#progress-saving--resume)
10. [File Structure](#file-structure)
11. [Troubleshooting](#troubleshooting)

## 🔍 Overview

The data extraction system is designed to process JSON files containing URLs to escort profile pages and extract structured data from each profile. The system includes multiple specialized scripts for different use cases.

### Extracted Data Fields

- **Phone Number** - Formatted as `1xxxxxxxxxx` (1 + 10 digits)
- **Name** - Person's name or working name
- **Age** - Age in years
- **Location** - Specific location/area from profile
- **City** - City from JSON metadata
- **State** - State from JSON metadata
- **Social Media** - Instagram, OnlyFans, Twitter, etc.
- **Raw Text** - Complete profile description
- **Status** - Processing status (success/failed/skipped_duplicate)
- **Extraction Date** - Timestamp of when data was extracted

## ✨ Features

- **Phone Deduplication** - Automatically skips profiles with duplicate phone numbers
- **Progress Saving** - Saves progress at regular intervals for resumability
- **Multi-threading** - Concurrent processing for faster extraction
- **Error Handling** - Robust error handling with detailed logging
- **Multiple Input Formats** - Supports various JSON file structures
- **Excel Output** - Professional Excel files with multiple sheets
- **Resume Capability** - Can resume from interruption points

## 📁 Scripts Description

### Core Extraction Scripts

#### 1. `phase2_batch_extractor.py` (MAIN SCRIPT)
**Primary script for processing Phase2 batch files**

- **Purpose**: Processes files `phase2_batch_01_500_urls.json` through `phase2_batch_18_315_urls.json`
- **Features**: 
  - Sequential batch processing
  - Cross-batch duplicate detection
  - Individual batch results + combined final file
  - Automatic progress saving after each batch
- **Usage**: `python run_phase2_extraction.py`
- **Output**: Individual batch Excel files + final combined Excel file

#### 2. `enhanced_dedup_extractor.py`
**Enhanced extractor for single large JSON files**

- **Purpose**: Processes single JSON files (like `fresh_all_urls_20250818_234554.json`)
- **Features**: 
  - Progress saving every 500 URLs
  - Phone deduplication
  - Resume from checkpoints
- **Usage**: `python start_extraction.py`
- **Best For**: Large single JSON files

#### 3. `comprehensive_data_extractor.py`
**General-purpose extractor for any JSON file**

- **Purpose**: Flexible extractor for various JSON formats
- **Usage**: `python comprehensive_data_extractor.py --json_file urls.json --output results.xlsx`
- **Best For**: Custom extraction jobs and testing

### Runner Scripts

#### 1. `run_phase2_extraction.py`
**Main runner for Phase2 batch extraction**

```bash
python run_phase2_extraction.py
```

- Automatically processes all Phase2 batch files
- Shows file information and estimates
- Handles interruptions gracefully

#### 2. `start_extraction.py`
**Runner for enhanced single-file extraction**

```bash
python start_extraction.py
```

- Processes `fresh_all_urls_20250818_234554.json`
- Auto-starts without user input
- Progress saving and resume capability

#### 3. `run_extractor.py`
**General runner with multiple options**

```bash
# Process single file
python run_extractor.py --file your_file.json

# Process multiple files matching pattern
python run_extractor.py --pattern "fresh_urls_*.json"

# Process all URL files
python run_extractor.py --all

# Interactive mode
python run_extractor.py
```

### Testing & Utility Scripts

#### 1. `test_phone_formatting.py`
Tests phone number formatting functionality
```bash
python test_phone_formatting.py
```

#### 2. `test_extractor.py`
Tests the extraction functionality with sample URLs
```bash
python test_extractor.py
```

#### 3. `demo_extractor.py`
Demonstration script showing various extraction methods
```bash
python demo_extractor.py
```

## 🛠 Installation & Setup

### Prerequisites

- Python 3.7+
- Internet connection for web scraping

### Install Dependencies

```bash
pip install -r requirements.txt
```

### Required Packages

```
beautifulsoup4==4.12.2
pandas==2.1.4
openpyxl==3.1.2
requests==2.31.0
lxml==4.9.3
```

## 🚀 Usage Examples

### Process Phase2 Batch Files (Recommended)

```bash
# Process all Phase2 batch files (phase2_batch_01 through phase2_batch_18)
python run_phase2_extraction.py
```

This will:
- Process 8,815 URLs across 18 batch files
- Save results after each batch
- Create individual Excel files for each batch
- Generate final combined Excel file
- Skip duplicate phone numbers across all batches

### Process Single Large JSON File

```bash
# Process fresh_all_urls_20250818_234554.json
python start_extraction.py
```

This will:
- Process 2,152 URLs from the JSON file
- Save checkpoints every 500 URLs
- Skip duplicate phone numbers
- Allow resuming if interrupted

### Custom File Processing

```bash
# Process specific file with custom settings
python run_extractor.py --file your_urls.json --delay 2.0 --workers 3 --limit 100

# Process multiple files
python run_extractor.py --pattern "batch_*.json" --delay 1.5

# Interactive mode - choose from available files
python run_extractor.py
```

## 📊 Output Format

### Excel File Structure

Each output Excel file contains:

#### Sheet 1: "Profile_Data" or "All_Profile_Data"
| Column | Description | Example |
|--------|-------------|---------|
| url | Original profile URL | https://site.com/post/123 |
| phone_number | Formatted phone (1xxxxxxxxxx) | *********** |
| name | Extracted name | Sarah |
| location | Profile location | Columbia |
| city | City from JSON | Baltimore |
| state | State from JSON | Maryland |
| age | Age in years | 28 |
| social_media | Social media accounts | Instagram: user123 |
| raw_text | Full profile text | My name is Sarah... |
| status | Processing status | success |
| error | Error message if failed | Request timeout |
| duplicate_phone | Is duplicate phone | False |
| extracted_at | Processing timestamp | 2025-01-19T10:30:45 |

#### Sheet 2: "Summary" (in final files)
- Total URLs processed
- Success/failure/duplicate counts
- Processing time statistics
- Unique phone numbers found

## 📞 Phone Number Formatting

All phone numbers are standardized to the format: **`1xxxxxxxxxx`**

### Examples:
- Input: `************` → Output: `***********`
- Input: `(*************` → Output: `***********`
- Input: `****** 123 4567` → Output: `***********`
- Input: `***********` → Output: `***********`

### Logic:
1. Extract all digits from input
2. Take last 10 digits if more than 10
3. Add "1" prefix
4. Result: 11-digit number starting with "1"

## 🔄 Duplicate Detection

The system prevents multiple entries for the same person:

- **Phone-based deduplication**: Uses normalized phone numbers
- **Cross-batch detection**: Maintains seen phones across all processing
- **Status tracking**: Marks duplicates as "skipped_duplicate"
- **Persistent storage**: Saves seen phones for resume capability

## 💾 Progress Saving & Resume

### Phase2 Batch Extraction
- Saves progress after each completed batch
- Creates `phase2_extraction_progress.json` with state
- Resume by running the same command again

### Enhanced Single-File Extraction
- Saves checkpoints every 500 URLs
- Creates checkpoint Excel files and progress JSON
- Automatically resumes from last checkpoint

### Progress Files
- `progress_state_[timestamp].json` - Single file extraction progress
- `phase2_extraction_progress.json` - Batch extraction progress
- `checkpoint_[num]_urls_[timestamp].xlsx` - Checkpoint data files

## 📁 File Structure

```
project/
├── README_DATA_EXTRACTION.md          # This file
├── requirements.txt                   # Python dependencies
├── 
├── CORE EXTRACTION SCRIPTS:
├── phase2_batch_extractor.py         # Main Phase2 batch processor
├── enhanced_dedup_extractor.py       # Single file processor
├── comprehensive_data_extractor.py   # General purpose extractor
├── 
├── RUNNER SCRIPTS:
├── run_phase2_extraction.py          # Phase2 batch runner
├── start_extraction.py               # Single file runner  
├── run_extractor.py                  # General runner
├── 
├── TESTING SCRIPTS:
├── test_phone_formatting.py          # Phone formatting tests
├── test_extractor.py                 # Extraction functionality tests
├── demo_extractor.py                 # Demo and examples
├── 
├── INPUT FILES:
├── phase2_batch_01_500_urls.json     # Batch 1 URLs
├── phase2_batch_02_500_urls.json     # Batch 2 URLs
├── ...                               # Batches 3-17
├── phase2_batch_18_315_urls.json     # Final batch URLs
├── fresh_all_urls_20250818_234554.json # Large single file
├── 
├── OUTPUT FILES:
├── phase2_batch_XX_results_[timestamp].xlsx    # Individual batch results
├── phase2_all_batches_FINAL_results_[timestamp].xlsx # Combined results
├── fresh_all_urls_FINAL_extraction_[timestamp].xlsx # Single file results
├── 
└── PROGRESS FILES:
    ├── phase2_extraction_progress.json         # Batch progress
    ├── progress_state_[timestamp].json         # Single file progress
    ├── checkpoint_[num]_urls_[timestamp].xlsx  # Checkpoint files
    └── *.log                                   # Log files
```

## 🔧 Configuration Options

### Common Parameters

| Parameter | Description | Default | Example |
|-----------|-------------|---------|---------|
| delay | Seconds between requests | 1.0 | --delay 2.0 |
| max_workers | Concurrent threads | 5 | --workers 3 |
| limit | Max URLs to process | None | --limit 100 |
| output | Custom output file | Auto | --output results.xlsx |

### Environment Variables

```bash
# Adjust for your system
export MAX_WORKERS=3
export REQUEST_DELAY=1.5
export CHECKPOINT_INTERVAL=500
```

## 🐛 Troubleshooting

### Common Issues

#### 1. **Connection Errors**
```
Request error: Connection timeout
```
**Solution**: Increase delay between requests
```bash
python run_phase2_extraction.py  # Edit delay in script to 2.0
```

#### 2. **Memory Issues with Large Files**
```
MemoryError: Unable to allocate array
```
**Solution**: Reduce worker count or process in smaller batches
```bash
# Edit max_workers to 2 in the script
```

#### 3. **Permission Errors**
```
Permission denied: results.xlsx
```
**Solution**: Close Excel files and ensure write permissions

#### 4. **Resume Not Working**
**Check for progress files**:
```bash
ls -la *progress*.json
ls -la checkpoint_*.xlsx
```

#### 5. **Phone Formatting Issues**
Run the phone formatting test:
```bash
python test_phone_formatting.py
```

### Debug Mode

Enable detailed logging by editing the scripts:
```python
logging.basicConfig(level=logging.DEBUG)  # Change from INFO to DEBUG
```

### Performance Optimization

#### For Faster Processing:
- Increase worker count: `max_workers=8`
- Decrease delay: `delay=0.5`
- Use SSD storage for checkpoint files

#### For Stability:
- Decrease worker count: `max_workers=2`
- Increase delay: `delay=2.0`
- Enable debug logging

### Recovery Commands

#### Start Fresh (Remove All Progress):
```bash
rm -f *progress*.json
rm -f checkpoint_*.xlsx
```

#### Resume from Specific Point:
- Edit the progress JSON files manually
- Remove unwanted checkpoint files
- Restart the extraction

## 📈 Performance Estimates

### Phase2 Batch Extraction (8,815 URLs)
- **Estimated Time**: 4.9 hours
- **Success Rate**: ~85-95%
- **Unique Phones**: ~4,000-6,000
- **Output Size**: ~15-25 MB

### Single File Extraction (2,152 URLs)  
- **Estimated Time**: 1.2 hours
- **Success Rate**: ~85-95%
- **Unique Phones**: ~1,000-1,500
- **Output Size**: ~3-5 MB

### Factors Affecting Performance:
- Network speed and stability
- Website response times
- Number of concurrent workers
- Delay between requests
- System resources (CPU, memory)

## 📝 Logging

All scripts generate detailed log files:

- `phase2_batch_extractor.log` - Phase2 batch processing
- `enhanced_dedup_extractor.log` - Single file processing
- `comprehensive_extractor.log` - General extraction

Log levels:
- **INFO**: Normal operation messages
- **WARNING**: Non-critical issues
- **ERROR**: Processing errors and failures
- **DEBUG**: Detailed debugging information

## 🔒 Best Practices

1. **Always test with small samples first**
   ```bash
   python run_extractor.py --file test_file.json --limit 10
   ```

2. **Monitor system resources during large extractions**
   - Watch CPU and memory usage
   - Ensure sufficient disk space
   - Check network stability

3. **Keep backups of progress files**
   ```bash
   cp progress_state_*.json backups/
   ```

4. **Use appropriate delays**
   - Start with 1.0 second delay
   - Increase if getting connection errors
   - Decrease for faster processing (if stable)

5. **Regular checkpoints**
   - Let scripts complete batch cycles
   - Don't interrupt during checkpoint saves
   - Verify checkpoint files are created

## 📞 Support

For issues or questions:

1. Check the troubleshooting section above
2. Review log files for error details
3. Test with smaller samples to isolate issues
4. Verify JSON file formats and structures

---

**Last Updated**: January 19, 2025  
**Version**: 2.0  
**Compatible Scripts**: All current extraction scripts