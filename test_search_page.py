#!/usr/bin/env python3
"""
Test script to check what's actually in our search pages
"""

import requests
import re
from bs4 import BeautifulSoup

def test_search_page():
    """Test what URLs are actually in our target search pages"""

    scraperapi_key = '********************************'

    # Test search pages for our target cities
    test_urls = [
        ('South New Jersey', 'https://aypapi.com.listcrawler.eu/brief/escorts/usa/new%20jersey/south%20new%20jersey/1'),
        ('Philadelphia', 'https://aaok.com.listcrawler.eu/brief/escorts/usa/pennsylvania/philadelphia/1'),
        ('Baltimore', 'https://aaok.com.listcrawler.eu/brief/escorts/usa/maryland/baltimore/1')
    ]

    for city_name, search_url in test_urls:
        print(f"\n🏙️ Testing {city_name} search page:")
        print(f"URL: {search_url}")
        print("-" * 60)

        try:
            params = {
                'api_key': scraperapi_key,
                'url': search_url,
                'render': 'false',
                'country_code': 'us'
            }

            response = requests.get('https://api.scraperapi.com/', params=params, timeout=30)

            if response.status_code == 200:
                html = response.text
                print(f"✅ Fetched successfully: {len(html)} characters")

                # Parse HTML
                soup = BeautifulSoup(html, 'html.parser')

                # Look for profile links
                profile_links = []

                # Find all links that contain 'post/escorts'
                all_links = soup.find_all('a', href=True)
                for link in all_links:
                    href = link.get('href', '')
                    if 'post/escorts' in href:
                        profile_links.append(href)

                print(f"📋 Found {len(profile_links)} profile URLs")

                # Analyze what cities are in these URLs
                cities_in_urls = set()
                states_in_urls = set()

                for url in profile_links[:15]:  # Check first 15
                    print(f"   {url}")

                    # Extract city and state from URL
                    city_match = re.search(r'/usa/([^/]+)/([^/]+)/', url)
                    if city_match:
                        state = city_match.group(1)
                        city = city_match.group(2)
                        states_in_urls.add(state)
                        cities_in_urls.add(city)

                print(f"\n📊 Analysis:")
                print(f"   States found: {sorted(states_in_urls)}")
                print(f"   Cities found: {sorted(cities_in_urls)}")

                # Check if we're getting the expected city
                expected_city = city_name.lower().replace(' ', '').replace('new', '').replace('jersey', '')
                if any(expected_city in city.lower() for city in cities_in_urls):
                    print(f"   ✅ Expected city URLs found!")
                else:
                    print(f"   ❌ Expected city URLs NOT found - this explains the issue!")

            else:
                print(f"❌ Failed to fetch: Status {response.status_code}")
                if response.text:
                    print(f"   Error: {response.text[:200]}")

        except Exception as e:
            print(f"❌ Error: {e}")

        print("-" * 60)

if __name__ == "__main__":
    test_search_page()
