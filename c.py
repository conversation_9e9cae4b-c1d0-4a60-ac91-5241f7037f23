#!/usr/bin/env python3

import pandas as pd
import sys
import os
import re

def process_excel_data(input_file, output_file=None):
    """
    Complete data processing pipeline:
    1. Clean phone numbers (remove non-digits)
    2. Extract city from page_title (2nd word)
    3. Update location with city
    4. Add '1' prefix to phone numbers
    
    Args:
        input_file (str): Path to input Excel file
        output_file (str): Path to output Excel file (optional)
    """
    
    # Set default output file name if not provided
    if output_file is None:
        name, ext = os.path.splitext(input_file)
        output_file = f"{name}_processed{ext}"
    
    try:
        # Check if input file exists
        if not os.path.exists(input_file):
            print(f"Error: Input file '{input_file}' not found!")
            return False
        
        print(f"📖 Reading Excel file: {input_file}")
        df = pd.read_excel(input_file)
        
        print(f"✅ File loaded successfully. Shape: {df.shape}")
        print(f"📊 Columns: {list(df.columns)}")
        
        # Step 1: Clean phone numbers (remove non-digits)
        print("\n" + "="*50)
        print("STEP 1: Cleaning phone numbers")
        print("="*50)
        
        phone_col = None
        possible_phone_columns = ['phone', 'phone_number', 'phonenumber', 'mobile', 'cell', 'telephone']
        
        # Find phone column
        if 'phone' in df.columns:
            phone_col = 'phone'
        else:
            for col in df.columns:
                if col.lower() in possible_phone_columns:
                    phone_col = col
                    break
        
        if phone_col:
            print(f"📞 Found phone column: '{phone_col}'")
            original_phones = df[phone_col].copy()
            
            # Clean phone numbers - remove non-digits
            df[phone_col] = df[phone_col].apply(
                lambda x: re.sub(r'[^0-9]', '', str(x)) if pd.notna(x) else x
            )
            
            print(f"🧹 Cleaned phone numbers (removed non-digits)")
            print("Sample before/after:")
            for i in range(min(5, len(df))):
                if pd.notna(original_phones.iloc[i]):
                    print(f"  {original_phones.iloc[i]} → {df[phone_col].iloc[i]}")
        else:
            print("⚠️  No phone column found, skipping phone cleaning")
        
        # Step 2: Extract city from page_title and update location
        print("\n" + "="*50)
        print("STEP 2: Processing page_title and location")
        print("="*50)
        
        if 'page_title' in df.columns:
            print("📄 Found page_title column")
            
            # Extract 2nd word from page_title as city
            def get_second_word(text):
                if pd.isna(text):
                    return ''
                words = str(text).split()
                if len(words) >= 2:
                    return words[1]  # Take only the 2nd word (index 1)
                else:
                    return ''
            
            df['city'] = df['page_title'].apply(get_second_word)
            print("🏙️  Extracted city from page_title (2nd word)")
            
            # Update location if it exists
            if 'location' in df.columns:
                print("📍 Found location column, updating with city")
                original_location = df['location'].copy()
                df['location'] = df['location'].astype(str) + ', ' + df['city']
                
                print("Sample location updates:")
                for i in range(min(5, len(df))):
                    if pd.notna(original_location.iloc[i]):
                        print(f"  {original_location.iloc[i]} → {df['location'].iloc[i]}")
            else:
                print("📍 No location column found, city saved as separate column")
                
            print("Sample page_title → city extractions:")
            sample_data = df[['page_title', 'city']].head()
            for _, row in sample_data.iterrows():
                if pd.notna(row['page_title']):
                    print(f"  '{row['page_title']}' → '{row['city']}'")
        else:
            print("⚠️  No page_title column found, skipping city extraction")
        
        # Step 3: Add '1' prefix to phone numbers
        if phone_col:
            print("\n" + "="*50)
            print("STEP 3: Adding '1' prefix to phone numbers")
            print("="*50)
            
            non_empty_phones = df[phone_col].notna().sum()
            print(f"📱 Processing {non_empty_phones} phone numbers")
            
            def add_prefix(phone):
                if pd.isna(phone):
                    return phone
                
                phone_str = str(phone).strip()
                
                if not phone_str or phone_str.lower() == 'nan':
                    return phone
                
                digits_only = ''.join(filter(str.isdigit, phone_str))
                
                if not digits_only:
                    return phone
                
                if phone_str.startswith('1'):
                    return phone_str
                
                return '1' + phone_str
            
            # Show sample before adding prefix
            print("Sample phone numbers before adding prefix:")
            for i, phone in enumerate(df[phone_col].head().values):
                if pd.notna(phone) and str(phone).strip():
                    print(f"  Row {i+1}: {phone}")
            
            # Apply prefix
            df[phone_col] = df[phone_col].apply(add_prefix)
            
            print("Sample phone numbers after adding prefix:")
            for i, phone in enumerate(df[phone_col].head().values):
                if pd.notna(phone) and str(phone).strip():
                    print(f"  Row {i+1}: {phone}")
        
        # Step 4: Save the final file
        print("\n" + "="*50)
        print("STEP 4: Saving processed file")
        print("="*50)
        
        print(f"💾 Saving to: {output_file}")
        df.to_excel(output_file, index=False)
        
        print(f"\n🎉 SUCCESS! Processing completed!")
        print(f"📁 Input file: {input_file}")
        print(f"📁 Output file: {output_file}")
        print(f"📊 Final shape: {df.shape}")
        print(f"📝 Final columns: {list(df.columns)}")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ Error: File '{input_file}' not found!")
        return False
    except Exception as e:
        print(f"❌ Error processing file: {str(e)}")
        return False

def main():
    """Main function to handle command line arguments"""
    
    # Default settings
    input_files = [
        "output_with_social_ids_9000.xlsx",
    ]
    
    input_file = None
    output_file = None
    
    # Check command line arguments
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        # Try to find existing file from the common names
        for file in input_files:
            if os.path.exists(file):
                input_file = file
                print(f"🔍 Found existing file: {input_file}")
                break
        
        if input_file is None:
            print("❌ No input file specified and no default files found!")
            print("Usage: python script.py <input_file> [output_file]")
            print("Or place one of these files in the current directory:")
            for file in input_files:
                print(f"  - {file}")
            sys.exit(1)
    
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    print("=" * 60)
    print("🚀 COMPLETE DATA PROCESSING PIPELINE")
    print("=" * 60)
    print("This script will:")
    print("1. Clean phone numbers (remove non-digits)")
    print("2. Extract city from page_title (2nd word)")
    print("3. Update location with city")
    print("4. Add '1' prefix to phone numbers")
    print("=" * 60)
    
    # Process the file
    success = process_excel_data(input_file, output_file)
    
    if success:
        print("\n✅ All tasks completed successfully!")
    else:
        print("\n❌ Processing failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()