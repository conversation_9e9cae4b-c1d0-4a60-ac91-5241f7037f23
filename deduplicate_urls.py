#!/usr/bin/env python3
"""
URL Deduplication Script for NYC Scraper
Removes duplicate URLs from parallel_nyc_all_urls.json before Phase 2
"""

import json
import sys
from datetime import datetime
from typing import Dict, List, Set

def deduplicate_urls(input_file: str = "parallel_nyc_all_urls.json", 
                    output_file: str = "parallel_nyc_all_urls_deduplicated.json") -> bool:
    """Remove duplicate URLs from the extracted URL data"""
    
    print("NYC URL Deduplication Tool")
    print("=" * 50)
    
    # Load the URL data
    try:
        with open(input_file, 'r') as f:
            url_data = json.load(f)
        print(f"✓ Loaded URL data from: {input_file}")
    except Exception as e:
        print(f"✗ Failed to load {input_file}: {e}")
        return False
    
    # Track statistics
    original_stats = {}
    deduplicated_stats = {}
    global_url_set = set()
    total_original = 0
    total_duplicates = 0
    
    print(f"\nOriginal Data Summary:")
    print("-" * 30)
    
    # First pass: collect statistics and find duplicates
    for combo_key, combo_data in url_data.items():
        borough = combo_data['borough']
        source = combo_data['source']
        urls = combo_data['urls']
        
        original_count = len(urls)
        total_original += original_count
        original_stats[combo_key] = original_count
        
        print(f"{borough} ({source}): {original_count:,} URLs")
        
        # Check for duplicates within this combination
        unique_urls_in_combo = set(urls)
        combo_duplicates = original_count - len(unique_urls_in_combo)
        if combo_duplicates > 0:
            print(f"  ⚠️  {combo_duplicates} internal duplicates found")
        
        # Check for duplicates across combinations
        cross_duplicates = len(global_url_set.intersection(unique_urls_in_combo))
        if cross_duplicates > 0:
            print(f"  ⚠️  {cross_duplicates} cross-combination duplicates found")
        
        global_url_set.update(unique_urls_in_combo)
    
    print(f"\nTotal original URLs: {total_original:,}")
    print(f"Unique URLs across all combinations: {len(global_url_set):,}")
    print(f"Total duplicates found: {total_original - len(global_url_set):,}")
    
    # Second pass: deduplicate URLs
    print(f"\nDeduplicating URLs...")
    print("-" * 30)
    
    deduplicated_data = {}
    processed_urls = set()
    
    for combo_key, combo_data in url_data.items():
        borough = combo_data['borough']
        source = combo_data['source']
        urls = combo_data['urls']
        
        # Remove duplicates within this combination and across combinations
        unique_urls = []
        for url in urls:
            if url not in processed_urls:
                unique_urls.append(url)
                processed_urls.add(url)
        
        # Update the combination data
        deduplicated_data[combo_key] = {
            'borough': borough,
            'source': source,
            'base_url': combo_data['base_url'],
            'pages_scraped': combo_data['pages_scraped'],
            'total_urls': len(unique_urls),
            'original_urls': len(urls),
            'duplicates_removed': len(urls) - len(unique_urls),
            'urls': unique_urls,
            'extracted_at': combo_data['extracted_at'],
            'deduplicated_at': datetime.now().isoformat(),
            'worker_id': combo_data.get('worker_id', 'unknown')
        }
        
        deduplicated_stats[combo_key] = len(unique_urls)
        
        duplicates_removed = len(urls) - len(unique_urls)
        if duplicates_removed > 0:
            print(f"{borough} ({source}): {len(urls):,} → {len(unique_urls):,} URLs (-{duplicates_removed})")
        else:
            print(f"{borough} ({source}): {len(unique_urls):,} URLs (no duplicates)")
    
    # Save deduplicated data
    try:
        with open(output_file, 'w') as f:
            json.dump(deduplicated_data, f, indent=2)
        print(f"\n✓ Deduplicated URLs saved to: {output_file}")
    except Exception as e:
        print(f"\n✗ Failed to save deduplicated data: {e}")
        return False
    
    # Final summary
    total_deduplicated = sum(deduplicated_stats.values())
    total_removed = total_original - total_deduplicated
    
    print(f"\nDeduplication Summary:")
    print("=" * 50)
    print(f"Original URLs: {total_original:,}")
    print(f"Unique URLs: {total_deduplicated:,}")
    print(f"Duplicates removed: {total_removed:,}")
    print(f"Deduplication rate: {(total_removed / total_original * 100):.1f}%")
    
    # Detailed breakdown
    print(f"\nDetailed Breakdown:")
    print("-" * 30)
    for combo_key in sorted(deduplicated_data.keys()):
        data = deduplicated_data[combo_key]
        borough = data['borough']
        source = data['source']
        original = data['original_urls']
        final = data['total_urls']
        removed = data['duplicates_removed']
        
        if removed > 0:
            print(f"{borough:12} ({source:6}): {original:5,} → {final:5,} URLs (-{removed:3})")
        else:
            print(f"{borough:12} ({source:6}): {final:5,} URLs (clean)")
    
    # Efficiency metrics for Phase 2
    estimated_batches = (total_deduplicated + 9) // 10  # Assuming batch size of 10
    print(f"\nPhase 2 Preparation:")
    print("-" * 30)
    print(f"URLs ready for Phase 2: {total_deduplicated:,}")
    print(f"Estimated batches (size 10): {estimated_batches:,}")
    print(f"API call reduction: {((total_deduplicated - estimated_batches) / total_deduplicated * 100):.1f}%")
    
    return True

def verify_deduplication(file_path: str = "parallel_nyc_all_urls_deduplicated.json") -> bool:
    """Verify that deduplication was successful"""
    
    print(f"\nVerifying deduplication in {file_path}...")
    
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
    except Exception as e:
        print(f"✗ Failed to load {file_path}: {e}")
        return False
    
    all_urls = []
    for combo_data in data.values():
        all_urls.extend(combo_data['urls'])
    
    unique_urls = set(all_urls)
    
    if len(all_urls) == len(unique_urls):
        print(f"✓ Verification passed: {len(all_urls):,} URLs, all unique")
        return True
    else:
        duplicates = len(all_urls) - len(unique_urls)
        print(f"✗ Verification failed: {duplicates} duplicates still found")
        return False

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Deduplicate URLs from NYC scraper data')
    parser.add_argument('--input', default='parallel_nyc_all_urls.json', 
                       help='Input JSON file with URLs')
    parser.add_argument('--output', default='parallel_nyc_all_urls_deduplicated.json',
                       help='Output JSON file for deduplicated URLs')
    parser.add_argument('--verify', action='store_true',
                       help='Verify deduplication after processing')
    
    args = parser.parse_args()
    
    # Perform deduplication
    success = deduplicate_urls(args.input, args.output)
    
    if not success:
        print("Deduplication failed!")
        return 1
    
    # Verify if requested
    if args.verify:
        verify_success = verify_deduplication(args.output)
        if not verify_success:
            print("Verification failed!")
            return 1
    
    print(f"\n🎯 Ready for Phase 2!")
    print(f"Use the deduplicated file: {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
