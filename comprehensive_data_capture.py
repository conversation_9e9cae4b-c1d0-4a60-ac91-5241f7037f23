#!/usr/bin/env python3
"""
Comprehensive data capture - saves both successful and failed extractions
"""

import json
import re
import pandas as pd
from datetime import datetime

def analyze_current_progress():
    """Analyze what's happening with the current scraper"""
    
    print("COMPREHENSIVE DATA CAPTURE ANALYSIS")
    print("=" * 60)
    
    # Let's create a sample of what we should be capturing
    sample_successful = [
        {
            'status': 'SUCCESS',
            'url': 'https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/manhattanny/191403342',
            'city': 'Manhattan',
            'source': 'aaok',
            'extraction_method': 'structured_parsing',
            'name': '<PERSON><PERSON>',
            'age': '25',
            'gender': 'woman',
            'phone': '3475665137',
            'description': 'Curvy in all the right places, I am a fun-loving adventurous spirit...',
            'social_media': 'Instagram: @ciara_nyc, Snapchat: ciara_fun',
            'email': None,
            'website': None,
            'post_id': '191403342',
            'raw_mistral_response': '[{"name": "<PERSON><PERSON>", "age": "25", ...}]',
            'parsing_error': None,
            'worker_id': 0,
            'scraped_at': datetime.now().isoformat()
        }
    ]
    
    sample_failed_parsing = [
        {
            'status': 'FAILED_PARSING',
            'url': 'https://aaok.com.listcrawler.eu/post/escorts/usa/newyork/brooklyn/191405678',
            'city': 'Brooklyn',
            'source': 'aaok',
            'extraction_method': 'fallback_failed',
            'name': None,
            'age': None,
            'gender': None,
            'phone': None,
            'description': None,
            'social_media': None,
            'email': None,
            'website': None,
            'post_id': '191405678',
            'raw_mistral_response': 'Here is the extracted data:\n```json\n[{"name": "Maria", "age": "22"...}]\n```\nBut with malformed JSON',
            'parsing_error': 'JSONDecodeError: Unterminated string starting at line 23',
            'worker_id': 1,
            'scraped_at': datetime.now().isoformat()
        }
    ]
    
    sample_failed_extraction = [
        {
            'status': 'FAILED_EXTRACTION',
            'url': 'https://aypapi.com.listcrawler.eu/post/escorts/usa/newyork/queens/191407890',
            'city': 'Queens',
            'source': 'aypapi',
            'extraction_method': 'mistral_error',
            'name': None,
            'age': None,
            'gender': None,
            'phone': None,
            'description': None,
            'social_media': None,
            'email': None,
            'website': None,
            'post_id': '191407890',
            'raw_mistral_response': None,
            'parsing_error': 'Mistral API timeout after 3 retries',
            'worker_id': 2,
            'scraped_at': datetime.now().isoformat()
        }
    ]
    
    # Combine all data
    all_data = sample_successful + sample_failed_parsing + sample_failed_extraction
    
    # Create comprehensive DataFrame
    df = pd.DataFrame(all_data)
    
    # Save comprehensive results
    output_file = "COMPREHENSIVE_data_capture_example.xlsx"
    df.to_excel(output_file, index=False)
    
    print(f"✓ Saved comprehensive example to: {output_file}")
    print(f"Total records: {len(all_data)}")
    print()
    
    # Show breakdown
    status_counts = df['status'].value_counts()
    print("Status Breakdown:")
    print("-" * 30)
    for status, count in status_counts.items():
        print(f"{status}: {count}")
    
    print()
    print("Column Structure:")
    print("-" * 30)
    for i, col in enumerate(df.columns, 1):
        print(f"{i:2d}. {col}")
    
    print()
    print("Sample Records:")
    print("-" * 30)
    
    for i, row in df.iterrows():
        print(f"\nRecord {i+1} - {row['status']}:")
        print(f"  URL: {row['url']}")
        print(f"  Name: {row['name']} | Age: {row['age']} | Gender: {row['gender']}")
        print(f"  Phone: {row['phone']}")
        print(f"  Parsing Error: {row['parsing_error']}")
        if row['raw_mistral_response']:
            print(f"  Raw Response: {row['raw_mistral_response'][:100]}...")
    
    print()
    print("🎯 WHAT WE SHOULD BE CAPTURING:")
    print("=" * 50)
    print("1. ✅ SUCCESSFUL extractions with structured data")
    print("2. ❌ FAILED PARSING with raw Mistral responses for manual review")
    print("3. ❌ FAILED EXTRACTION with error details")
    print("4. 📊 ALL attempts (not just 'valid' profiles)")
    print("5. 🔧 Raw data for manual processing of failed cases")
    
    print()
    print("💡 BENEFITS:")
    print("-" * 20)
    print("• No data loss - capture everything")
    print("• Manual review possible for failed parsing")
    print("• Error analysis for system improvement")
    print("• Complete audit trail")
    print("• Maximum data recovery")
    
    print()
    print("🚨 CURRENT ISSUE:")
    print("-" * 20)
    print("• System is extracting profiles successfully")
    print("• But 'failed to parse' means we're losing data")
    print("• Need to capture raw Mistral responses")
    print("• Should save partial/failed extractions too")
    
    return output_file

def create_improved_extraction_logic():
    """Show improved extraction logic that captures everything"""
    
    improved_logic = """
    IMPROVED EXTRACTION LOGIC:
    
    def process_batch_comprehensive(self, batch, worker_id):
        results = []
        
        for url, markdown in batch:
            try:
                # Call Mistral
                raw_response = self.call_mistral_with_retry(...)
                
                if raw_response:
                    try:
                        # Try direct JSON parsing
                        parsed_data = json.loads(raw_response)
                        
                        # SUCCESS - structured data
                        for profile in parsed_data:
                            results.append({
                                'status': 'SUCCESS',
                                'url': url,
                                'extraction_method': 'direct_parsing',
                                'name': profile.get('name'),
                                'age': profile.get('age'),
                                'gender': profile.get('gender', 'woman'),  # Default assumption
                                'phone': profile.get('phone'),
                                'description': profile.get('description'),
                                'social_media': profile.get('social_media'),
                                'raw_mistral_response': raw_response,
                                'parsing_error': None,
                                'worker_id': worker_id
                            })
                            
                    except json.JSONDecodeError as e:
                        # Try fallback parsing
                        json_match = re.search(r'\\[.*\\]', raw_response, re.DOTALL)
                        if json_match:
                            try:
                                parsed_data = json.loads(json_match.group())
                                
                                # SUCCESS - fallback parsing
                                for profile in parsed_data:
                                    results.append({
                                        'status': 'SUCCESS',
                                        'url': url,
                                        'extraction_method': 'fallback_parsing',
                                        'name': profile.get('name'),
                                        'age': profile.get('age'),
                                        'gender': profile.get('gender', 'woman'),
                                        'phone': profile.get('phone'),
                                        'description': profile.get('description'),
                                        'social_media': profile.get('social_media'),
                                        'raw_mistral_response': raw_response,
                                        'parsing_error': str(e),
                                        'worker_id': worker_id
                                    })
                                    
                            except json.JSONDecodeError as e2:
                                # FAILED PARSING - but save raw data
                                results.append({
                                    'status': 'FAILED_PARSING',
                                    'url': url,
                                    'extraction_method': 'parsing_failed',
                                    'name': None,
                                    'age': None,
                                    'gender': None,
                                    'phone': None,
                                    'description': None,
                                    'social_media': None,
                                    'raw_mistral_response': raw_response,
                                    'parsing_error': f"Direct: {e}, Fallback: {e2}",
                                    'worker_id': worker_id
                                })
                        else:
                            # FAILED PARSING - no JSON found
                            results.append({
                                'status': 'FAILED_PARSING',
                                'url': url,
                                'extraction_method': 'no_json_found',
                                'name': None,
                                'age': None,
                                'gender': None,
                                'phone': None,
                                'description': None,
                                'social_media': None,
                                'raw_mistral_response': raw_response,
                                'parsing_error': f"No JSON array found. Error: {e}",
                                'worker_id': worker_id
                            })
                else:
                    # FAILED EXTRACTION - no Mistral response
                    results.append({
                        'status': 'FAILED_EXTRACTION',
                        'url': url,
                        'extraction_method': 'mistral_failed',
                        'name': None,
                        'age': None,
                        'gender': None,
                        'phone': None,
                        'description': None,
                        'social_media': None,
                        'raw_mistral_response': None,
                        'parsing_error': 'Mistral API failed after retries',
                        'worker_id': worker_id
                    })
                    
            except Exception as e:
                # FAILED EXTRACTION - unexpected error
                results.append({
                    'status': 'FAILED_EXTRACTION',
                    'url': url,
                    'extraction_method': 'unexpected_error',
                    'name': None,
                    'age': None,
                    'gender': None,
                    'phone': None,
                    'description': None,
                    'social_media': None,
                    'raw_mistral_response': None,
                    'parsing_error': str(e),
                    'worker_id': worker_id
                })
        
        return results
    """
    
    print("IMPROVED EXTRACTION LOGIC:")
    print("=" * 50)
    print(improved_logic)
    
    print("\n🎯 KEY IMPROVEMENTS:")
    print("-" * 30)
    print("1. ✅ Capture ALL attempts (success + failed)")
    print("2. 📝 Save raw Mistral responses for manual review")
    print("3. 🔍 Detailed error categorization")
    print("4. 🛠️ Enable manual data recovery")
    print("5. 📊 Complete audit trail")

if __name__ == "__main__":
    output_file = analyze_current_progress()
    print(f"\n📁 Check the example file: {output_file}")
    print("\n" + "="*60)
    create_improved_extraction_logic()
    
    print(f"\n🎯 RECOMMENDATION:")
    print("="*50)
    print("1. Modify the current scraper to capture ALL data")
    print("2. Save both successful and failed extractions")
    print("3. Include raw Mistral responses for manual review")
    print("4. Create separate sheets: SUCCESS, FAILED_PARSING, FAILED_EXTRACTION")
    print("5. Enable manual data recovery from failed parsing cases")
